const parser = require("socket.io-parser");
const decoder = new parser.Decoder();
const axios = require("axios");
const WebSocket = require("ws");

let lastResultSession = 0

decoder.on("decoded", function (packet) {
  var messageType = packet?.data?.[0];
  var messageData = packet?.data?.[1];

  if (messageType == "SOCKET_BO_LAST_RESULT") {
    const [lastResult] = messageData
    if (lastResult.session === lastResultSession) return
    if (lastResult.session % 2 == 0) console.log(lastResult)
    lastResultSession = lastResult.session
  }
});

SOCKET_CHANNEL = {
  BO_PRICE: "BO_PRICE",
  BO_PRICE_SUBSCRIBE: "BO_PRICE_SUBSCRIBE",
  BO_PRICE_UNSUBSCRIBE: "BO_PRICE_UNSUBSCRIBE",
  TRADER_SENTIMENT: "TRADER_SENTIMENT",
  TRADER_SENTIMENT_SUBSCRIBE: "TRADER_SENTIMENT_SUBSCRIBE",
  TRADER_SENTIMENT_UNSUBSCRIBE: "TRADER_SENTIMENT_UNSUBSCRIBE",
  BO_RESULT: "BO_RESULT",
  BO_RESULT_SUBSCRIBE: "BO_RESULT_SUBSCRIBE",
  BO_RESULT_UNSUBSCRIBE: "BO_RESULT_UNSUBSCRIBE",
  SOCKET_BO_LAST_RESULT: "SOCKET_BO_LAST_RESULT",
  SOCKET_BO_LAST_RESULT_SUBSCRIBE: "SOCKET_BO_LAST_RESULT_SUBSCRIBE",
  SOCKET_BO_LAST_RESULT_UNSUBSCRIBE: "SOCKET_BO_LAST_RESULT_UNSUBSCRIBE",
  NEW_LOGIN: "new-login",
  LANGUAGE: "LANGUAGE",
  BO_CHART_INDICATORS: "BO_CHART_INDICATORS",
  DEPOSIT_SUCCESS: "DEPOSIT_SUCCESS",
  INTERNAL_DEPOSIT_SUCCESS: "INTERNAL_DEPOSIT_SUCCESS",
};
var token =
  "eyJhbGciOiJIUzI1NiIsImtpZCI6InBvY2luZXgubmV0IiwidHlwIjoiSldUIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************.DH7OXaz12fBNdQ7rh3QM0gVJg8gN0PGnzFsn06E9mKE";

var uid = 1631256574764; // thay uid bằng uid của bạn


(async () => {
  await start();
})()

async function start() {
  try {
    const params = { uid: uid, ssid: token, EIO: 3, transport: "polling", }
    const response = await axios.get("https://ws.pocinex.net/socket.io/", { params })
    const data = parsePolling(response.data);
    await authen(data.sid);
  } catch (error) {
    console.log("🚀 ~ file: index.js ~ line 47 ~ e", error.message);
  }

}

async function authen(sid) {
  try {
    const params = { uid: uid, ssid: token, EIO: 3, transport: "polling", sid: sid }
    const headers = { cookie: "io=" + sid }
    const response = await axios.get("https://ws.pocinex.net/socket.io/", { params, headers })
    if (response.data === "2:40") await sendPacket(sid);
    else {
      console.log("authenticate failed");
      return false;
    }
  } catch (error) {
    console.log("🚀 ~ file: index.js ~ line 73 ~ e", error.message);
  }
  // return
}

async function sendPacket(sid) {
  try {
    const params = { uid: uid, ssid: token, EIO: 3, transport: "polling", sid: sid }
    const headers = { cookie: "io=" + sid }
    const response = await axios.post("https://ws.pocinex.net/socket.io/", '19:42["LANGUAGE","vi"]15:42["new-login"]24:42["BO_PRICE_SUBSCRIBE"]32:42["TRADER_SENTIMENT_SUBSCRIBE"]37:42["SOCKET_BO_LAST_RESULT_SUBSCRIBE"]25:42["BO_RESULT_SUBSCRIBE"]', { params, headers, })
    if (response.data === "ok") startWebsocket(sid);
    else return false;
  } catch (error) {
    console.log("🚀 ~ file: index.js ~ line 96 ~ e", error.message);
  }
  // return 
}

function parsePolling(str) {
  let char = str.charAt(0);
  if (isNumeric(char) || char === ":") {
    str = str.substring(1);
    return parsePolling(str);
  } else return JSON.parse(str);
}

function isNumeric(n) {
  return !isNaN(parseFloat(n)) && isFinite(n);
}

function startWebsocket(sid) {
  let interval;
  let ws = new WebSocket(
    "wss://ws.pocinex.net/socket.io/?uid=" + uid +
    "&ssid=" + token +
    "&EIO=3&transport=websocket&sid=" + sid
  );

  ws.on("error", function (err) { console.log(err.message); })
    // Successfully
    .on("open", function open() {
      ws.send("2probe");
      console.log("connected");
    })

    .on("close", async function close() {
      console.log("disconnected");
      start();
    })

    .on("message", function incoming(data) {
      // console.log(data)
      if (data === "3probe") {
        ws.send(5);
        interval = setInterval(() => {
          ws.send(2);
        }, 25000);
      } else if (data != "2") {
        decoder.add(data);
      }
    });
}
