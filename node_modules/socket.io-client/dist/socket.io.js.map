{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./build/index.js", "webpack://io/./build/manager.js", "webpack://io/./build/on.js", "webpack://io/./build/socket.js", "webpack://io/./build/typed-events.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/backo2/index.js", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/debug/src/browser.js", "webpack://io/./node_modules/debug/src/common.js", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/ms/index.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./node_modules/socket.io-parser/dist/binary.js", "webpack://io/./node_modules/socket.io-parser/dist/index.js", "webpack://io/./node_modules/socket.io-parser/dist/is-binary.js", "webpack://io/./node_modules/yeast/index.js"], "names": ["Object", "defineProperty", "exports", "value", "io", "Socket", "Manager", "protocol", "url_1", "require", "manager_1", "debug", "module", "lookup", "cache", "managers", "uri", "opts", "undefined", "parsed", "url", "path", "source", "id", "sameNamespace", "newConnection", "forceNew", "multiplex", "query", "query<PERSON><PERSON>", "socket", "socket_io_parser_1", "enumerable", "get", "connect", "manager_2", "socket_1", "eio", "util_1", "parser", "on_1", "Backoff", "typed_events_1", "_a", "nsps", "subs", "installTimerFunctions", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "Encoder", "decoder", "Decoder", "_autoConnect", "autoConnect", "open", "v", "arguments", "length", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "fn", "indexOf", "engine", "self", "skipReconnect", "openSubDestroy", "on", "onopen", "errorSub", "err", "cleanup", "emit<PERSON><PERSON><PERSON><PERSON>", "maybeReconnectOnOpen", "timer", "setTimeoutFn", "close", "emit", "Error", "autoUnref", "unref", "push", "subDestroy", "clearTimeout", "onping", "bind", "ondata", "onerror", "onclose", "ondecoded", "data", "add", "packet", "nsp", "keys", "active", "_close", "encodedPackets", "encode", "i", "write", "options", "for<PERSON>ach", "destroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "StrictEventEmitter", "obj", "ev", "off", "RESERVED_EVENTS", "freeze", "connect_error", "disconnect", "disconnecting", "newListener", "removeListener", "connected", "disconnected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "onpacket", "subEvents", "args", "unshift", "apply", "hasOwnProperty", "type", "PacketType", "EVENT", "compress", "pop", "isTransportWritable", "transport", "writable", "discardPacket", "_packet", "CONNECT", "sid", "onconnect", "onevent", "BINARY_EVENT", "ACK", "onack", "BINARY_ACK", "DISCONNECT", "ondisconnect", "CONNECT_ERROR", "message", "ack", "emitEvent", "_anyListeners", "listeners", "slice", "listener", "sent", "emitBuffered", "splice", "Emitter", "event", "parseuri", "loc", "location", "host", "char<PERSON>t", "test", "port", "ipv6", "href", "ms", "factor", "prototype", "Math", "pow", "rand", "random", "deviation", "floor", "mixin", "key", "addEventListener", "_callbacks", "once", "removeAllListeners", "removeEventListener", "callbacks", "cb", "Array", "len", "hasListeners", "formatArgs", "save", "load", "useColors", "storage", "localstorage", "warned", "console", "warn", "colors", "window", "process", "__nwjs", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "RegExp", "$1", "namespace", "humanize", "diff", "c", "color", "index", "lastC", "replace", "log", "namespaces", "setItem", "removeItem", "error", "r", "getItem", "env", "DEBUG", "localStorage", "formatters", "j", "JSON", "stringify", "setup", "createDebug", "coerce", "disable", "enable", "enabled", "names", "skips", "selectColor", "hash", "charCodeAt", "abs", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "curr", "Number", "Date", "prev", "format", "formatter", "val", "call", "logFn", "extend", "configurable", "set", "init", "delimiter", "newDebug", "split", "substr", "map", "toNamespace", "join", "name", "regexp", "toString", "substring", "stack", "Function", "Transport", "transports", "parseqs", "hostname", "secure", "readyState", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "jsonp", "timestampParam", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "decode", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "offlineEventListener", "onClose", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "e", "shift", "setTransport", "onDrain", "onPacket", "onError", "probe", "failed", "onTransportOpen", "send", "msg", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "l", "onHandshake", "parse", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "onOpen", "clearTimeoutFn", "cleanupAndClose", "waitForUpgrade", "desc", "pingIntervalTimer", "filteredUpgrades", "o", "description", "doOpen", "doClose", "packets", "decodePacket", "binaryType", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "forceJSONP", "Polling", "globalThis", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "onData", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "async", "src", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "area", "iframeId", "className", "position", "top", "left", "target", "method", "setAttribute", "action", "complete", "initIframe", "html", "submit", "attachEvent", "onreadystatechange", "onload", "pick", "empty", "hasXHR2", "responseType", "forceBase64", "supportsBinary", "Request", "req", "request", "pollXhr", "create", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onLoad", "responseText", "status", "requestsCount", "requests", "onSuccess", "fromError", "abort", "XDomainRequest", "enablesXDR", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "yeast", "poll", "onPause", "total", "doPoll", "callback", "decodePayload", "encodePayload", "doWrite", "schema", "timestampRequests", "b64", "nextTick", "isPromiseAvailable", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "product", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "_socket", "onmessage", "lastPacket", "encodePacket", "<PERSON><PERSON><PERSON>", "byteLength", "attr", "reduce", "acc", "k", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "useNativeTimers", "hasCORS", "concat", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "base64decoder", "encodedPacket", "mapBinary", "decodeBase64Packet", "packetType", "decoded", "base64", "Blob", "withNativeBlob", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "SEPARATOR", "String", "fromCharCode", "count", "encodedPayload", "decodedPacket", "chars", "arraybuffer", "bytes", "Uint8Array", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "s", "m", "h", "d", "w", "y", "isFinite", "fmtLong", "fmtShort", "str", "exec", "n", "parseFloat", "msAbs", "round", "plural", "isPlural", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "re", "parts", "b", "authority", "ipv6uri", "pathNames", "regx", "$0", "$2", "reconstructPacket", "deconstructPacket", "is_binary_1", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "isBinary", "placeholder", "_placeholder", "num", "isArray", "newData", "_reconstructPacket", "binary_1", "hasBinary", "encodeAsBinary", "encodeAsString", "deconstruction", "decodeString", "reconstructor", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "finishedReconstruction", "reconPack", "binData", "withNativeFile", "File", "toJSON", "alphabet", "seed", "encoded", "now"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFa;;;;AACbA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,EAAR,GAAaF,OAAO,CAACG,MAAR,GAAiBH,OAAO,CAACI,OAAR,GAAkBJ,OAAO,CAACK,QAAR,GAAmB,KAAK,CAAxE;;AACA,IAAMC,KAAK,GAAGC,mBAAO,CAAC,6BAAD,CAArB;;AACA,IAAMC,SAAS,GAAGD,mBAAO,CAAC,qCAAD,CAAzB;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,kBAAjB,CAAd;AACA;AACA;AACA;;;AACAG,MAAM,CAACV,OAAP,GAAiBA,OAAO,GAAGW,MAA3B;AACA;AACA;AACA;;AACA,IAAMC,KAAK,GAAIZ,OAAO,CAACa,QAAR,GAAmB,EAAlC;;AACA,SAASF,MAAT,CAAgBG,GAAhB,EAAqBC,IAArB,EAA2B;AACvB,MAAI,QAAOD,GAAP,MAAe,QAAnB,EAA6B;AACzBC,QAAI,GAAGD,GAAP;AACAA,OAAG,GAAGE,SAAN;AACH;;AACDD,MAAI,GAAGA,IAAI,IAAI,EAAf;AACA,MAAME,MAAM,GAAG,CAAC,GAAGX,KAAK,CAACY,GAAV,EAAeJ,GAAf,EAAoBC,IAAI,CAACI,IAAL,IAAa,YAAjC,CAAf;AACA,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAtB;AACA,MAAMC,EAAE,GAAGJ,MAAM,CAACI,EAAlB;AACA,MAAMF,IAAI,GAAGF,MAAM,CAACE,IAApB;AACA,MAAMG,aAAa,GAAGV,KAAK,CAACS,EAAD,CAAL,IAAaF,IAAI,IAAIP,KAAK,CAACS,EAAD,CAAL,CAAU,MAAV,CAA3C;AACA,MAAME,aAAa,GAAGR,IAAI,CAACS,QAAL,IAClBT,IAAI,CAAC,sBAAD,CADc,IAElB,UAAUA,IAAI,CAACU,SAFG,IAGlBH,aAHJ;AAIA,MAAIpB,EAAJ;;AACA,MAAIqB,aAAJ,EAAmB;AACfd,SAAK,CAAC,8BAAD,EAAiCW,MAAjC,CAAL;AACAlB,MAAE,GAAG,IAAIM,SAAS,CAACJ,OAAd,CAAsBgB,MAAtB,EAA8BL,IAA9B,CAAL;AACH,GAHD,MAIK;AACD,QAAI,CAACH,KAAK,CAACS,EAAD,CAAV,EAAgB;AACZZ,WAAK,CAAC,wBAAD,EAA2BW,MAA3B,CAAL;AACAR,WAAK,CAACS,EAAD,CAAL,GAAY,IAAIb,SAAS,CAACJ,OAAd,CAAsBgB,MAAtB,EAA8BL,IAA9B,CAAZ;AACH;;AACDb,MAAE,GAAGU,KAAK,CAACS,EAAD,CAAV;AACH;;AACD,MAAIJ,MAAM,CAACS,KAAP,IAAgB,CAACX,IAAI,CAACW,KAA1B,EAAiC;AAC7BX,QAAI,CAACW,KAAL,GAAaT,MAAM,CAACU,QAApB;AACH;;AACD,SAAOzB,EAAE,CAAC0B,MAAH,CAAUX,MAAM,CAACE,IAAjB,EAAuBJ,IAAvB,CAAP;AACH;;AACDf,OAAO,CAACE,EAAR,GAAaS,MAAb;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIkB,kBAAkB,GAAGtB,mBAAO,CAAC,uEAAD,CAAhC;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,UAA/B,EAA2C;AAAE8B,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOF,kBAAkB,CAACxB,QAA1B;AAAqC;AAA5E,CAA3C;AACA;AACA;AACA;AACA;AACA;AACA;;AACAL,OAAO,CAACgC,OAAR,GAAkBrB,MAAlB;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIsB,SAAS,GAAG1B,mBAAO,CAAC,qCAAD,CAAvB;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,SAA/B,EAA0C;AAAE8B,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOE,SAAS,CAAC7B,OAAjB;AAA2B;AAAlE,CAA1C;;AACA,IAAI8B,QAAQ,GAAG3B,mBAAO,CAAC,mCAAD,CAAtB;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,QAA/B,EAAyC;AAAE8B,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOG,QAAQ,CAAC/B,MAAhB;AAAyB;AAAhE,CAAzC;AACAH,OAAO,WAAP,GAAkBW,MAAlB,C;;;;;;;;;;;;ACtEa;;;;;;;;;;;;;;;;;;;;;;;;AACbb,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACI,OAAR,GAAkB,KAAK,CAAvB;;AACA,IAAM+B,GAAG,GAAG5B,mBAAO,CAAC,sEAAD,CAAnB;;AACA,IAAM6B,MAAM,GAAG7B,mBAAO,CAAC,8EAAD,CAAtB;;AACA,IAAM2B,QAAQ,GAAG3B,mBAAO,CAAC,mCAAD,CAAxB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,uEAAD,CAAtB;;AACA,IAAM+B,IAAI,GAAG/B,mBAAO,CAAC,2BAAD,CAApB;;AACA,IAAMgC,OAAO,GAAGhC,mBAAO,CAAC,8CAAD,CAAvB;;AACA,IAAMiC,cAAc,GAAGjC,mBAAO,CAAC,+CAAD,CAA9B;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,0BAAjB,CAAd;;IACMH,O;;;;;AACF,mBAAYU,GAAZ,EAAiBC,IAAjB,EAAuB;AAAA;;AAAA;;AACnB,QAAI0B,EAAJ;;AACA;AACA,UAAKC,IAAL,GAAY,EAAZ;AACA,UAAKC,IAAL,GAAY,EAAZ;;AACA,QAAI7B,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;AAChCC,UAAI,GAAGD,GAAP;AACAA,SAAG,GAAGE,SAAN;AACH;;AACDD,QAAI,GAAGA,IAAI,IAAI,EAAf;AACAA,QAAI,CAACI,IAAL,GAAYJ,IAAI,CAACI,IAAL,IAAa,YAAzB;AACA,UAAKJ,IAAL,GAAYA,IAAZ;AACA,KAAC,GAAGqB,MAAM,CAACQ,qBAAX,iCAAwC7B,IAAxC;;AACA,UAAK8B,YAAL,CAAkB9B,IAAI,CAAC8B,YAAL,KAAsB,KAAxC;;AACA,UAAKC,oBAAL,CAA0B/B,IAAI,CAAC+B,oBAAL,IAA6BC,QAAvD;;AACA,UAAKC,iBAAL,CAAuBjC,IAAI,CAACiC,iBAAL,IAA0B,IAAjD;;AACA,UAAKC,oBAAL,CAA0BlC,IAAI,CAACkC,oBAAL,IAA6B,IAAvD;;AACA,UAAKC,mBAAL,CAAyB,CAACT,EAAE,GAAG1B,IAAI,CAACmC,mBAAX,MAAoC,IAApC,IAA4CT,EAAE,KAAK,KAAK,CAAxD,GAA4DA,EAA5D,GAAiE,GAA1F;;AACA,UAAKU,OAAL,GAAe,IAAIZ,OAAJ,CAAY;AACvBa,SAAG,EAAE,MAAKJ,iBAAL,EADkB;AAEvBK,SAAG,EAAE,MAAKJ,oBAAL,EAFkB;AAGvBK,YAAM,EAAE,MAAKJ,mBAAL;AAHe,KAAZ,CAAf;;AAKA,UAAKK,OAAL,CAAa,QAAQxC,IAAI,CAACwC,OAAb,GAAuB,KAAvB,GAA+BxC,IAAI,CAACwC,OAAjD;;AACA,UAAKC,WAAL,GAAmB,QAAnB;AACA,UAAK1C,GAAL,GAAWA,GAAX;;AACA,QAAM2C,OAAO,GAAG1C,IAAI,CAACsB,MAAL,IAAeA,MAA/B;;AACA,UAAKqB,OAAL,GAAe,IAAID,OAAO,CAACE,OAAZ,EAAf;AACA,UAAKC,OAAL,GAAe,IAAIH,OAAO,CAACI,OAAZ,EAAf;AACA,UAAKC,YAAL,GAAoB/C,IAAI,CAACgD,WAAL,KAAqB,KAAzC;AACA,QAAI,MAAKD,YAAT,EACI,MAAKE,IAAL;AA/Be;AAgCtB;;;;WACD,sBAAaC,CAAb,EAAgB;AACZ,UAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKC,aAAZ;AACJ,WAAKA,aAAL,GAAqB,CAAC,CAACH,CAAvB;AACA,aAAO,IAAP;AACH;;;WACD,8BAAqBA,CAArB,EAAwB;AACpB,UAAIA,CAAC,KAAKjD,SAAV,EACI,OAAO,KAAKqD,qBAAZ;AACJ,WAAKA,qBAAL,GAA6BJ,CAA7B;AACA,aAAO,IAAP;AACH;;;WACD,2BAAkBA,CAAlB,EAAqB;AACjB,UAAIxB,EAAJ;;AACA,UAAIwB,CAAC,KAAKjD,SAAV,EACI,OAAO,KAAKsD,kBAAZ;AACJ,WAAKA,kBAAL,GAA0BL,CAA1B;AACA,OAACxB,EAAE,GAAG,KAAKU,OAAX,MAAwB,IAAxB,IAAgCV,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAAC8B,MAAH,CAAUN,CAAV,CAAzD;AACA,aAAO,IAAP;AACH;;;WACD,6BAAoBA,CAApB,EAAuB;AACnB,UAAIxB,EAAJ;;AACA,UAAIwB,CAAC,KAAKjD,SAAV,EACI,OAAO,KAAKwD,oBAAZ;AACJ,WAAKA,oBAAL,GAA4BP,CAA5B;AACA,OAACxB,EAAE,GAAG,KAAKU,OAAX,MAAwB,IAAxB,IAAgCV,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACgC,SAAH,CAAaR,CAAb,CAAzD;AACA,aAAO,IAAP;AACH;;;WACD,8BAAqBA,CAArB,EAAwB;AACpB,UAAIxB,EAAJ;;AACA,UAAIwB,CAAC,KAAKjD,SAAV,EACI,OAAO,KAAK0D,qBAAZ;AACJ,WAAKA,qBAAL,GAA6BT,CAA7B;AACA,OAACxB,EAAE,GAAG,KAAKU,OAAX,MAAwB,IAAxB,IAAgCV,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACkC,MAAH,CAAUV,CAAV,CAAzD;AACA,aAAO,IAAP;AACH;;;WACD,iBAAQA,CAAR,EAAW;AACP,UAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKS,QAAZ;AACJ,WAAKA,QAAL,GAAgBX,CAAhB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,gCAAuB;AACnB;AACA,UAAI,CAAC,KAAKY,aAAN,IACA,KAAKT,aADL,IAEA,KAAKjB,OAAL,CAAa2B,QAAb,KAA0B,CAF9B,EAEiC;AAC7B;AACA,aAAKC,SAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,cAAKC,EAAL,EAAS;AAAA;;AACLvE,WAAK,CAAC,eAAD,EAAkB,KAAK+C,WAAvB,CAAL;AACA,UAAI,CAAC,KAAKA,WAAL,CAAiByB,OAAjB,CAAyB,MAAzB,CAAL,EACI,OAAO,IAAP;AACJxE,WAAK,CAAC,YAAD,EAAe,KAAKK,GAApB,CAAL;AACA,WAAKoE,MAAL,GAAc/C,GAAG,CAAC,KAAKrB,GAAN,EAAW,KAAKC,IAAhB,CAAjB;AACA,UAAMa,MAAM,GAAG,KAAKsD,MAApB;AACA,UAAMC,IAAI,GAAG,IAAb;AACA,WAAK3B,WAAL,GAAmB,SAAnB;AACA,WAAK4B,aAAL,GAAqB,KAArB,CATK,CAUL;;AACA,UAAMC,cAAc,GAAG,CAAC,GAAG/C,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,MAArB,EAA6B,YAAY;AAC5DuD,YAAI,CAACI,MAAL;AACAP,UAAE,IAAIA,EAAE,EAAR;AACH,OAHsB,CAAvB,CAXK,CAeL;;AACA,UAAMQ,QAAQ,GAAG,CAAC,GAAGlD,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,OAArB,EAA8B,UAAC6D,GAAD,EAAS;AACpDhF,aAAK,CAAC,OAAD,CAAL;AACA0E,YAAI,CAACO,OAAL;AACAP,YAAI,CAAC3B,WAAL,GAAmB,QAAnB;;AACA,cAAI,CAACmC,YAAL,CAAkB,OAAlB,EAA2BF,GAA3B;;AACA,YAAIT,EAAJ,EAAQ;AACJA,YAAE,CAACS,GAAD,CAAF;AACH,SAFD,MAGK;AACD;AACAN,cAAI,CAACS,oBAAL;AACH;AACJ,OAZgB,CAAjB;;AAaA,UAAI,UAAU,KAAKhB,QAAnB,EAA6B;AACzB,YAAMrB,OAAO,GAAG,KAAKqB,QAArB;AACAnE,aAAK,CAAC,uCAAD,EAA0C8C,OAA1C,CAAL;;AACA,YAAIA,OAAO,KAAK,CAAhB,EAAmB;AACf8B,wBAAc,GADC,CACG;AACrB,SALwB,CAMzB;;;AACA,YAAMQ,KAAK,GAAG,KAAKC,YAAL,CAAkB,YAAM;AAClCrF,eAAK,CAAC,oCAAD,EAAuC8C,OAAvC,CAAL;AACA8B,wBAAc;AACdzD,gBAAM,CAACmE,KAAP;AACAnE,gBAAM,CAACoE,IAAP,CAAY,OAAZ,EAAqB,IAAIC,KAAJ,CAAU,SAAV,CAArB;AACH,SALa,EAKX1C,OALW,CAAd;;AAMA,YAAI,KAAKxC,IAAL,CAAUmF,SAAd,EAAyB;AACrBL,eAAK,CAACM,KAAN;AACH;;AACD,aAAKxD,IAAL,CAAUyD,IAAV,CAAe,SAASC,UAAT,GAAsB;AACjCC,sBAAY,CAACT,KAAD,CAAZ;AACH,SAFD;AAGH;;AACD,WAAKlD,IAAL,CAAUyD,IAAV,CAAef,cAAf;AACA,WAAK1C,IAAL,CAAUyD,IAAV,CAAeZ,QAAf;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQR,EAAR,EAAY;AACR,aAAO,KAAKhB,IAAL,CAAUgB,EAAV,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,kBAAS;AACLvE,WAAK,CAAC,MAAD,CAAL,CADK,CAEL;;AACA,WAAKiF,OAAL,GAHK,CAIL;;AACA,WAAKlC,WAAL,GAAmB,MAAnB;AACA,WAAKmC,YAAL,CAAkB,MAAlB,EANK,CAOL;;AACA,UAAM/D,MAAM,GAAG,KAAKsD,MAApB;AACA,WAAKvC,IAAL,CAAUyD,IAAV,CAAe,CAAC,GAAG9D,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,MAArB,EAA6B,KAAK2E,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAA7B,CAAf,EAAqE,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,MAArB,EAA6B,KAAK6E,MAAL,CAAYD,IAAZ,CAAiB,IAAjB,CAA7B,CAArE,EAA2H,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,OAArB,EAA8B,KAAK8E,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAA9B,CAA3H,EAAmL,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAa1D,MAAb,EAAqB,OAArB,EAA8B,KAAK+E,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAA9B,CAAnL,EAA2O,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAa,KAAK1B,OAAlB,EAA2B,SAA3B,EAAsC,KAAKgD,SAAL,CAAeJ,IAAf,CAAoB,IAApB,CAAtC,CAA3O;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,kBAAS;AACL,WAAKb,YAAL,CAAkB,MAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,gBAAOkB,IAAP,EAAa;AACT,WAAKjD,OAAL,CAAakD,GAAb,CAAiBD,IAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,mBAAUE,MAAV,EAAkB;AACd,WAAKpB,YAAL,CAAkB,QAAlB,EAA4BoB,MAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,iBAAQtB,GAAR,EAAa;AACThF,WAAK,CAAC,OAAD,EAAUgF,GAAV,CAAL;AACA,WAAKE,YAAL,CAAkB,OAAlB,EAA2BF,GAA3B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,gBAAOuB,GAAP,EAAYjG,IAAZ,EAAkB;AACd,UAAIa,MAAM,GAAG,KAAKc,IAAL,CAAUsE,GAAV,CAAb;;AACA,UAAI,CAACpF,MAAL,EAAa;AACTA,cAAM,GAAG,IAAIM,QAAQ,CAAC/B,MAAb,CAAoB,IAApB,EAA0B6G,GAA1B,EAA+BjG,IAA/B,CAAT;AACA,aAAK2B,IAAL,CAAUsE,GAAV,IAAiBpF,MAAjB;AACH;;AACD,aAAOA,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,kBAASA,MAAT,EAAiB;AACb,UAAMc,IAAI,GAAG5C,MAAM,CAACmH,IAAP,CAAY,KAAKvE,IAAjB,CAAb;;AACA,+BAAkBA,IAAlB,2BAAwB;AAAnB,YAAMsE,GAAG,YAAT;AACD,YAAMpF,OAAM,GAAG,KAAKc,IAAL,CAAUsE,GAAV,CAAf;;AACA,YAAIpF,OAAM,CAACsF,MAAX,EAAmB;AACfzG,eAAK,CAAC,2CAAD,EAA8CuG,GAA9C,CAAL;AACA;AACH;AACJ;;AACD,WAAKG,MAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQJ,MAAR,EAAgB;AACZtG,WAAK,CAAC,mBAAD,EAAsBsG,MAAtB,CAAL;AACA,UAAMK,cAAc,GAAG,KAAK1D,OAAL,CAAa2D,MAAb,CAAoBN,MAApB,CAAvB;;AACA,WAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAACjD,MAAnC,EAA2CmD,CAAC,EAA5C,EAAgD;AAC5C,aAAKpC,MAAL,CAAYqC,KAAZ,CAAkBH,cAAc,CAACE,CAAD,CAAhC,EAAqCP,MAAM,CAACS,OAA5C;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;WACI,mBAAU;AACN/G,WAAK,CAAC,SAAD,CAAL;AACA,WAAKkC,IAAL,CAAU8E,OAAV,CAAkB,UAACpB,UAAD;AAAA,eAAgBA,UAAU,EAA1B;AAAA,OAAlB;AACA,WAAK1D,IAAL,CAAUwB,MAAV,GAAmB,CAAnB;AACA,WAAKP,OAAL,CAAa8D,OAAb;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,kBAAS;AACLjH,WAAK,CAAC,YAAD,CAAL;AACA,WAAK2E,aAAL,GAAqB,IAArB;AACA,WAAKP,aAAL,GAAqB,KAArB;;AACA,UAAI,cAAc,KAAKrB,WAAvB,EAAoC;AAChC;AACA;AACA,aAAKkC,OAAL;AACH;;AACD,WAAKvC,OAAL,CAAawE,KAAb;AACA,WAAKnE,WAAL,GAAmB,QAAnB;AACA,UAAI,KAAK0B,MAAT,EACI,KAAKA,MAAL,CAAYa,KAAZ;AACP;AACD;AACJ;AACA;AACA;AACA;;;;WACI,sBAAa;AACT,aAAO,KAAKoB,MAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,iBAAQS,MAAR,EAAgB;AACZnH,WAAK,CAAC,SAAD,CAAL;AACA,WAAKiF,OAAL;AACA,WAAKvC,OAAL,CAAawE,KAAb;AACA,WAAKnE,WAAL,GAAmB,QAAnB;AACA,WAAKmC,YAAL,CAAkB,OAAlB,EAA2BiC,MAA3B;;AACA,UAAI,KAAKxD,aAAL,IAAsB,CAAC,KAAKgB,aAAhC,EAA+C;AAC3C,aAAKL,SAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;WACI,qBAAY;AAAA;;AACR,UAAI,KAAKF,aAAL,IAAsB,KAAKO,aAA/B,EACI,OAAO,IAAP;AACJ,UAAMD,IAAI,GAAG,IAAb;;AACA,UAAI,KAAKhC,OAAL,CAAa2B,QAAb,IAAyB,KAAKT,qBAAlC,EAAyD;AACrD5D,aAAK,CAAC,kBAAD,CAAL;AACA,aAAK0C,OAAL,CAAawE,KAAb;AACA,aAAKhC,YAAL,CAAkB,kBAAlB;AACA,aAAKd,aAAL,GAAqB,KAArB;AACH,OALD,MAMK;AACD,YAAMgD,KAAK,GAAG,KAAK1E,OAAL,CAAa2E,QAAb,EAAd;AACArH,aAAK,CAAC,yCAAD,EAA4CoH,KAA5C,CAAL;AACA,aAAKhD,aAAL,GAAqB,IAArB;AACA,YAAMgB,KAAK,GAAG,KAAKC,YAAL,CAAkB,YAAM;AAClC,cAAIX,IAAI,CAACC,aAAT,EACI;AACJ3E,eAAK,CAAC,sBAAD,CAAL;;AACA,gBAAI,CAACkF,YAAL,CAAkB,mBAAlB,EAAuCR,IAAI,CAAChC,OAAL,CAAa2B,QAApD,EAJkC,CAKlC;;;AACA,cAAIK,IAAI,CAACC,aAAT,EACI;AACJD,cAAI,CAACnB,IAAL,CAAU,UAACyB,GAAD,EAAS;AACf,gBAAIA,GAAJ,EAAS;AACLhF,mBAAK,CAAC,yBAAD,CAAL;AACA0E,kBAAI,CAACN,aAAL,GAAqB,KAArB;AACAM,kBAAI,CAACJ,SAAL;;AACA,oBAAI,CAACY,YAAL,CAAkB,iBAAlB,EAAqCF,GAArC;AACH,aALD,MAMK;AACDhF,mBAAK,CAAC,mBAAD,CAAL;AACA0E,kBAAI,CAAC4C,WAAL;AACH;AACJ,WAXD;AAYH,SApBa,EAoBXF,KApBW,CAAd;;AAqBA,YAAI,KAAK9G,IAAL,CAAUmF,SAAd,EAAyB;AACrBL,eAAK,CAACM,KAAN;AACH;;AACD,aAAKxD,IAAL,CAAUyD,IAAV,CAAe,SAASC,UAAT,GAAsB;AACjCC,sBAAY,CAACT,KAAD,CAAZ;AACH,SAFD;AAGH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;WACI,uBAAc;AACV,UAAMmC,OAAO,GAAG,KAAK7E,OAAL,CAAa2B,QAA7B;AACA,WAAKD,aAAL,GAAqB,KAArB;AACA,WAAK1B,OAAL,CAAawE,KAAb;AACA,WAAKhC,YAAL,CAAkB,WAAlB,EAA+BqC,OAA/B;AACH;;;;EA5WiBxF,cAAc,CAACyF,kB;;AA8WrCjI,OAAO,CAACI,OAAR,GAAkBA,OAAlB,C;;;;;;;;;;;;ACzXa;;AACbN,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACsF,EAAR,GAAa,KAAK,CAAlB;;AACA,SAASA,EAAT,CAAY4C,GAAZ,EAAiBC,EAAjB,EAAqBnD,EAArB,EAAyB;AACrBkD,KAAG,CAAC5C,EAAJ,CAAO6C,EAAP,EAAWnD,EAAX;AACA,SAAO,SAASqB,UAAT,GAAsB;AACzB6B,OAAG,CAACE,GAAJ,CAAQD,EAAR,EAAYnD,EAAZ;AACH,GAFD;AAGH;;AACDhF,OAAO,CAACsF,EAAR,GAAaA,EAAb,C;;;;;;;;;;;;ACTa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACbxF,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACG,MAAR,GAAiB,KAAK,CAAtB;;AACA,IAAM0B,kBAAkB,GAAGtB,mBAAO,CAAC,uEAAD,CAAlC;;AACA,IAAM+B,IAAI,GAAG/B,mBAAO,CAAC,2BAAD,CAApB;;AACA,IAAMiC,cAAc,GAAGjC,mBAAO,CAAC,+CAAD,CAA9B;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,yBAAjB,CAAd;AACA;AACA;AACA;AACA;;;AACA,IAAM8H,eAAe,GAAGvI,MAAM,CAACwI,MAAP,CAAc;AAClCtG,SAAO,EAAE,CADyB;AAElCuG,eAAa,EAAE,CAFmB;AAGlCC,YAAU,EAAE,CAHsB;AAIlCC,eAAa,EAAE,CAJmB;AAKlC;AACAC,aAAW,EAAE,CANqB;AAOlCC,gBAAc,EAAE;AAPkB,CAAd,CAAxB;;IASMxI,M;;;;;AACF;AACJ;AACA;AACA;AACA;AACI,kBAAYD,EAAZ,EAAgB8G,GAAhB,EAAqBjG,IAArB,EAA2B;AAAA;;AAAA;;AACvB;AACA,UAAK6H,SAAL,GAAiB,KAAjB;AACA,UAAKC,YAAL,GAAoB,IAApB;AACA,UAAKC,aAAL,GAAqB,EAArB;AACA,UAAKC,UAAL,GAAkB,EAAlB;AACA,UAAKC,GAAL,GAAW,CAAX;AACA,UAAKC,IAAL,GAAY,EAAZ;AACA,UAAKC,KAAL,GAAa,EAAb;AACA,UAAKhJ,EAAL,GAAUA,EAAV;AACA,UAAK8G,GAAL,GAAWA,GAAX;;AACA,QAAIjG,IAAI,IAAIA,IAAI,CAACoI,IAAjB,EAAuB;AACnB,YAAKA,IAAL,GAAYpI,IAAI,CAACoI,IAAjB;AACH;;AACD,QAAI,MAAKjJ,EAAL,CAAQ4D,YAAZ,EACI,MAAKE,IAAL;AAfmB;AAgB1B;AACD;AACJ;AACA;AACA;AACA;;;;;WACI,qBAAY;AACR,UAAI,KAAKrB,IAAT,EACI;AACJ,UAAMzC,EAAE,GAAG,KAAKA,EAAhB;AACA,WAAKyC,IAAL,GAAY,CACR,CAAC,GAAGL,IAAI,CAACgD,EAAT,EAAapF,EAAb,EAAiB,MAAjB,EAAyB,KAAKqF,MAAL,CAAYiB,IAAZ,CAAiB,IAAjB,CAAzB,CADQ,EAER,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAapF,EAAb,EAAiB,QAAjB,EAA2B,KAAKkJ,QAAL,CAAc5C,IAAd,CAAmB,IAAnB,CAA3B,CAFQ,EAGR,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAapF,EAAb,EAAiB,OAAjB,EAA0B,KAAKwG,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAA1B,CAHQ,EAIR,CAAC,GAAGlE,IAAI,CAACgD,EAAT,EAAapF,EAAb,EAAiB,OAAjB,EAA0B,KAAKyG,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAA1B,CAJQ,CAAZ;AAMH;AACD;AACJ;AACA;;;;SACI,eAAa;AACT,aAAO,CAAC,CAAC,KAAK7D,IAAd;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,mBAAU;AACN,UAAI,KAAKiG,SAAT,EACI,OAAO,IAAP;AACJ,WAAKS,SAAL;AACA,UAAI,CAAC,KAAKnJ,EAAL,CAAQ,eAAR,CAAL,EACI,KAAKA,EAAL,CAAQ8D,IAAR,GALE,CAKc;;AACpB,UAAI,WAAW,KAAK9D,EAAL,CAAQsD,WAAvB,EACI,KAAK+B,MAAL;AACJ,aAAO,IAAP;AACH;AACD;AACJ;AACA;;;;WACI,gBAAO;AACH,aAAO,KAAKvD,OAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,gBAAc;AAAA,wCAANsH,IAAM;AAANA,YAAM;AAAA;;AACVA,UAAI,CAACC,OAAL,CAAa,SAAb;AACA,WAAKvD,IAAL,CAAUwD,KAAV,CAAgB,IAAhB,EAAsBF,IAAtB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,cAAKnB,EAAL,EAAkB;AACd,UAAIE,eAAe,CAACoB,cAAhB,CAA+BtB,EAA/B,CAAJ,EAAwC;AACpC,cAAM,IAAIlC,KAAJ,CAAU,MAAMkC,EAAN,GAAW,4BAArB,CAAN;AACH;;AAHa,yCAANmB,IAAM;AAANA,YAAM;AAAA;;AAIdA,UAAI,CAACC,OAAL,CAAapB,EAAb;AACA,UAAMpB,MAAM,GAAG;AACX2C,YAAI,EAAE7H,kBAAkB,CAAC8H,UAAnB,CAA8BC,KADzB;AAEX/C,YAAI,EAAEyC;AAFK,OAAf;AAIAvC,YAAM,CAACS,OAAP,GAAiB,EAAjB;AACAT,YAAM,CAACS,OAAP,CAAeqC,QAAf,GAA0B,KAAKX,KAAL,CAAWW,QAAX,KAAwB,KAAlD,CAVc,CAWd;;AACA,UAAI,eAAe,OAAOP,IAAI,CAACA,IAAI,CAACnF,MAAL,GAAc,CAAf,CAA9B,EAAiD;AAC7C1D,aAAK,CAAC,gCAAD,EAAmC,KAAKuI,GAAxC,CAAL;AACA,aAAKC,IAAL,CAAU,KAAKD,GAAf,IAAsBM,IAAI,CAACQ,GAAL,EAAtB;AACA/C,cAAM,CAAC1F,EAAP,GAAY,KAAK2H,GAAL,EAAZ;AACH;;AACD,UAAMe,mBAAmB,GAAG,KAAK7J,EAAL,CAAQgF,MAAR,IACxB,KAAKhF,EAAL,CAAQgF,MAAR,CAAe8E,SADS,IAExB,KAAK9J,EAAL,CAAQgF,MAAR,CAAe8E,SAAf,CAAyBC,QAF7B;AAGA,UAAMC,aAAa,GAAG,KAAKhB,KAAL,iBAAwB,CAACa,mBAAD,IAAwB,CAAC,KAAKnB,SAAtD,CAAtB;;AACA,UAAIsB,aAAJ,EAAmB;AACfzJ,aAAK,CAAC,2DAAD,CAAL;AACH,OAFD,MAGK,IAAI,KAAKmI,SAAT,EAAoB;AACrB,aAAK7B,MAAL,CAAYA,MAAZ;AACH,OAFI,MAGA;AACD,aAAKgC,UAAL,CAAgB3C,IAAhB,CAAqBW,MAArB;AACH;;AACD,WAAKmC,KAAL,GAAa,EAAb;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,gBAAOnC,OAAP,EAAe;AACXA,aAAM,CAACC,GAAP,GAAa,KAAKA,GAAlB;;AACA,WAAK9G,EAAL,CAAQiK,OAAR,CAAgBpD,OAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,kBAAS;AAAA;;AACLtG,WAAK,CAAC,gCAAD,CAAL;;AACA,UAAI,OAAO,KAAK0I,IAAZ,IAAoB,UAAxB,EAAoC;AAChC,aAAKA,IAAL,CAAU,UAACtC,IAAD,EAAU;AAChB,gBAAI,CAACE,MAAL,CAAY;AAAE2C,gBAAI,EAAE7H,kBAAkB,CAAC8H,UAAnB,CAA8BS,OAAtC;AAA+CvD,gBAAI,EAAJA;AAA/C,WAAZ;AACH,SAFD;AAGH,OAJD,MAKK;AACD,aAAKE,MAAL,CAAY;AAAE2C,cAAI,EAAE7H,kBAAkB,CAAC8H,UAAnB,CAA8BS,OAAtC;AAA+CvD,cAAI,EAAE,KAAKsC;AAA1D,SAAZ;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQ1D,GAAR,EAAa;AACT,UAAI,CAAC,KAAKmD,SAAV,EAAqB;AACjB,aAAKjD,YAAL,CAAkB,eAAlB,EAAmCF,GAAnC;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQmC,MAAR,EAAgB;AACZnH,WAAK,CAAC,YAAD,EAAemH,MAAf,CAAL;AACA,WAAKgB,SAAL,GAAiB,KAAjB;AACA,WAAKC,YAAL,GAAoB,IAApB;AACA,aAAO,KAAKxH,EAAZ;AACA,WAAKsE,YAAL,CAAkB,YAAlB,EAAgCiC,MAAhC;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,kBAASb,MAAT,EAAiB;AACb,UAAMzF,aAAa,GAAGyF,MAAM,CAACC,GAAP,KAAe,KAAKA,GAA1C;AACA,UAAI,CAAC1F,aAAL,EACI;;AACJ,cAAQyF,MAAM,CAAC2C,IAAf;AACI,aAAK7H,kBAAkB,CAAC8H,UAAnB,CAA8BS,OAAnC;AACI,cAAIrD,MAAM,CAACF,IAAP,IAAeE,MAAM,CAACF,IAAP,CAAYwD,GAA/B,EAAoC;AAChC,gBAAMhJ,EAAE,GAAG0F,MAAM,CAACF,IAAP,CAAYwD,GAAvB;AACA,iBAAKC,SAAL,CAAejJ,EAAf;AACH,WAHD,MAIK;AACD,iBAAKsE,YAAL,CAAkB,eAAlB,EAAmC,IAAIM,KAAJ,CAAU,2LAAV,CAAnC;AACH;;AACD;;AACJ,aAAKpE,kBAAkB,CAAC8H,UAAnB,CAA8BC,KAAnC;AACI,eAAKW,OAAL,CAAaxD,MAAb;AACA;;AACJ,aAAKlF,kBAAkB,CAAC8H,UAAnB,CAA8Ba,YAAnC;AACI,eAAKD,OAAL,CAAaxD,MAAb;AACA;;AACJ,aAAKlF,kBAAkB,CAAC8H,UAAnB,CAA8Bc,GAAnC;AACI,eAAKC,KAAL,CAAW3D,MAAX;AACA;;AACJ,aAAKlF,kBAAkB,CAAC8H,UAAnB,CAA8BgB,UAAnC;AACI,eAAKD,KAAL,CAAW3D,MAAX;AACA;;AACJ,aAAKlF,kBAAkB,CAAC8H,UAAnB,CAA8BiB,UAAnC;AACI,eAAKC,YAAL;AACA;;AACJ,aAAKhJ,kBAAkB,CAAC8H,UAAnB,CAA8BmB,aAAnC;AACI,cAAMrF,GAAG,GAAG,IAAIQ,KAAJ,CAAUc,MAAM,CAACF,IAAP,CAAYkE,OAAtB,CAAZ,CADJ,CAEI;;AACAtF,aAAG,CAACoB,IAAJ,GAAWE,MAAM,CAACF,IAAP,CAAYA,IAAvB;AACA,eAAKlB,YAAL,CAAkB,eAAlB,EAAmCF,GAAnC;AACA;AA9BR;AAgCH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQsB,MAAR,EAAgB;AACZ,UAAMuC,IAAI,GAAGvC,MAAM,CAACF,IAAP,IAAe,EAA5B;AACApG,WAAK,CAAC,mBAAD,EAAsB6I,IAAtB,CAAL;;AACA,UAAI,QAAQvC,MAAM,CAAC1F,EAAnB,EAAuB;AACnBZ,aAAK,CAAC,iCAAD,CAAL;AACA6I,YAAI,CAAClD,IAAL,CAAU,KAAK4E,GAAL,CAASjE,MAAM,CAAC1F,EAAhB,CAAV;AACH;;AACD,UAAI,KAAKuH,SAAT,EAAoB;AAChB,aAAKqC,SAAL,CAAe3B,IAAf;AACH,OAFD,MAGK;AACD,aAAKR,aAAL,CAAmB1C,IAAnB,CAAwBtG,MAAM,CAACwI,MAAP,CAAcgB,IAAd,CAAxB;AACH;AACJ;;;WACD,mBAAUA,IAAV,EAAgB;AACZ,UAAI,KAAK4B,aAAL,IAAsB,KAAKA,aAAL,CAAmB/G,MAA7C,EAAqD;AACjD,YAAMgH,SAAS,GAAG,KAAKD,aAAL,CAAmBE,KAAnB,EAAlB;;AADiD,mDAE1BD,SAF0B;AAAA;;AAAA;AAEjD,8DAAkC;AAAA,gBAAvBE,QAAuB;AAC9BA,oBAAQ,CAAC7B,KAAT,CAAe,IAAf,EAAqBF,IAArB;AACH;AAJgD;AAAA;AAAA;AAAA;AAAA;AAKpD;;AACD,4DAAWE,KAAX,CAAiB,IAAjB,EAAuBF,IAAvB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,aAAIjI,EAAJ,EAAQ;AACJ,UAAM8D,IAAI,GAAG,IAAb;AACA,UAAImG,IAAI,GAAG,KAAX;AACA,aAAO,YAAmB;AACtB;AACA,YAAIA,IAAJ,EACI;AACJA,YAAI,GAAG,IAAP;;AAJsB,2CAANhC,IAAM;AAANA,cAAM;AAAA;;AAKtB7I,aAAK,CAAC,gBAAD,EAAmB6I,IAAnB,CAAL;AACAnE,YAAI,CAAC4B,MAAL,CAAY;AACR2C,cAAI,EAAE7H,kBAAkB,CAAC8H,UAAnB,CAA8Bc,GAD5B;AAERpJ,YAAE,EAAEA,EAFI;AAGRwF,cAAI,EAAEyC;AAHE,SAAZ;AAKH,OAXD;AAYH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,eAAMvC,MAAN,EAAc;AACV,UAAMiE,GAAG,GAAG,KAAK/B,IAAL,CAAUlC,MAAM,CAAC1F,EAAjB,CAAZ;;AACA,UAAI,eAAe,OAAO2J,GAA1B,EAA+B;AAC3BvK,aAAK,CAAC,wBAAD,EAA2BsG,MAAM,CAAC1F,EAAlC,EAAsC0F,MAAM,CAACF,IAA7C,CAAL;AACAmE,WAAG,CAACxB,KAAJ,CAAU,IAAV,EAAgBzC,MAAM,CAACF,IAAvB;AACA,eAAO,KAAKoC,IAAL,CAAUlC,MAAM,CAAC1F,EAAjB,CAAP;AACH,OAJD,MAKK;AACDZ,aAAK,CAAC,YAAD,EAAesG,MAAM,CAAC1F,EAAtB,CAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;WACI,mBAAUA,EAAV,EAAc;AACVZ,WAAK,CAAC,6BAAD,EAAgCY,EAAhC,CAAL;AACA,WAAKA,EAAL,GAAUA,EAAV;AACA,WAAKuH,SAAL,GAAiB,IAAjB;AACA,WAAKC,YAAL,GAAoB,KAApB;AACA,WAAK0C,YAAL;AACA,WAAK5F,YAAL,CAAkB,SAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,wBAAe;AAAA;;AACX,WAAKmD,aAAL,CAAmBrB,OAAnB,CAA2B,UAAC6B,IAAD;AAAA,eAAU,MAAI,CAAC2B,SAAL,CAAe3B,IAAf,CAAV;AAAA,OAA3B;AACA,WAAKR,aAAL,GAAqB,EAArB;AACA,WAAKC,UAAL,CAAgBtB,OAAhB,CAAwB,UAACV,MAAD;AAAA,eAAY,MAAI,CAACA,MAAL,CAAYA,MAAZ,CAAZ;AAAA,OAAxB;AACA,WAAKgC,UAAL,GAAkB,EAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,wBAAe;AACXtI,WAAK,CAAC,wBAAD,EAA2B,KAAKuG,GAAhC,CAAL;AACA,WAAKU,OAAL;AACA,WAAKf,OAAL,CAAa,sBAAb;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,mBAAU;AACN,UAAI,KAAKhE,IAAT,EAAe;AACX;AACA,aAAKA,IAAL,CAAU8E,OAAV,CAAkB,UAACpB,UAAD;AAAA,iBAAgBA,UAAU,EAA1B;AAAA,SAAlB;AACA,aAAK1D,IAAL,GAAY3B,SAAZ;AACH;;AACD,WAAKd,EAAL,CAAQ,UAAR,EAAoB,IAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,sBAAa;AACT,UAAI,KAAK0I,SAAT,EAAoB;AAChBnI,aAAK,CAAC,4BAAD,EAA+B,KAAKuG,GAApC,CAAL;AACA,aAAKD,MAAL,CAAY;AAAE2C,cAAI,EAAE7H,kBAAkB,CAAC8H,UAAnB,CAA8BiB;AAAtC,SAAZ;AACH,OAJQ,CAKT;;;AACA,WAAKlD,OAAL;;AACA,UAAI,KAAKkB,SAAT,EAAoB;AAChB;AACA,aAAKjC,OAAL,CAAa,sBAAb;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,iBAAQ;AACJ,aAAO,KAAK6B,UAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,kBAASqB,SAAT,EAAmB;AACf,WAAKX,KAAL,CAAWW,QAAX,GAAsBA,SAAtB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;SACI,eAAe;AACX,WAAKX,KAAL,eAAsB,IAAtB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,eAAMmC,QAAN,EAAgB;AACZ,WAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;AACA,WAAKA,aAAL,CAAmB9E,IAAnB,CAAwBiF,QAAxB;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;WACI,oBAAWA,QAAX,EAAqB;AACjB,WAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;AACA,WAAKA,aAAL,CAAmB3B,OAAnB,CAA2B8B,QAA3B;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,gBAAOA,QAAP,EAAiB;AACb,UAAI,CAAC,KAAKH,aAAV,EAAyB;AACrB,eAAO,IAAP;AACH;;AACD,UAAIG,QAAJ,EAAc;AACV,YAAMF,SAAS,GAAG,KAAKD,aAAvB;;AACA,aAAK,IAAI5D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6D,SAAS,CAAChH,MAA9B,EAAsCmD,CAAC,EAAvC,EAA2C;AACvC,cAAI+D,QAAQ,KAAKF,SAAS,CAAC7D,CAAD,CAA1B,EAA+B;AAC3B6D,qBAAS,CAACK,MAAV,CAAiBlE,CAAjB,EAAoB,CAApB;AACA,mBAAO,IAAP;AACH;AACJ;AACJ,OARD,MASK;AACD,aAAK4D,aAAL,GAAqB,EAArB;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,wBAAe;AACX,aAAO,KAAKA,aAAL,IAAsB,EAA7B;AACH;;;;EAhbgB1I,cAAc,CAACyF,kB;;AAkbpCjI,OAAO,CAACG,MAAR,GAAiBA,MAAjB,C;;;;;;;;;;;;ACtca;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACbL,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACiI,kBAAR,GAA6B,KAAK,CAAlC;;AACA,IAAMwD,OAAO,GAAGlL,mBAAO,CAAC,oEAAD,CAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACM0H,kB;;;;;;;;;;;;;;AACF;AACJ;AACA;AACA;AACA;AACA;AACI,gBAAGE,EAAH,EAAOkD,QAAP,EAAiB;AACb,iFAASlD,EAAT,EAAakD,QAAb;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,cAAKlD,EAAL,EAASkD,QAAT,EAAmB;AACf,mFAAWlD,EAAX,EAAekD,QAAf;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,cAAKlD,EAAL,EAAkB;AAAA;;AAAA,wCAANmB,IAAM;AAANA,YAAM;AAAA;;AACd,2GAAWnB,EAAX,SAAkBmB,IAAlB;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;WACI,sBAAanB,EAAb,EAA0B;AAAA;;AAAA,yCAANmB,IAAM;AAANA,YAAM;AAAA;;AACtB,2GAAWnB,EAAX,SAAkBmB,IAAlB;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,mBAAUoC,KAAV,EAAiB;AACb,+FAAuBA,KAAvB;AACH;;;;EApD4BD,O;;AAsDjCzL,OAAO,CAACiI,kBAAR,GAA6BA,kBAA7B,C;;;;;;;;;;;;ACvEa;;AACbnI,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACkB,GAAR,GAAc,KAAK,CAAnB;;AACA,IAAMyK,QAAQ,GAAGpL,mBAAO,CAAC,kDAAD,CAAxB;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,sBAAjB,CAAd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASW,GAAT,CAAaJ,GAAb,EAAkC;AAAA,MAAhBK,IAAgB,uEAAT,EAAS;AAAA,MAALyK,GAAK;AAC9B,MAAI1D,GAAG,GAAGpH,GAAV,CAD8B,CAE9B;;AACA8K,KAAG,GAAGA,GAAG,IAAK,OAAOC,QAAP,KAAoB,WAApB,IAAmCA,QAAjD;AACA,MAAI,QAAQ/K,GAAZ,EACIA,GAAG,GAAG8K,GAAG,CAACvL,QAAJ,GAAe,IAAf,GAAsBuL,GAAG,CAACE,IAAhC,CAL0B,CAM9B;;AACA,MAAI,OAAOhL,GAAP,KAAe,QAAnB,EAA6B;AACzB,QAAI,QAAQA,GAAG,CAACiL,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACvB,UAAI,QAAQjL,GAAG,CAACiL,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACvBjL,WAAG,GAAG8K,GAAG,CAACvL,QAAJ,GAAeS,GAArB;AACH,OAFD,MAGK;AACDA,WAAG,GAAG8K,GAAG,CAACE,IAAJ,GAAWhL,GAAjB;AACH;AACJ;;AACD,QAAI,CAAC,sBAAsBkL,IAAtB,CAA2BlL,GAA3B,CAAL,EAAsC;AAClCL,WAAK,CAAC,sBAAD,EAAyBK,GAAzB,CAAL;;AACA,UAAI,gBAAgB,OAAO8K,GAA3B,EAAgC;AAC5B9K,WAAG,GAAG8K,GAAG,CAACvL,QAAJ,GAAe,IAAf,GAAsBS,GAA5B;AACH,OAFD,MAGK;AACDA,WAAG,GAAG,aAAaA,GAAnB;AACH;AACJ,KAjBwB,CAkBzB;;;AACAL,SAAK,CAAC,UAAD,EAAaK,GAAb,CAAL;AACAoH,OAAG,GAAGyD,QAAQ,CAAC7K,GAAD,CAAd;AACH,GA5B6B,CA6B9B;;;AACA,MAAI,CAACoH,GAAG,CAAC+D,IAAT,EAAe;AACX,QAAI,cAAcD,IAAd,CAAmB9D,GAAG,CAAC7H,QAAvB,CAAJ,EAAsC;AAClC6H,SAAG,CAAC+D,IAAJ,GAAW,IAAX;AACH,KAFD,MAGK,IAAI,eAAeD,IAAf,CAAoB9D,GAAG,CAAC7H,QAAxB,CAAJ,EAAuC;AACxC6H,SAAG,CAAC+D,IAAJ,GAAW,KAAX;AACH;AACJ;;AACD/D,KAAG,CAAC/G,IAAJ,GAAW+G,GAAG,CAAC/G,IAAJ,IAAY,GAAvB;AACA,MAAM+K,IAAI,GAAGhE,GAAG,CAAC4D,IAAJ,CAAS7G,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAAxC;AACA,MAAM6G,IAAI,GAAGI,IAAI,GAAG,MAAMhE,GAAG,CAAC4D,IAAV,GAAiB,GAApB,GAA0B5D,GAAG,CAAC4D,IAA/C,CAxC8B,CAyC9B;;AACA5D,KAAG,CAAC7G,EAAJ,GAAS6G,GAAG,CAAC7H,QAAJ,GAAe,KAAf,GAAuByL,IAAvB,GAA8B,GAA9B,GAAoC5D,GAAG,CAAC+D,IAAxC,GAA+C9K,IAAxD,CA1C8B,CA2C9B;;AACA+G,KAAG,CAACiE,IAAJ,GACIjE,GAAG,CAAC7H,QAAJ,GACI,KADJ,GAEIyL,IAFJ,IAGKF,GAAG,IAAIA,GAAG,CAACK,IAAJ,KAAa/D,GAAG,CAAC+D,IAAxB,GAA+B,EAA/B,GAAoC,MAAM/D,GAAG,CAAC+D,IAHnD,CADJ;AAKA,SAAO/D,GAAP;AACH;;AACDlI,OAAO,CAACkB,GAAR,GAAcA,GAAd,C;;;;;;;;;;;AChEA;AACA;AACA;AAEAR,MAAM,CAACV,OAAP,GAAiBuC,OAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAT,CAAiBxB,IAAjB,EAAuB;AACrBA,MAAI,GAAGA,IAAI,IAAI,EAAf;AACA,OAAKqL,EAAL,GAAUrL,IAAI,CAACqC,GAAL,IAAY,GAAtB;AACA,OAAKC,GAAL,GAAWtC,IAAI,CAACsC,GAAL,IAAY,KAAvB;AACA,OAAKgJ,MAAL,GAActL,IAAI,CAACsL,MAAL,IAAe,CAA7B;AACA,OAAK/I,MAAL,GAAcvC,IAAI,CAACuC,MAAL,GAAc,CAAd,IAAmBvC,IAAI,CAACuC,MAAL,IAAe,CAAlC,GAAsCvC,IAAI,CAACuC,MAA3C,GAAoD,CAAlE;AACA,OAAKwB,QAAL,GAAgB,CAAhB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AAEAvC,OAAO,CAAC+J,SAAR,CAAkBxE,QAAlB,GAA6B,YAAU;AACrC,MAAIsE,EAAE,GAAG,KAAKA,EAAL,GAAUG,IAAI,CAACC,GAAL,CAAS,KAAKH,MAAd,EAAsB,KAAKvH,QAAL,EAAtB,CAAnB;;AACA,MAAI,KAAKxB,MAAT,EAAiB;AACf,QAAImJ,IAAI,GAAIF,IAAI,CAACG,MAAL,EAAZ;AACA,QAAIC,SAAS,GAAGJ,IAAI,CAACK,KAAL,CAAWH,IAAI,GAAG,KAAKnJ,MAAZ,GAAqB8I,EAAhC,CAAhB;AACAA,MAAE,GAAG,CAACG,IAAI,CAACK,KAAL,CAAWH,IAAI,GAAG,EAAlB,IAAwB,CAAzB,KAA+B,CAA/B,GAAoCL,EAAE,GAAGO,SAAzC,GAAqDP,EAAE,GAAGO,SAA/D;AACD;;AACD,SAAOJ,IAAI,CAACnJ,GAAL,CAASgJ,EAAT,EAAa,KAAK/I,GAAlB,IAAyB,CAAhC;AACD,CARD;AAUA;AACA;AACA;AACA;AACA;;;AAEAd,OAAO,CAAC+J,SAAR,CAAkB3E,KAAlB,GAA0B,YAAU;AAClC,OAAK7C,QAAL,GAAgB,CAAhB;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEAvC,OAAO,CAAC+J,SAAR,CAAkB/H,MAAlB,GAA2B,UAASnB,GAAT,EAAa;AACtC,OAAKgJ,EAAL,GAAUhJ,GAAV;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEAb,OAAO,CAAC+J,SAAR,CAAkB3H,MAAlB,GAA2B,UAAStB,GAAT,EAAa;AACtC,OAAKA,GAAL,GAAWA,GAAX;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEAd,OAAO,CAAC+J,SAAR,CAAkB7H,SAAlB,GAA8B,UAASnB,MAAT,EAAgB;AAC5C,OAAKA,MAAL,GAAcA,MAAd;AACD,CAFD,C;;;;;;;;;;;AChFA;AACA;AACA;AAEA,IAAI,IAAJ,EAAmC;AACjC5C,QAAM,CAACV,OAAP,GAAiByL,OAAjB;AACD;AAED;AACA;AACA;AACA;AACA;;;AAEA,SAASA,OAAT,CAAiBvD,GAAjB,EAAsB;AACpB,MAAIA,GAAJ,EAAS,OAAO2E,KAAK,CAAC3E,GAAD,CAAZ;AACV;;AAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS2E,KAAT,CAAe3E,GAAf,EAAoB;AAClB,OAAK,IAAI4E,GAAT,IAAgBrB,OAAO,CAACa,SAAxB,EAAmC;AACjCpE,OAAG,CAAC4E,GAAD,CAAH,GAAWrB,OAAO,CAACa,SAAR,CAAkBQ,GAAlB,CAAX;AACD;;AACD,SAAO5E,GAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAuD,OAAO,CAACa,SAAR,CAAkBhH,EAAlB,GACAmG,OAAO,CAACa,SAAR,CAAkBS,gBAAlB,GAAqC,UAASrB,KAAT,EAAgB1G,EAAhB,EAAmB;AACtD,OAAKgI,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AACA,GAAC,KAAKA,UAAL,CAAgB,MAAMtB,KAAtB,IAA+B,KAAKsB,UAAL,CAAgB,MAAMtB,KAAtB,KAAgC,EAAhE,EACGtF,IADH,CACQpB,EADR;AAEA,SAAO,IAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAyG,OAAO,CAACa,SAAR,CAAkBW,IAAlB,GAAyB,UAASvB,KAAT,EAAgB1G,EAAhB,EAAmB;AAC1C,WAASM,EAAT,GAAc;AACZ,SAAK8C,GAAL,CAASsD,KAAT,EAAgBpG,EAAhB;AACAN,MAAE,CAACwE,KAAH,CAAS,IAAT,EAAetF,SAAf;AACD;;AAEDoB,IAAE,CAACN,EAAH,GAAQA,EAAR;AACA,OAAKM,EAAL,CAAQoG,KAAR,EAAepG,EAAf;AACA,SAAO,IAAP;AACD,CATD;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAmG,OAAO,CAACa,SAAR,CAAkBlE,GAAlB,GACAqD,OAAO,CAACa,SAAR,CAAkB3D,cAAlB,GACA8C,OAAO,CAACa,SAAR,CAAkBY,kBAAlB,GACAzB,OAAO,CAACa,SAAR,CAAkBa,mBAAlB,GAAwC,UAASzB,KAAT,EAAgB1G,EAAhB,EAAmB;AACzD,OAAKgI,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC,CADyD,CAGzD;;AACA,MAAI,KAAK9I,SAAS,CAACC,MAAnB,EAA2B;AACzB,SAAK6I,UAAL,GAAkB,EAAlB;AACA,WAAO,IAAP;AACD,GAPwD,CASzD;;;AACA,MAAII,SAAS,GAAG,KAAKJ,UAAL,CAAgB,MAAMtB,KAAtB,CAAhB;AACA,MAAI,CAAC0B,SAAL,EAAgB,OAAO,IAAP,CAXyC,CAazD;;AACA,MAAI,KAAKlJ,SAAS,CAACC,MAAnB,EAA2B;AACzB,WAAO,KAAK6I,UAAL,CAAgB,MAAMtB,KAAtB,CAAP;AACA,WAAO,IAAP;AACD,GAjBwD,CAmBzD;;;AACA,MAAI2B,EAAJ;;AACA,OAAK,IAAI/F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8F,SAAS,CAACjJ,MAA9B,EAAsCmD,CAAC,EAAvC,EAA2C;AACzC+F,MAAE,GAAGD,SAAS,CAAC9F,CAAD,CAAd;;AACA,QAAI+F,EAAE,KAAKrI,EAAP,IAAaqI,EAAE,CAACrI,EAAH,KAAUA,EAA3B,EAA+B;AAC7BoI,eAAS,CAAC5B,MAAV,CAAiBlE,CAAjB,EAAoB,CAApB;AACA;AACD;AACF,GA3BwD,CA6BzD;AACA;;;AACA,MAAI8F,SAAS,CAACjJ,MAAV,KAAqB,CAAzB,EAA4B;AAC1B,WAAO,KAAK6I,UAAL,CAAgB,MAAMtB,KAAtB,CAAP;AACD;;AAED,SAAO,IAAP;AACD,CAvCD;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAD,OAAO,CAACa,SAAR,CAAkBtG,IAAlB,GAAyB,UAAS0F,KAAT,EAAe;AACtC,OAAKsB,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AAEA,MAAI1D,IAAI,GAAG,IAAIgE,KAAJ,CAAUpJ,SAAS,CAACC,MAAV,GAAmB,CAA7B,CAAX;AAAA,MACIiJ,SAAS,GAAG,KAAKJ,UAAL,CAAgB,MAAMtB,KAAtB,CADhB;;AAGA,OAAK,IAAIpE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpD,SAAS,CAACC,MAA9B,EAAsCmD,CAAC,EAAvC,EAA2C;AACzCgC,QAAI,CAAChC,CAAC,GAAG,CAAL,CAAJ,GAAcpD,SAAS,CAACoD,CAAD,CAAvB;AACD;;AAED,MAAI8F,SAAJ,EAAe;AACbA,aAAS,GAAGA,SAAS,CAAChC,KAAV,CAAgB,CAAhB,CAAZ;;AACA,SAAK,IAAI9D,CAAC,GAAG,CAAR,EAAWiG,GAAG,GAAGH,SAAS,CAACjJ,MAAhC,EAAwCmD,CAAC,GAAGiG,GAA5C,EAAiD,EAAEjG,CAAnD,EAAsD;AACpD8F,eAAS,CAAC9F,CAAD,CAAT,CAAakC,KAAb,CAAmB,IAAnB,EAAyBF,IAAzB;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAlBD;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAmC,OAAO,CAACa,SAAR,CAAkBnB,SAAlB,GAA8B,UAASO,KAAT,EAAe;AAC3C,OAAKsB,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AACA,SAAO,KAAKA,UAAL,CAAgB,MAAMtB,KAAtB,KAAgC,EAAvC;AACD,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAD,OAAO,CAACa,SAAR,CAAkBkB,YAAlB,GAAiC,UAAS9B,KAAT,EAAe;AAC9C,SAAO,CAAC,CAAE,KAAKP,SAAL,CAAeO,KAAf,EAAsBvH,MAAhC;AACD,CAFD,C;;;;;;;;;;;AC5KA;;AAEA;AACA;AACA;AAEAnE,OAAO,CAACyN,UAAR,GAAqBA,UAArB;AACAzN,OAAO,CAAC0N,IAAR,GAAeA,IAAf;AACA1N,OAAO,CAAC2N,IAAR,GAAeA,IAAf;AACA3N,OAAO,CAAC4N,SAAR,GAAoBA,SAApB;AACA5N,OAAO,CAAC6N,OAAR,GAAkBC,YAAY,EAA9B;;AACA9N,OAAO,CAAC0H,OAAR,GAAmB,YAAM;AACxB,MAAIqG,MAAM,GAAG,KAAb;AAEA,SAAO,YAAM;AACZ,QAAI,CAACA,MAAL,EAAa;AACZA,YAAM,GAAG,IAAT;AACAC,aAAO,CAACC,IAAR,CAAa,uIAAb;AACA;AACD,GALD;AAMA,CATiB,EAAlB;AAWA;AACA;AACA;;;AAEAjO,OAAO,CAACkO,MAAR,GAAiB,CAChB,SADgB,EAEhB,SAFgB,EAGhB,SAHgB,EAIhB,SAJgB,EAKhB,SALgB,EAMhB,SANgB,EAOhB,SAPgB,EAQhB,SARgB,EAShB,SATgB,EAUhB,SAVgB,EAWhB,SAXgB,EAYhB,SAZgB,EAahB,SAbgB,EAchB,SAdgB,EAehB,SAfgB,EAgBhB,SAhBgB,EAiBhB,SAjBgB,EAkBhB,SAlBgB,EAmBhB,SAnBgB,EAoBhB,SApBgB,EAqBhB,SArBgB,EAsBhB,SAtBgB,EAuBhB,SAvBgB,EAwBhB,SAxBgB,EAyBhB,SAzBgB,EA0BhB,SA1BgB,EA2BhB,SA3BgB,EA4BhB,SA5BgB,EA6BhB,SA7BgB,EA8BhB,SA9BgB,EA+BhB,SA/BgB,EAgChB,SAhCgB,EAiChB,SAjCgB,EAkChB,SAlCgB,EAmChB,SAnCgB,EAoChB,SApCgB,EAqChB,SArCgB,EAsChB,SAtCgB,EAuChB,SAvCgB,EAwChB,SAxCgB,EAyChB,SAzCgB,EA0ChB,SA1CgB,EA2ChB,SA3CgB,EA4ChB,SA5CgB,EA6ChB,SA7CgB,EA8ChB,SA9CgB,EA+ChB,SA/CgB,EAgDhB,SAhDgB,EAiDhB,SAjDgB,EAkDhB,SAlDgB,EAmDhB,SAnDgB,EAoDhB,SApDgB,EAqDhB,SArDgB,EAsDhB,SAtDgB,EAuDhB,SAvDgB,EAwDhB,SAxDgB,EAyDhB,SAzDgB,EA0DhB,SA1DgB,EA2DhB,SA3DgB,EA4DhB,SA5DgB,EA6DhB,SA7DgB,EA8DhB,SA9DgB,EA+DhB,SA/DgB,EAgEhB,SAhEgB,EAiEhB,SAjEgB,EAkEhB,SAlEgB,EAmEhB,SAnEgB,EAoEhB,SApEgB,EAqEhB,SArEgB,EAsEhB,SAtEgB,EAuEhB,SAvEgB,EAwEhB,SAxEgB,EAyEhB,SAzEgB,EA0EhB,SA1EgB,EA2EhB,SA3EgB,EA4EhB,SA5EgB,CAAjB;AA+EA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,SAASN,SAAT,GAAqB;AACpB;AACA;AACA;AACA,MAAI,OAAOO,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,OAAxC,KAAoDD,MAAM,CAACC,OAAP,CAAe1E,IAAf,KAAwB,UAAxB,IAAsCyE,MAAM,CAACC,OAAP,CAAeC,MAAzG,CAAJ,EAAsH;AACrH,WAAO,IAAP;AACA,GANmB,CAQpB;;;AACA,MAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,uBAAxC,CAA/D,EAAiI;AAChI,WAAO,KAAP;AACA,GAXmB,CAapB;AACA;;;AACA,SAAQ,OAAOC,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACC,eAA5C,IAA+DD,QAAQ,CAACC,eAAT,CAAyBC,KAAxF,IAAiGF,QAAQ,CAACC,eAAT,CAAyBC,KAAzB,CAA+BC,gBAAjI,IAEL,OAAOV,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACH,OAAxC,KAAoDG,MAAM,CAACH,OAAP,CAAec,OAAf,IAA2BX,MAAM,CAACH,OAAP,CAAee,SAAf,IAA4BZ,MAAM,CAACH,OAAP,CAAegB,KAA1H,CAFK,IAKL,OAAOV,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,gBAAxC,CAA3D,IAAwHQ,QAAQ,CAACC,MAAM,CAACC,EAAR,EAAY,EAAZ,CAAR,IAA2B,EAL9I,IAOL,OAAOb,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,oBAAxC,CAP7D;AAQA;AAED;AACA;AACA;AACA;AACA;;;AAEA,SAAShB,UAAT,CAAoBnE,IAApB,EAA0B;AACzBA,MAAI,CAAC,CAAD,CAAJ,GAAU,CAAC,KAAKsE,SAAL,GAAiB,IAAjB,GAAwB,EAAzB,IACT,KAAKwB,SADI,IAER,KAAKxB,SAAL,GAAiB,KAAjB,GAAyB,GAFjB,IAGTtE,IAAI,CAAC,CAAD,CAHK,IAIR,KAAKsE,SAAL,GAAiB,KAAjB,GAAyB,GAJjB,IAKT,GALS,GAKHlN,MAAM,CAACV,OAAP,CAAeqP,QAAf,CAAwB,KAAKC,IAA7B,CALP;;AAOA,MAAI,CAAC,KAAK1B,SAAV,EAAqB;AACpB;AACA;;AAED,MAAM2B,CAAC,GAAG,YAAY,KAAKC,KAA3B;AACAlG,MAAI,CAACkC,MAAL,CAAY,CAAZ,EAAe,CAAf,EAAkB+D,CAAlB,EAAqB,gBAArB,EAbyB,CAezB;AACA;AACA;;AACA,MAAIE,KAAK,GAAG,CAAZ;AACA,MAAIC,KAAK,GAAG,CAAZ;AACApG,MAAI,CAAC,CAAD,CAAJ,CAAQqG,OAAR,CAAgB,aAAhB,EAA+B,UAAAlB,KAAK,EAAI;AACvC,QAAIA,KAAK,KAAK,IAAd,EAAoB;AACnB;AACA;;AACDgB,SAAK;;AACL,QAAIhB,KAAK,KAAK,IAAd,EAAoB;AACnB;AACA;AACAiB,WAAK,GAAGD,KAAR;AACA;AACD,GAVD;AAYAnG,MAAI,CAACkC,MAAL,CAAYkE,KAAZ,EAAmB,CAAnB,EAAsBH,CAAtB;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAvP,OAAO,CAAC4P,GAAR,GAAc5B,OAAO,CAACvN,KAAR,IAAiBuN,OAAO,CAAC4B,GAAzB,IAAiC,YAAM,CAAE,CAAvD;AAEA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASlC,IAAT,CAAcmC,UAAd,EAA0B;AACzB,MAAI;AACH,QAAIA,UAAJ,EAAgB;AACf7P,aAAO,CAAC6N,OAAR,CAAgBiC,OAAhB,CAAwB,OAAxB,EAAiCD,UAAjC;AACA,KAFD,MAEO;AACN7P,aAAO,CAAC6N,OAAR,CAAgBkC,UAAhB,CAA2B,OAA3B;AACA;AACD,GAND,CAME,OAAOC,KAAP,EAAc,CACf;AACA;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASrC,IAAT,GAAgB;AACf,MAAIsC,CAAJ;;AACA,MAAI;AACHA,KAAC,GAAGjQ,OAAO,CAAC6N,OAAR,CAAgBqC,OAAhB,CAAwB,OAAxB,CAAJ;AACA,GAFD,CAEE,OAAOF,KAAP,EAAc,CACf;AACA;AACA,GAPc,CASf;;;AACA,MAAI,CAACC,CAAD,IAAM,OAAO7B,OAAP,KAAmB,WAAzB,IAAwC,SAASA,OAArD,EAA8D;AAC7D6B,KAAC,GAAG7B,OAAO,CAAC+B,GAAR,CAAYC,KAAhB;AACA;;AAED,SAAOH,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASnC,YAAT,GAAwB;AACvB,MAAI;AACH;AACA;AACA,WAAOuC,YAAP;AACA,GAJD,CAIE,OAAOL,KAAP,EAAc,CACf;AACA;AACA;AACD;;AAEDtP,MAAM,CAACV,OAAP,GAAiBO,mBAAO,CAAC,oDAAD,CAAP,CAAoBP,OAApB,CAAjB;AAEA,IAAOsQ,UAAP,GAAqB5P,MAAM,CAACV,OAA5B,CAAOsQ,UAAP;AAEA;AACA;AACA;;AAEAA,UAAU,CAACC,CAAX,GAAe,UAAUtM,CAAV,EAAa;AAC3B,MAAI;AACH,WAAOuM,IAAI,CAACC,SAAL,CAAexM,CAAf,CAAP;AACA,GAFD,CAEE,OAAO+L,KAAP,EAAc;AACf,WAAO,iCAAiCA,KAAK,CAACjF,OAA9C;AACA;AACD,CAND,C;;;;;;;;;;;;;;;;;;;;;;;ACrQA;AACA;AACA;AACA;AAEA,SAAS2F,KAAT,CAAeP,GAAf,EAAoB;AACnBQ,aAAW,CAAClQ,KAAZ,GAAoBkQ,WAApB;AACAA,aAAW,WAAX,GAAsBA,WAAtB;AACAA,aAAW,CAACC,MAAZ,GAAqBA,MAArB;AACAD,aAAW,CAACE,OAAZ,GAAsBA,OAAtB;AACAF,aAAW,CAACG,MAAZ,GAAqBA,MAArB;AACAH,aAAW,CAACI,OAAZ,GAAsBA,OAAtB;AACAJ,aAAW,CAACtB,QAAZ,GAAuB9O,mBAAO,CAAC,sCAAD,CAA9B;AACAoQ,aAAW,CAACjJ,OAAZ,GAAsBA,OAAtB;AAEA5H,QAAM,CAACmH,IAAP,CAAYkJ,GAAZ,EAAiB1I,OAAjB,CAAyB,UAAAqF,GAAG,EAAI;AAC/B6D,eAAW,CAAC7D,GAAD,CAAX,GAAmBqD,GAAG,CAACrD,GAAD,CAAtB;AACA,GAFD;AAIA;AACD;AACA;;AAEC6D,aAAW,CAACK,KAAZ,GAAoB,EAApB;AACAL,aAAW,CAACM,KAAZ,GAAoB,EAApB;AAEA;AACD;AACA;AACA;AACA;;AACCN,aAAW,CAACL,UAAZ,GAAyB,EAAzB;AAEA;AACD;AACA;AACA;AACA;AACA;;AACC,WAASY,WAAT,CAAqB9B,SAArB,EAAgC;AAC/B,QAAI+B,IAAI,GAAG,CAAX;;AAEA,SAAK,IAAI7J,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8H,SAAS,CAACjL,MAA9B,EAAsCmD,CAAC,EAAvC,EAA2C;AAC1C6J,UAAI,GAAI,CAACA,IAAI,IAAI,CAAT,IAAcA,IAAf,GAAuB/B,SAAS,CAACgC,UAAV,CAAqB9J,CAArB,CAA9B;AACA6J,UAAI,IAAI,CAAR,CAF0C,CAE/B;AACX;;AAED,WAAOR,WAAW,CAACzC,MAAZ,CAAmB3B,IAAI,CAAC8E,GAAL,CAASF,IAAT,IAAiBR,WAAW,CAACzC,MAAZ,CAAmB/J,MAAvD,CAAP;AACA;;AACDwM,aAAW,CAACO,WAAZ,GAA0BA,WAA1B;AAEA;AACD;AACA;AACA;AACA;AACA;AACA;;AACC,WAASP,WAAT,CAAqBvB,SAArB,EAAgC;AAC/B,QAAIkC,QAAJ;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIC,eAAJ;AACA,QAAIC,YAAJ;;AAEA,aAAShR,KAAT,GAAwB;AAAA,wCAAN6I,IAAM;AAANA,YAAM;AAAA;;AACvB;AACA,UAAI,CAAC7I,KAAK,CAACsQ,OAAX,EAAoB;AACnB;AACA;;AAED,UAAM5L,IAAI,GAAG1E,KAAb,CANuB,CAQvB;;AACA,UAAMiR,IAAI,GAAGC,MAAM,CAAC,IAAIC,IAAJ,EAAD,CAAnB;AACA,UAAMxF,EAAE,GAAGsF,IAAI,IAAIJ,QAAQ,IAAII,IAAhB,CAAf;AACAvM,UAAI,CAACmK,IAAL,GAAYlD,EAAZ;AACAjH,UAAI,CAAC0M,IAAL,GAAYP,QAAZ;AACAnM,UAAI,CAACuM,IAAL,GAAYA,IAAZ;AACAJ,cAAQ,GAAGI,IAAX;AAEApI,UAAI,CAAC,CAAD,CAAJ,GAAUqH,WAAW,CAACC,MAAZ,CAAmBtH,IAAI,CAAC,CAAD,CAAvB,CAAV;;AAEA,UAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AAChC;AACAA,YAAI,CAACC,OAAL,CAAa,IAAb;AACA,OArBsB,CAuBvB;;;AACA,UAAIkG,KAAK,GAAG,CAAZ;AACAnG,UAAI,CAAC,CAAD,CAAJ,GAAUA,IAAI,CAAC,CAAD,CAAJ,CAAQqG,OAAR,CAAgB,eAAhB,EAAiC,UAAClB,KAAD,EAAQqD,MAAR,EAAmB;AAC7D;AACA,YAAIrD,KAAK,KAAK,IAAd,EAAoB;AACnB,iBAAO,GAAP;AACA;;AACDgB,aAAK;AACL,YAAMsC,SAAS,GAAGpB,WAAW,CAACL,UAAZ,CAAuBwB,MAAvB,CAAlB;;AACA,YAAI,OAAOC,SAAP,KAAqB,UAAzB,EAAqC;AACpC,cAAMC,GAAG,GAAG1I,IAAI,CAACmG,KAAD,CAAhB;AACAhB,eAAK,GAAGsD,SAAS,CAACE,IAAV,CAAe9M,IAAf,EAAqB6M,GAArB,CAAR,CAFoC,CAIpC;;AACA1I,cAAI,CAACkC,MAAL,CAAYiE,KAAZ,EAAmB,CAAnB;AACAA,eAAK;AACL;;AACD,eAAOhB,KAAP;AACA,OAhBS,CAAV,CAzBuB,CA2CvB;;AACAkC,iBAAW,CAAClD,UAAZ,CAAuBwE,IAAvB,CAA4B9M,IAA5B,EAAkCmE,IAAlC;AAEA,UAAM4I,KAAK,GAAG/M,IAAI,CAACyK,GAAL,IAAYe,WAAW,CAACf,GAAtC;AACAsC,WAAK,CAAC1I,KAAN,CAAYrE,IAAZ,EAAkBmE,IAAlB;AACA;;AAED7I,SAAK,CAAC2O,SAAN,GAAkBA,SAAlB;AACA3O,SAAK,CAACmN,SAAN,GAAkB+C,WAAW,CAAC/C,SAAZ,EAAlB;AACAnN,SAAK,CAAC+O,KAAN,GAAcmB,WAAW,CAACO,WAAZ,CAAwB9B,SAAxB,CAAd;AACA3O,SAAK,CAAC0R,MAAN,GAAeA,MAAf;AACA1R,SAAK,CAACiH,OAAN,GAAgBiJ,WAAW,CAACjJ,OAA5B,CA5D+B,CA4DM;;AAErC5H,UAAM,CAACC,cAAP,CAAsBU,KAAtB,EAA6B,SAA7B,EAAwC;AACvCqB,gBAAU,EAAE,IAD2B;AAEvCsQ,kBAAY,EAAE,KAFyB;AAGvCrQ,SAAG,EAAE,eAAM;AACV,YAAIwP,cAAc,KAAK,IAAvB,EAA6B;AAC5B,iBAAOA,cAAP;AACA;;AACD,YAAIC,eAAe,KAAKb,WAAW,CAACd,UAApC,EAAgD;AAC/C2B,yBAAe,GAAGb,WAAW,CAACd,UAA9B;AACA4B,sBAAY,GAAGd,WAAW,CAACI,OAAZ,CAAoB3B,SAApB,CAAf;AACA;;AAED,eAAOqC,YAAP;AACA,OAbsC;AAcvCY,SAAG,EAAE,aAAApO,CAAC,EAAI;AACTsN,sBAAc,GAAGtN,CAAjB;AACA;AAhBsC,KAAxC,EA9D+B,CAiF/B;;AACA,QAAI,OAAO0M,WAAW,CAAC2B,IAAnB,KAA4B,UAAhC,EAA4C;AAC3C3B,iBAAW,CAAC2B,IAAZ,CAAiB7R,KAAjB;AACA;;AAED,WAAOA,KAAP;AACA;;AAED,WAAS0R,MAAT,CAAgB/C,SAAhB,EAA2BmD,SAA3B,EAAsC;AACrC,QAAMC,QAAQ,GAAG7B,WAAW,CAAC,KAAKvB,SAAL,IAAkB,OAAOmD,SAAP,KAAqB,WAArB,GAAmC,GAAnC,GAAyCA,SAA3D,IAAwEnD,SAAzE,CAA5B;AACAoD,YAAQ,CAAC5C,GAAT,GAAe,KAAKA,GAApB;AACA,WAAO4C,QAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAAS1B,MAAT,CAAgBjB,UAAhB,EAA4B;AAC3Bc,eAAW,CAACjD,IAAZ,CAAiBmC,UAAjB;AACAc,eAAW,CAACd,UAAZ,GAAyBA,UAAzB;AAEAc,eAAW,CAACK,KAAZ,GAAoB,EAApB;AACAL,eAAW,CAACM,KAAZ,GAAoB,EAApB;AAEA,QAAI3J,CAAJ;AACA,QAAMmL,KAAK,GAAG,CAAC,OAAO5C,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8C,EAA/C,EAAmD4C,KAAnD,CAAyD,QAAzD,CAAd;AACA,QAAMlF,GAAG,GAAGkF,KAAK,CAACtO,MAAlB;;AAEA,SAAKmD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGiG,GAAhB,EAAqBjG,CAAC,EAAtB,EAA0B;AACzB,UAAI,CAACmL,KAAK,CAACnL,CAAD,CAAV,EAAe;AACd;AACA;AACA;;AAEDuI,gBAAU,GAAG4C,KAAK,CAACnL,CAAD,CAAL,CAASqI,OAAT,CAAiB,KAAjB,EAAwB,KAAxB,CAAb;;AAEA,UAAIE,UAAU,CAAC,CAAD,CAAV,KAAkB,GAAtB,EAA2B;AAC1Bc,mBAAW,CAACM,KAAZ,CAAkB7K,IAAlB,CAAuB,IAAI8I,MAAJ,CAAW,MAAMW,UAAU,CAAC6C,MAAX,CAAkB,CAAlB,CAAN,GAA6B,GAAxC,CAAvB;AACA,OAFD,MAEO;AACN/B,mBAAW,CAACK,KAAZ,CAAkB5K,IAAlB,CAAuB,IAAI8I,MAAJ,CAAW,MAAMW,UAAN,GAAmB,GAA9B,CAAvB;AACA;AACD;AACD;AAED;AACD;AACA;AACA;AACA;AACA;;;AACC,WAASgB,OAAT,GAAmB;AAClB,QAAMhB,UAAU,GAAG,6BACfc,WAAW,CAACK,KAAZ,CAAkB2B,GAAlB,CAAsBC,WAAtB,CADe,sBAEfjC,WAAW,CAACM,KAAZ,CAAkB0B,GAAlB,CAAsBC,WAAtB,EAAmCD,GAAnC,CAAuC,UAAAvD,SAAS;AAAA,aAAI,MAAMA,SAAV;AAAA,KAAhD,CAFe,GAGjByD,IAHiB,CAGZ,GAHY,CAAnB;AAIAlC,eAAW,CAACG,MAAZ,CAAmB,EAAnB;AACA,WAAOjB,UAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASkB,OAAT,CAAiB+B,IAAjB,EAAuB;AACtB,QAAIA,IAAI,CAACA,IAAI,CAAC3O,MAAL,GAAc,CAAf,CAAJ,KAA0B,GAA9B,EAAmC;AAClC,aAAO,IAAP;AACA;;AAED,QAAImD,CAAJ;AACA,QAAIiG,GAAJ;;AAEA,SAAKjG,CAAC,GAAG,CAAJ,EAAOiG,GAAG,GAAGoD,WAAW,CAACM,KAAZ,CAAkB9M,MAApC,EAA4CmD,CAAC,GAAGiG,GAAhD,EAAqDjG,CAAC,EAAtD,EAA0D;AACzD,UAAIqJ,WAAW,CAACM,KAAZ,CAAkB3J,CAAlB,EAAqB0E,IAArB,CAA0B8G,IAA1B,CAAJ,EAAqC;AACpC,eAAO,KAAP;AACA;AACD;;AAED,SAAKxL,CAAC,GAAG,CAAJ,EAAOiG,GAAG,GAAGoD,WAAW,CAACK,KAAZ,CAAkB7M,MAApC,EAA4CmD,CAAC,GAAGiG,GAAhD,EAAqDjG,CAAC,EAAtD,EAA0D;AACzD,UAAIqJ,WAAW,CAACK,KAAZ,CAAkB1J,CAAlB,EAAqB0E,IAArB,CAA0B8G,IAA1B,CAAJ,EAAqC;AACpC,eAAO,IAAP;AACA;AACD;;AAED,WAAO,KAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASF,WAAT,CAAqBG,MAArB,EAA6B;AAC5B,WAAOA,MAAM,CAACC,QAAP,GACLC,SADK,CACK,CADL,EACQF,MAAM,CAACC,QAAP,GAAkB7O,MAAlB,GAA2B,CADnC,EAELwL,OAFK,CAEG,SAFH,EAEc,GAFd,CAAP;AAGA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASiB,MAAT,CAAgBoB,GAAhB,EAAqB;AACpB,QAAIA,GAAG,YAAY/L,KAAnB,EAA0B;AACzB,aAAO+L,GAAG,CAACkB,KAAJ,IAAalB,GAAG,CAACjH,OAAxB;AACA;;AACD,WAAOiH,GAAP;AACA;AAED;AACD;AACA;AACA;;;AACC,WAAStK,OAAT,GAAmB;AAClBsG,WAAO,CAACC,IAAR,CAAa,uIAAb;AACA;;AAED0C,aAAW,CAACG,MAAZ,CAAmBH,WAAW,CAAChD,IAAZ,EAAnB;AAEA,SAAOgD,WAAP;AACA;;AAEDjQ,MAAM,CAACV,OAAP,GAAiB0Q,KAAjB,C;;;;;;;;;;;ACjRAhQ,MAAM,CAACV,OAAP,GAAkB,YAAM;AACtB,MAAI,OAAOmF,IAAP,KAAgB,WAApB,EAAiC;AAC/B,WAAOA,IAAP;AACD,GAFD,MAEO,IAAI,OAAOgJ,MAAP,KAAkB,WAAtB,EAAmC;AACxC,WAAOA,MAAP;AACD,GAFM,MAEA;AACL,WAAOgF,QAAQ,CAAC,aAAD,CAAR,EAAP;AACD;AACF,CARgB,EAAjB,C;;;;;;;;;;;ACAA,IAAMhT,MAAM,GAAGI,mBAAO,CAAC,+DAAD,CAAtB;;AAEAG,MAAM,CAACV,OAAP,GAAiB,UAACc,GAAD,EAAMC,IAAN;AAAA,SAAe,IAAIZ,MAAJ,CAAWW,GAAX,EAAgBC,IAAhB,CAAf;AAAA,CAAjB;AAEA;AACA;AACA;AACA;;;AAEAL,MAAM,CAACV,OAAP,CAAeG,MAAf,GAAwBA,MAAxB;AACAO,MAAM,CAACV,OAAP,CAAeK,QAAf,GAA0BF,MAAM,CAACE,QAAjC,C,CAA2C;;AAC3CK,MAAM,CAACV,OAAP,CAAeoT,SAAf,GAA2B7S,mBAAO,CAAC,qEAAD,CAAlC;AACAG,MAAM,CAACV,OAAP,CAAeqT,UAAf,GAA4B9S,mBAAO,CAAC,mFAAD,CAAnC;AACAG,MAAM,CAACV,OAAP,CAAeqC,MAAf,GAAwB9B,mBAAO,CAAC,sEAAD,CAA/B,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbA,IAAM8S,UAAU,GAAG9S,mBAAO,CAAC,mFAAD,CAA1B;;AACA,IAAMkL,OAAO,GAAGlL,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,yBAAjB,CAAd;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAMoL,QAAQ,GAAGpL,mBAAO,CAAC,kDAAD,CAAxB;;AACA,IAAM+S,OAAO,GAAG/S,mBAAO,CAAC,gDAAD,CAAvB;;AACA,eAAkCA,mBAAO,CAAC,2DAAD,CAAzC;AAAA,IAAQqC,qBAAR,YAAQA,qBAAR;;IAEMzC,M;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACA;AACE,kBAAYW,GAAZ,EAA4B;AAAA;;AAAA,QAAXC,IAAW,uEAAJ,EAAI;;AAAA;;AAC1B;;AAEA,QAAID,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;AAClCC,UAAI,GAAGD,GAAP;AACAA,SAAG,GAAG,IAAN;AACD;;AAED,QAAIA,GAAJ,EAAS;AACPA,SAAG,GAAG6K,QAAQ,CAAC7K,GAAD,CAAd;AACAC,UAAI,CAACwS,QAAL,GAAgBzS,GAAG,CAACgL,IAApB;AACA/K,UAAI,CAACyS,MAAL,GAAc1S,GAAG,CAACT,QAAJ,KAAiB,OAAjB,IAA4BS,GAAG,CAACT,QAAJ,KAAiB,KAA3D;AACAU,UAAI,CAACkL,IAAL,GAAYnL,GAAG,CAACmL,IAAhB;AACA,UAAInL,GAAG,CAACY,KAAR,EAAeX,IAAI,CAACW,KAAL,GAAaZ,GAAG,CAACY,KAAjB;AAChB,KAND,MAMO,IAAIX,IAAI,CAAC+K,IAAT,EAAe;AACpB/K,UAAI,CAACwS,QAAL,GAAgB5H,QAAQ,CAAC5K,IAAI,CAAC+K,IAAN,CAAR,CAAoBA,IAApC;AACD;;AAEDlJ,yBAAqB,gCAAO7B,IAAP,CAArB;AAEA,UAAKyS,MAAL,GACE,QAAQzS,IAAI,CAACyS,MAAb,GACIzS,IAAI,CAACyS,MADT,GAEI,OAAO3H,QAAP,KAAoB,WAApB,IAAmC,aAAaA,QAAQ,CAACxL,QAH/D;;AAKA,QAAIU,IAAI,CAACwS,QAAL,IAAiB,CAACxS,IAAI,CAACkL,IAA3B,EAAiC;AAC/B;AACAlL,UAAI,CAACkL,IAAL,GAAY,MAAKuH,MAAL,GAAc,KAAd,GAAsB,IAAlC;AACD;;AAED,UAAKD,QAAL,GACExS,IAAI,CAACwS,QAAL,KACC,OAAO1H,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAAC0H,QAA3C,GAAsD,WADvD,CADF;AAGA,UAAKtH,IAAL,GACElL,IAAI,CAACkL,IAAL,KACC,OAAOJ,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACI,IAA5C,GACGJ,QAAQ,CAACI,IADZ,GAEG,MAAKuH,MAAL,GACA,GADA,GAEA,EALJ,CADF;AAQA,UAAKH,UAAL,GAAkBtS,IAAI,CAACsS,UAAL,IAAmB,CAAC,SAAD,EAAY,WAAZ,CAArC;AACA,UAAKI,UAAL,GAAkB,EAAlB;AACA,UAAKC,WAAL,GAAmB,EAAnB;AACA,UAAKC,aAAL,GAAqB,CAArB;AAEA,UAAK5S,IAAL,GAAY,SACV;AACEI,UAAI,EAAE,YADR;AAEEyS,WAAK,EAAE,KAFT;AAGEC,qBAAe,EAAE,KAHnB;AAIEC,aAAO,EAAE,IAJX;AAKEC,WAAK,EAAE,IALT;AAMEC,oBAAc,EAAE,GANlB;AAOEC,qBAAe,EAAE,KAPnB;AAQEC,wBAAkB,EAAE,IARtB;AASEC,uBAAiB,EAAE;AACjBC,iBAAS,EAAE;AADM,OATrB;AAYEC,sBAAgB,EAAE,EAZpB;AAaEC,yBAAmB,EAAE;AAbvB,KADU,EAgBVvT,IAhBU,CAAZ;AAmBA,UAAKA,IAAL,CAAUI,IAAV,GAAiB,MAAKJ,IAAL,CAAUI,IAAV,CAAewO,OAAf,CAAuB,KAAvB,EAA8B,EAA9B,IAAoC,GAArD;;AAEA,QAAI,OAAO,MAAK5O,IAAL,CAAUW,KAAjB,KAA2B,QAA/B,EAAyC;AACvC,YAAKX,IAAL,CAAUW,KAAV,GAAkB4R,OAAO,CAACiB,MAAR,CAAe,MAAKxT,IAAL,CAAUW,KAAzB,CAAlB;AACD,KArEyB,CAuE1B;;;AACA,UAAKL,EAAL,GAAU,IAAV;AACA,UAAKmT,QAAL,GAAgB,IAAhB;AACA,UAAKC,YAAL,GAAoB,IAApB;AACA,UAAKC,WAAL,GAAmB,IAAnB,CA3E0B,CA6E1B;;AACA,UAAKC,gBAAL,GAAwB,IAAxB;;AAEA,QAAI,OAAO5H,gBAAP,KAA4B,UAAhC,EAA4C;AAC1C,UAAI,MAAKhM,IAAL,CAAUuT,mBAAd,EAAmC;AACjC;AACA;AACA;AACAvH,wBAAgB,CACd,cADc,EAEd,YAAM;AACJ,cAAI,MAAK/C,SAAT,EAAoB;AAClB;AACA,kBAAKA,SAAL,CAAekD,kBAAf;;AACA,kBAAKlD,SAAL,CAAejE,KAAf;AACD;AACF,SARa,EASd,KATc,CAAhB;AAWD;;AACD,UAAI,MAAKwN,QAAL,KAAkB,WAAtB,EAAmC;AACjC,cAAKqB,oBAAL,GAA4B,YAAM;AAChC,gBAAKC,OAAL,CAAa,iBAAb;AACD,SAFD;;AAGA9H,wBAAgB,CAAC,SAAD,EAAY,MAAK6H,oBAAjB,EAAuC,KAAvC,CAAhB;AACD;AACF;;AAED,UAAK5Q,IAAL;;AAzG0B;AA0G3B;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;;WACE,yBAAgB8O,IAAhB,EAAsB;AACpBrS,WAAK,CAAC,yBAAD,EAA4BqS,IAA5B,CAAL;AACA,UAAMpR,KAAK,GAAGoT,KAAK,CAAC,KAAK/T,IAAL,CAAUW,KAAX,CAAnB,CAFoB,CAIpB;;AACAA,WAAK,CAACqT,GAAN,GAAY1S,MAAM,CAAChC,QAAnB,CALoB,CAOpB;;AACAqB,WAAK,CAACsI,SAAN,GAAkB8I,IAAlB,CARoB,CAUpB;;AACA,UAAI,KAAKzR,EAAT,EAAaK,KAAK,CAAC2I,GAAN,GAAY,KAAKhJ,EAAjB;;AAEb,UAAMN,IAAI,GAAG,SACX,EADW,EAEX,KAAKA,IAAL,CAAUsT,gBAAV,CAA2BvB,IAA3B,CAFW,EAGX,KAAK/R,IAHM,EAIX;AACEW,aAAK,EAALA,KADF;AAEEE,cAAM,EAAE,IAFV;AAGE2R,gBAAQ,EAAE,KAAKA,QAHjB;AAIEC,cAAM,EAAE,KAAKA,MAJf;AAKEvH,YAAI,EAAE,KAAKA;AALb,OAJW,CAAb;;AAaAxL,WAAK,CAAC,aAAD,EAAgBM,IAAhB,CAAL;AAEA,aAAO,IAAIsS,UAAU,CAACP,IAAD,CAAd,CAAqB/R,IAArB,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,gBAAO;AAAA;;AACL,UAAIiJ,SAAJ;;AACA,UACE,KAAKjJ,IAAL,CAAUkT,eAAV,IACA9T,MAAM,CAAC6U,qBADP,IAEA,KAAK3B,UAAL,CAAgBpO,OAAhB,CAAwB,WAAxB,MAAyC,CAAC,CAH5C,EAIE;AACA+E,iBAAS,GAAG,WAAZ;AACD,OAND,MAMO,IAAI,MAAM,KAAKqJ,UAAL,CAAgBlP,MAA1B,EAAkC;AACvC;AACA,aAAK2B,YAAL,CAAkB,YAAM;AACtB,gBAAI,CAACE,IAAL,CAAU,OAAV,EAAmB,yBAAnB;AACD,SAFD,EAEG,CAFH;AAGA;AACD,OANM,MAMA;AACLgE,iBAAS,GAAG,KAAKqJ,UAAL,CAAgB,CAAhB,CAAZ;AACD;;AACD,WAAKI,UAAL,GAAkB,SAAlB,CAjBK,CAmBL;;AACA,UAAI;AACFzJ,iBAAS,GAAG,KAAKiL,eAAL,CAAqBjL,SAArB,CAAZ;AACD,OAFD,CAEE,OAAOkL,CAAP,EAAU;AACVzU,aAAK,CAAC,oCAAD,EAAuCyU,CAAvC,CAAL;AACA,aAAK7B,UAAL,CAAgB8B,KAAhB;AACA,aAAKnR,IAAL;AACA;AACD;;AAEDgG,eAAS,CAAChG,IAAV;AACA,WAAKoR,YAAL,CAAkBpL,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,sBAAaA,SAAb,EAAwB;AAAA;;AACtBvJ,WAAK,CAAC,sBAAD,EAAyBuJ,SAAS,CAAC8I,IAAnC,CAAL;;AAEA,UAAI,KAAK9I,SAAT,EAAoB;AAClBvJ,aAAK,CAAC,gCAAD,EAAmC,KAAKuJ,SAAL,CAAe8I,IAAlD,CAAL;AACA,aAAK9I,SAAL,CAAekD,kBAAf;AACD,OANqB,CAQtB;;;AACA,WAAKlD,SAAL,GAAiBA,SAAjB,CATsB,CAWtB;;AACAA,eAAS,CACN1E,EADH,CACM,OADN,EACe,KAAK+P,OAAL,CAAa7O,IAAb,CAAkB,IAAlB,CADf,EAEGlB,EAFH,CAEM,QAFN,EAEgB,KAAKgQ,QAAL,CAAc9O,IAAd,CAAmB,IAAnB,CAFhB,EAGGlB,EAHH,CAGM,OAHN,EAGe,KAAKiQ,OAAL,CAAa/O,IAAb,CAAkB,IAAlB,CAHf,EAIGlB,EAJH,CAIM,OAJN,EAIe,YAAM;AACjB,cAAI,CAACuP,OAAL,CAAa,iBAAb;AACD,OANH;AAOD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,eAAM/B,IAAN,EAAY;AAAA;;AACVrS,WAAK,CAAC,wBAAD,EAA2BqS,IAA3B,CAAL;AACA,UAAI9I,SAAS,GAAG,KAAKiL,eAAL,CAAqBnC,IAArB,EAA2B;AAAE0C,aAAK,EAAE;AAAT,OAA3B,CAAhB;AACA,UAAIC,MAAM,GAAG,KAAb;AAEAtV,YAAM,CAAC6U,qBAAP,GAA+B,KAA/B;;AAEA,UAAMU,eAAe,GAAG,SAAlBA,eAAkB,GAAM;AAC5B,YAAID,MAAJ,EAAY;AAEZhV,aAAK,CAAC,6BAAD,EAAgCqS,IAAhC,CAAL;AACA9I,iBAAS,CAAC2L,IAAV,CAAe,CAAC;AAAEjM,cAAI,EAAE,MAAR;AAAgB7C,cAAI,EAAE;AAAtB,SAAD,CAAf;AACAmD,iBAAS,CAACiD,IAAV,CAAe,QAAf,EAAyB,UAAA2I,GAAG,EAAI;AAC9B,cAAIH,MAAJ,EAAY;;AACZ,cAAI,WAAWG,GAAG,CAAClM,IAAf,IAAuB,YAAYkM,GAAG,CAAC/O,IAA3C,EAAiD;AAC/CpG,iBAAK,CAAC,2BAAD,EAA8BqS,IAA9B,CAAL;AACA,kBAAI,CAAC+C,SAAL,GAAiB,IAAjB;;AACA,kBAAI,CAAC7P,IAAL,CAAU,WAAV,EAAuBgE,SAAvB;;AACA,gBAAI,CAACA,SAAL,EAAgB;AAChB7J,kBAAM,CAAC6U,qBAAP,GAA+B,gBAAgBhL,SAAS,CAAC8I,IAAzD;AAEArS,iBAAK,CAAC,gCAAD,EAAmC,MAAI,CAACuJ,SAAL,CAAe8I,IAAlD,CAAL;;AACA,kBAAI,CAAC9I,SAAL,CAAe8L,KAAf,CAAqB,YAAM;AACzB,kBAAIL,MAAJ,EAAY;AACZ,kBAAI,aAAa,MAAI,CAAChC,UAAtB,EAAkC;AAClChT,mBAAK,CAAC,+CAAD,CAAL;AAEAiF,qBAAO;;AAEP,oBAAI,CAAC0P,YAAL,CAAkBpL,SAAlB;;AACAA,uBAAS,CAAC2L,IAAV,CAAe,CAAC;AAAEjM,oBAAI,EAAE;AAAR,eAAD,CAAf;;AACA,oBAAI,CAAC1D,IAAL,CAAU,SAAV,EAAqBgE,SAArB;;AACAA,uBAAS,GAAG,IAAZ;AACA,oBAAI,CAAC6L,SAAL,GAAiB,KAAjB;;AACA,oBAAI,CAACE,KAAL;AACD,aAbD;AAcD,WAtBD,MAsBO;AACLtV,iBAAK,CAAC,6BAAD,EAAgCqS,IAAhC,CAAL;AACA,gBAAMrN,GAAG,GAAG,IAAIQ,KAAJ,CAAU,aAAV,CAAZ;AACAR,eAAG,CAACuE,SAAJ,GAAgBA,SAAS,CAAC8I,IAA1B;;AACA,kBAAI,CAAC9M,IAAL,CAAU,cAAV,EAA0BP,GAA1B;AACD;AACF,SA9BD;AA+BD,OApCD;;AAsCA,eAASuQ,eAAT,GAA2B;AACzB,YAAIP,MAAJ,EAAY,OADa,CAGzB;;AACAA,cAAM,GAAG,IAAT;AAEA/P,eAAO;AAEPsE,iBAAS,CAACjE,KAAV;AACAiE,iBAAS,GAAG,IAAZ;AACD,OAvDS,CAyDV;;;AACA,UAAMtD,OAAO,GAAG,SAAVA,OAAU,CAAAjB,GAAG,EAAI;AACrB,YAAMuK,KAAK,GAAG,IAAI/J,KAAJ,CAAU,kBAAkBR,GAA5B,CAAd;AACAuK,aAAK,CAAChG,SAAN,GAAkBA,SAAS,CAAC8I,IAA5B;AAEAkD,uBAAe;AAEfvV,aAAK,CAAC,kDAAD,EAAqDqS,IAArD,EAA2DrN,GAA3D,CAAL;;AAEA,cAAI,CAACO,IAAL,CAAU,cAAV,EAA0BgK,KAA1B;AACD,OATD;;AAWA,eAASiG,gBAAT,GAA4B;AAC1BvP,eAAO,CAAC,kBAAD,CAAP;AACD,OAvES,CAyEV;;;AACA,eAASC,OAAT,GAAmB;AACjBD,eAAO,CAAC,eAAD,CAAP;AACD,OA5ES,CA8EV;;;AACA,eAASwP,SAAT,CAAmBC,EAAnB,EAAuB;AACrB,YAAInM,SAAS,IAAImM,EAAE,CAACrD,IAAH,KAAY9I,SAAS,CAAC8I,IAAvC,EAA6C;AAC3CrS,eAAK,CAAC,4BAAD,EAA+B0V,EAAE,CAACrD,IAAlC,EAAwC9I,SAAS,CAAC8I,IAAlD,CAAL;AACAkD,yBAAe;AAChB;AACF,OApFS,CAsFV;;;AACA,UAAMtQ,OAAO,GAAG,SAAVA,OAAU,GAAM;AACpBsE,iBAAS,CAACrB,cAAV,CAAyB,MAAzB,EAAiC+M,eAAjC;AACA1L,iBAAS,CAACrB,cAAV,CAAyB,OAAzB,EAAkCjC,OAAlC;AACAsD,iBAAS,CAACrB,cAAV,CAAyB,OAAzB,EAAkCsN,gBAAlC;;AACA,cAAI,CAACtN,cAAL,CAAoB,OAApB,EAA6BhC,OAA7B;;AACA,cAAI,CAACgC,cAAL,CAAoB,WAApB,EAAiCuN,SAAjC;AACD,OAND;;AAQAlM,eAAS,CAACiD,IAAV,CAAe,MAAf,EAAuByI,eAAvB;AACA1L,eAAS,CAACiD,IAAV,CAAe,OAAf,EAAwBvG,OAAxB;AACAsD,eAAS,CAACiD,IAAV,CAAe,OAAf,EAAwBgJ,gBAAxB;AAEA,WAAKhJ,IAAL,CAAU,OAAV,EAAmBtG,OAAnB;AACA,WAAKsG,IAAL,CAAU,WAAV,EAAuBiJ,SAAvB;AAEAlM,eAAS,CAAChG,IAAV;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AACPvD,WAAK,CAAC,aAAD,CAAL;AACA,WAAKgT,UAAL,GAAkB,MAAlB;AACAtT,YAAM,CAAC6U,qBAAP,GAA+B,gBAAgB,KAAKhL,SAAL,CAAe8I,IAA9D;AACA,WAAK9M,IAAL,CAAU,MAAV;AACA,WAAK+P,KAAL,GALO,CAOP;AACA;;AACA,UACE,WAAW,KAAKtC,UAAhB,IACA,KAAK1S,IAAL,CAAU+S,OADV,IAEA,KAAK9J,SAAL,CAAe8L,KAHjB,EAIE;AACArV,aAAK,CAAC,yBAAD,CAAL;AACA,YAAI6G,CAAC,GAAG,CAAR;AACA,YAAM8O,CAAC,GAAG,KAAK5B,QAAL,CAAcrQ,MAAxB;;AACA,eAAOmD,CAAC,GAAG8O,CAAX,EAAc9O,CAAC,EAAf,EAAmB;AACjB,eAAKkO,KAAL,CAAW,KAAKhB,QAAL,CAAclN,CAAd,CAAX;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAASP,MAAT,EAAiB;AACf,UACE,cAAc,KAAK0M,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAHrB,EAIE;AACAhT,aAAK,CAAC,sCAAD,EAAyCsG,MAAM,CAAC2C,IAAhD,EAAsD3C,MAAM,CAACF,IAA7D,CAAL;AAEA,aAAKb,IAAL,CAAU,QAAV,EAAoBe,MAApB,EAHA,CAKA;;AACA,aAAKf,IAAL,CAAU,WAAV;;AAEA,gBAAQe,MAAM,CAAC2C,IAAf;AACE,eAAK,MAAL;AACE,iBAAK2M,WAAL,CAAiB7F,IAAI,CAAC8F,KAAL,CAAWvP,MAAM,CAACF,IAAlB,CAAjB;AACA;;AAEF,eAAK,MAAL;AACE,iBAAK0P,gBAAL;AACA,iBAAKC,UAAL,CAAgB,MAAhB;AACA,iBAAKxQ,IAAL,CAAU,MAAV;AACA,iBAAKA,IAAL,CAAU,MAAV;AACA;;AAEF,eAAK,OAAL;AACE,gBAAMP,GAAG,GAAG,IAAIQ,KAAJ,CAAU,cAAV,CAAZ;AACAR,eAAG,CAACgR,IAAJ,GAAW1P,MAAM,CAACF,IAAlB;AACA,iBAAK0O,OAAL,CAAa9P,GAAb;AACA;;AAEF,eAAK,SAAL;AACE,iBAAKO,IAAL,CAAU,MAAV,EAAkBe,MAAM,CAACF,IAAzB;AACA,iBAAKb,IAAL,CAAU,SAAV,EAAqBe,MAAM,CAACF,IAA5B;AACA;AArBJ;AAuBD,OAnCD,MAmCO;AACLpG,aAAK,CAAC,6CAAD,EAAgD,KAAKgT,UAArD,CAAL;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,qBAAY5M,IAAZ,EAAkB;AAChB,WAAKb,IAAL,CAAU,WAAV,EAAuBa,IAAvB;AACA,WAAKxF,EAAL,GAAUwF,IAAI,CAACwD,GAAf;AACA,WAAKL,SAAL,CAAetI,KAAf,CAAqB2I,GAArB,GAA2BxD,IAAI,CAACwD,GAAhC;AACA,WAAKmK,QAAL,GAAgB,KAAKkC,cAAL,CAAoB7P,IAAI,CAAC2N,QAAzB,CAAhB;AACA,WAAKC,YAAL,GAAoB5N,IAAI,CAAC4N,YAAzB;AACA,WAAKC,WAAL,GAAmB7N,IAAI,CAAC6N,WAAxB;AACA,WAAKiC,MAAL,GAPgB,CAQhB;;AACA,UAAI,aAAa,KAAKlD,UAAtB,EAAkC;AAClC,WAAK8C,gBAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,4BAAmB;AAAA;;AACjB,WAAKK,cAAL,CAAoB,KAAKjC,gBAAzB;AACA,WAAKA,gBAAL,GAAwB,KAAK7O,YAAL,CAAkB,YAAM;AAC9C,cAAI,CAAC+O,OAAL,CAAa,cAAb;AACD,OAFuB,EAErB,KAAKJ,YAAL,GAAoB,KAAKC,WAFJ,CAAxB;;AAGA,UAAI,KAAK3T,IAAL,CAAUmF,SAAd,EAAyB;AACvB,aAAKyO,gBAAL,CAAsBxO,KAAtB;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AACR,WAAKuN,WAAL,CAAiBlI,MAAjB,CAAwB,CAAxB,EAA2B,KAAKmI,aAAhC,EADQ,CAGR;AACA;AACA;;AACA,WAAKA,aAAL,GAAqB,CAArB;;AAEA,UAAI,MAAM,KAAKD,WAAL,CAAiBvP,MAA3B,EAAmC;AACjC,aAAK6B,IAAL,CAAU,OAAV;AACD,OAFD,MAEO;AACL,aAAK+P,KAAL;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQ;AACN,UACE,aAAa,KAAKtC,UAAlB,IACA,KAAKzJ,SAAL,CAAeC,QADf,IAEA,CAAC,KAAK4L,SAFN,IAGA,KAAKnC,WAAL,CAAiBvP,MAJnB,EAKE;AACA1D,aAAK,CAAC,+BAAD,EAAkC,KAAKiT,WAAL,CAAiBvP,MAAnD,CAAL;AACA,aAAK6F,SAAL,CAAe2L,IAAf,CAAoB,KAAKjC,WAAzB,EAFA,CAGA;AACA;;AACA,aAAKC,aAAL,GAAqB,KAAKD,WAAL,CAAiBvP,MAAtC;AACA,aAAK6B,IAAL,CAAU,OAAV;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;WACE,eAAM4P,GAAN,EAAWpO,OAAX,EAAoBxC,EAApB,EAAwB;AACtB,WAAKwR,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCpO,OAAhC,EAAyCxC,EAAzC;AACA,aAAO,IAAP;AACD;;;WAED,cAAK4Q,GAAL,EAAUpO,OAAV,EAAmBxC,EAAnB,EAAuB;AACrB,WAAKwR,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCpO,OAAhC,EAAyCxC,EAAzC;AACA,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;WACE,oBAAW0E,IAAX,EAAiB7C,IAAjB,EAAuBW,OAAvB,EAAgCxC,EAAhC,EAAoC;AAClC,UAAI,eAAe,OAAO6B,IAA1B,EAAgC;AAC9B7B,UAAE,GAAG6B,IAAL;AACAA,YAAI,GAAG7F,SAAP;AACD;;AAED,UAAI,eAAe,OAAOwG,OAA1B,EAAmC;AACjCxC,UAAE,GAAGwC,OAAL;AACAA,eAAO,GAAG,IAAV;AACD;;AAED,UAAI,cAAc,KAAKiM,UAAnB,IAAiC,aAAa,KAAKA,UAAvD,EAAmE;AACjE;AACD;;AAEDjM,aAAO,GAAGA,OAAO,IAAI,EAArB;AACAA,aAAO,CAACqC,QAAR,GAAmB,UAAUrC,OAAO,CAACqC,QAArC;AAEA,UAAM9C,MAAM,GAAG;AACb2C,YAAI,EAAEA,IADO;AAEb7C,YAAI,EAAEA,IAFO;AAGbW,eAAO,EAAEA;AAHI,OAAf;AAKA,WAAKxB,IAAL,CAAU,cAAV,EAA0Be,MAA1B;AACA,WAAK2M,WAAL,CAAiBtN,IAAjB,CAAsBW,MAAtB;AACA,UAAI/B,EAAJ,EAAQ,KAAKiI,IAAL,CAAU,OAAV,EAAmBjI,EAAnB;AACR,WAAK+Q,KAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQ;AAAA;;AACN,UAAMhQ,KAAK,GAAG,SAARA,KAAQ,GAAM;AAClB,cAAI,CAAC8O,OAAL,CAAa,cAAb;;AACApU,aAAK,CAAC,6CAAD,CAAL;;AACA,cAAI,CAACuJ,SAAL,CAAejE,KAAf;AACD,OAJD;;AAMA,UAAM8Q,eAAe,GAAG,SAAlBA,eAAkB,GAAM;AAC5B,cAAI,CAAClO,cAAL,CAAoB,SAApB,EAA+BkO,eAA/B;;AACA,cAAI,CAAClO,cAAL,CAAoB,cAApB,EAAoCkO,eAApC;;AACA9Q,aAAK;AACN,OAJD;;AAMA,UAAM+Q,cAAc,GAAG,SAAjBA,cAAiB,GAAM;AAC3B;AACA,cAAI,CAAC7J,IAAL,CAAU,SAAV,EAAqB4J,eAArB;;AACA,cAAI,CAAC5J,IAAL,CAAU,cAAV,EAA0B4J,eAA1B;AACD,OAJD;;AAMA,UAAI,cAAc,KAAKpD,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;AAC/D,aAAKA,UAAL,GAAkB,SAAlB;;AAEA,YAAI,KAAKC,WAAL,CAAiBvP,MAArB,EAA6B;AAC3B,eAAK8I,IAAL,CAAU,OAAV,EAAmB,YAAM;AACvB,gBAAI,MAAI,CAAC4I,SAAT,EAAoB;AAClBiB,4BAAc;AACf,aAFD,MAEO;AACL/Q,mBAAK;AACN;AACF,WAND;AAOD,SARD,MAQO,IAAI,KAAK8P,SAAT,EAAoB;AACzBiB,wBAAc;AACf,SAFM,MAEA;AACL/Q,eAAK;AACN;AACF;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQN,GAAR,EAAa;AACXhF,WAAK,CAAC,iBAAD,EAAoBgF,GAApB,CAAL;AACAtF,YAAM,CAAC6U,qBAAP,GAA+B,KAA/B;AACA,WAAKhP,IAAL,CAAU,OAAV,EAAmBP,GAAnB;AACA,WAAKoP,OAAL,CAAa,iBAAb,EAAgCpP,GAAhC;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQmC,MAAR,EAAgBmP,IAAhB,EAAsB;AACpB,UACE,cAAc,KAAKtD,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAHrB,EAIE;AACAhT,aAAK,CAAC,gCAAD,EAAmCmH,MAAnC,CAAL,CADA,CAGA;;AACA,aAAKgP,cAAL,CAAoB,KAAKI,iBAAzB;AACA,aAAKJ,cAAL,CAAoB,KAAKjC,gBAAzB,EALA,CAOA;;AACA,aAAK3K,SAAL,CAAekD,kBAAf,CAAkC,OAAlC,EARA,CAUA;;AACA,aAAKlD,SAAL,CAAejE,KAAf,GAXA,CAaA;;AACA,aAAKiE,SAAL,CAAekD,kBAAf;;AAEA,YAAI,OAAOC,mBAAP,KAA+B,UAAnC,EAA+C;AAC7CA,6BAAmB,CAAC,SAAD,EAAY,KAAKyH,oBAAjB,EAAuC,KAAvC,CAAnB;AACD,SAlBD,CAoBA;;;AACA,aAAKnB,UAAL,GAAkB,QAAlB,CArBA,CAuBA;;AACA,aAAKpS,EAAL,GAAU,IAAV,CAxBA,CA0BA;;AACA,aAAK2E,IAAL,CAAU,OAAV,EAAmB4B,MAAnB,EAA2BmP,IAA3B,EA3BA,CA6BA;AACA;;AACA,aAAKrD,WAAL,GAAmB,EAAnB;AACA,aAAKC,aAAL,GAAqB,CAArB;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;WACE,wBAAea,QAAf,EAAyB;AACvB,UAAMyC,gBAAgB,GAAG,EAAzB;AACA,UAAI3P,CAAC,GAAG,CAAR;AACA,UAAMiJ,CAAC,GAAGiE,QAAQ,CAACrQ,MAAnB;;AACA,aAAOmD,CAAC,GAAGiJ,CAAX,EAAcjJ,CAAC,EAAf,EAAmB;AACjB,YAAI,CAAC,KAAK+L,UAAL,CAAgBpO,OAAhB,CAAwBuP,QAAQ,CAAClN,CAAD,CAAhC,CAAL,EACE2P,gBAAgB,CAAC7Q,IAAjB,CAAsBoO,QAAQ,CAAClN,CAAD,CAA9B;AACH;;AACD,aAAO2P,gBAAP;AACD;;;;EA9oBkBxL,O;;AAipBrBtL,MAAM,CAAC6U,qBAAP,GAA+B,KAA/B;AAEA;AACA;AACA;AACA;AACA;;AAEA7U,MAAM,CAACE,QAAP,GAAkBgC,MAAM,CAAChC,QAAzB,C,CAAmC;;AAEnC,SAASyU,KAAT,CAAe5M,GAAf,EAAoB;AAClB,MAAMgP,CAAC,GAAG,EAAV;;AACA,OAAK,IAAI5P,CAAT,IAAcY,GAAd,EAAmB;AACjB,QAAIA,GAAG,CAACuB,cAAJ,CAAmBnC,CAAnB,CAAJ,EAA2B;AACzB4P,OAAC,CAAC5P,CAAD,CAAD,GAAOY,GAAG,CAACZ,CAAD,CAAV;AACD;AACF;;AACD,SAAO4P,CAAP;AACD;;AAEDxW,MAAM,CAACV,OAAP,GAAiBG,MAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7qBA,IAAMkC,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAMkL,OAAO,GAAGlL,mBAAO,CAAC,oEAAD,CAAvB;;AACA,eAAkCA,mBAAO,CAAC,2DAAD,CAAzC;AAAA,IAAQqC,qBAAR,YAAQA,qBAAR;;AACA,IAAMnC,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,4BAAjB,CAAd;;IAEM6S,S;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,qBAAYrS,IAAZ,EAAkB;AAAA;;AAAA;;AAChB;AACA6B,yBAAqB,gCAAO7B,IAAP,CAArB;AAEA,UAAKA,IAAL,GAAYA,IAAZ;AACA,UAAKW,KAAL,GAAaX,IAAI,CAACW,KAAlB;AACA,UAAK+R,UAAL,GAAkB,EAAlB;AACA,UAAK7R,MAAL,GAAcb,IAAI,CAACa,MAAnB;AAPgB;AAQjB;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;;WACE,iBAAQgU,GAAR,EAAamB,IAAb,EAAmB;AACjB,UAAMtR,GAAG,GAAG,IAAIQ,KAAJ,CAAU2P,GAAV,CAAZ;AACAnQ,SAAG,CAACiE,IAAJ,GAAW,gBAAX;AACAjE,SAAG,CAAC0R,WAAJ,GAAkBJ,IAAlB;AACA,WAAK/Q,IAAL,CAAU,OAAV,EAAmBP,GAAnB;AACA,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,gBAAO;AACL,UAAI,aAAa,KAAKgO,UAAlB,IAAgC,OAAO,KAAKA,UAAhD,EAA4D;AAC1D,aAAKA,UAAL,GAAkB,SAAlB;AACA,aAAK2D,MAAL;AACD;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQ;AACN,UAAI,cAAc,KAAK3D,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;AAC/D,aAAK4D,OAAL;AACA,aAAKxC,OAAL;AACD;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,cAAKyC,OAAL,EAAc;AACZ,UAAI,WAAW,KAAK7D,UAApB,EAAgC;AAC9B,aAAKlM,KAAL,CAAW+P,OAAX;AACD,OAFD,MAEO;AACL;AACA7W,aAAK,CAAC,2CAAD,CAAL;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AACP,WAAKgT,UAAL,GAAkB,MAAlB;AACA,WAAKxJ,QAAL,GAAgB,IAAhB;AACA,WAAKjE,IAAL,CAAU,MAAV;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,gBAAOa,IAAP,EAAa;AACX,UAAME,MAAM,GAAG1E,MAAM,CAACkV,YAAP,CAAoB1Q,IAApB,EAA0B,KAAKjF,MAAL,CAAY4V,UAAtC,CAAf;AACA,WAAKlC,QAAL,CAAcvO,MAAd;AACD;AAED;AACF;AACA;;;;WACE,kBAASA,MAAT,EAAiB;AACf,WAAKf,IAAL,CAAU,QAAV,EAAoBe,MAApB;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AACR,WAAK0M,UAAL,GAAkB,QAAlB;AACA,WAAKzN,IAAL,CAAU,OAAV;AACD;;;;EAhHqByF,O;;AAmHxB/K,MAAM,CAACV,OAAP,GAAiBoT,SAAjB,C;;;;;;;;;;;ACxHA,IAAMqE,cAAc,GAAGlX,mBAAO,CAAC,iFAAD,CAA9B;;AACA,IAAMmX,GAAG,GAAGnX,mBAAO,CAAC,oFAAD,CAAnB;;AACA,IAAMoX,KAAK,GAAGpX,mBAAO,CAAC,wFAAD,CAArB;;AACA,IAAMqX,SAAS,GAAGrX,mBAAO,CAAC,gFAAD,CAAzB;;AAEAP,OAAO,CAAC6X,OAAR,GAAkBA,OAAlB;AACA7X,OAAO,CAAC4X,SAAR,GAAoBA,SAApB;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAT,CAAiB9W,IAAjB,EAAuB;AACrB,MAAI+W,GAAJ;AACA,MAAIC,EAAE,GAAG,KAAT;AACA,MAAIC,EAAE,GAAG,KAAT;AACA,MAAMjE,KAAK,GAAG,UAAUhT,IAAI,CAACgT,KAA7B;;AAEA,MAAI,OAAOlI,QAAP,KAAoB,WAAxB,EAAqC;AACnC,QAAMoM,KAAK,GAAG,aAAapM,QAAQ,CAACxL,QAApC;AACA,QAAI4L,IAAI,GAAGJ,QAAQ,CAACI,IAApB,CAFmC,CAInC;;AACA,QAAI,CAACA,IAAL,EAAW;AACTA,UAAI,GAAGgM,KAAK,GAAG,GAAH,GAAS,EAArB;AACD;;AAEDF,MAAE,GAAGhX,IAAI,CAACwS,QAAL,KAAkB1H,QAAQ,CAAC0H,QAA3B,IAAuCtH,IAAI,KAAKlL,IAAI,CAACkL,IAA1D;AACA+L,MAAE,GAAGjX,IAAI,CAACyS,MAAL,KAAgByE,KAArB;AACD;;AAEDlX,MAAI,CAACmX,OAAL,GAAeH,EAAf;AACAhX,MAAI,CAACoX,OAAL,GAAeH,EAAf;AACAF,KAAG,GAAG,IAAIL,cAAJ,CAAmB1W,IAAnB,CAAN;;AAEA,MAAI,UAAU+W,GAAV,IAAiB,CAAC/W,IAAI,CAACqX,UAA3B,EAAuC;AACrC,WAAO,IAAIV,GAAJ,CAAQ3W,IAAR,CAAP;AACD,GAFD,MAEO;AACL,QAAI,CAACgT,KAAL,EAAY,MAAM,IAAI9N,KAAJ,CAAU,gBAAV,CAAN;AACZ,WAAO,IAAI0R,KAAJ,CAAU5W,IAAV,CAAP;AACD;AACF,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5CD,IAAMsX,OAAO,GAAG9X,mBAAO,CAAC,4EAAD,CAAvB;;AACA,IAAM+X,UAAU,GAAG/X,mBAAO,CAAC,gFAAD,CAA1B;;AAEA,IAAMgY,QAAQ,GAAG,KAAjB;AACA,IAAMC,eAAe,GAAG,MAAxB;AAEA;AACA;AACA;;AAEA,IAAIpL,SAAJ;;IAEMqL,Y;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,wBAAY1X,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;AAEA,UAAKW,KAAL,GAAa,MAAKA,KAAL,IAAc,EAA3B,CAHgB,CAKhB;AACA;;AACA,QAAI,CAAC0L,SAAL,EAAgB;AACd;AACAA,eAAS,GAAGkL,UAAU,CAACI,MAAX,GAAoBJ,UAAU,CAACI,MAAX,IAAqB,EAArD;AACD,KAVe,CAYhB;;;AACA,UAAKjJ,KAAL,GAAarC,SAAS,CAACjJ,MAAvB,CAbgB,CAehB;;AACAiJ,aAAS,CAAChH,IAAV,CAAe,MAAKuS,MAAL,CAAYnS,IAAZ,+BAAf,EAhBgB,CAkBhB;;AACA,UAAK9E,KAAL,CAAW6O,CAAX,GAAe,MAAKd,KAApB;AAnBgB;AAoBjB;AAED;AACF;AACA;;;;;SACE,eAAqB;AACnB,aAAO,KAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AACR,UAAI,KAAKmJ,MAAT,EAAiB;AACf;AACA,aAAKA,MAAL,CAAYlS,OAAZ,GAAsB,YAAM,CAAE,CAA9B;;AACA,aAAKkS,MAAL,CAAYC,UAAZ,CAAuBC,WAAvB,CAAmC,KAAKF,MAAxC;AACA,aAAKA,MAAL,GAAc,IAAd;AACD;;AAED,UAAI,KAAKG,IAAT,EAAe;AACb,aAAKA,IAAL,CAAUF,UAAV,CAAqBC,WAArB,CAAiC,KAAKC,IAAtC;AACA,aAAKA,IAAL,GAAY,IAAZ;AACA,aAAKC,MAAL,GAAc,IAAd;AACD;;AAED;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AAAA;;AACP,UAAMJ,MAAM,GAAGlK,QAAQ,CAACuK,aAAT,CAAuB,QAAvB,CAAf;;AAEA,UAAI,KAAKL,MAAT,EAAiB;AACf,aAAKA,MAAL,CAAYC,UAAZ,CAAuBC,WAAvB,CAAmC,KAAKF,MAAxC;AACA,aAAKA,MAAL,GAAc,IAAd;AACD;;AAEDA,YAAM,CAACM,KAAP,GAAe,IAAf;AACAN,YAAM,CAACO,GAAP,GAAa,KAAKrY,GAAL,EAAb;;AACA8X,YAAM,CAAClS,OAAP,GAAiB,UAAAwO,CAAC,EAAI;AACpB,cAAI,CAACK,OAAL,CAAa,kBAAb,EAAiCL,CAAjC;AACD,OAFD;;AAIA,UAAMkE,QAAQ,GAAG1K,QAAQ,CAAC2K,oBAAT,CAA8B,QAA9B,EAAwC,CAAxC,CAAjB;;AACA,UAAID,QAAJ,EAAc;AACZA,gBAAQ,CAACP,UAAT,CAAoBS,YAApB,CAAiCV,MAAjC,EAAyCQ,QAAzC;AACD,OAFD,MAEO;AACL,SAAC1K,QAAQ,CAAC6K,IAAT,IAAiB7K,QAAQ,CAAC8K,IAA3B,EAAiCC,WAAjC,CAA6Cb,MAA7C;AACD;;AACD,WAAKA,MAAL,GAAcA,MAAd;AAEA,UAAMc,SAAS,GACb,gBAAgB,OAAOpL,SAAvB,IAAoC,SAAStC,IAAT,CAAcsC,SAAS,CAACC,SAAxB,CADtC;;AAGA,UAAImL,SAAJ,EAAe;AACb,aAAK5T,YAAL,CAAkB,YAAW;AAC3B,cAAMkT,MAAM,GAAGtK,QAAQ,CAACuK,aAAT,CAAuB,QAAvB,CAAf;AACAvK,kBAAQ,CAAC8K,IAAT,CAAcC,WAAd,CAA0BT,MAA1B;AACAtK,kBAAQ,CAAC8K,IAAT,CAAcV,WAAd,CAA0BE,MAA1B;AACD,SAJD,EAIG,GAJH;AAKD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;WACE,iBAAQnS,IAAR,EAAc7B,EAAd,EAAkB;AAAA;;AAChB,UAAIgU,MAAJ;;AAEA,UAAI,CAAC,KAAKD,IAAV,EAAgB;AACd,YAAMA,IAAI,GAAGrK,QAAQ,CAACuK,aAAT,CAAuB,MAAvB,CAAb;AACA,YAAMU,IAAI,GAAGjL,QAAQ,CAACuK,aAAT,CAAuB,UAAvB,CAAb;AACA,YAAM5X,EAAE,GAAI,KAAKuY,QAAL,GAAgB,gBAAgB,KAAKnK,KAAjD;AAEAsJ,YAAI,CAACc,SAAL,GAAiB,UAAjB;AACAd,YAAI,CAACnK,KAAL,CAAWkL,QAAX,GAAsB,UAAtB;AACAf,YAAI,CAACnK,KAAL,CAAWmL,GAAX,GAAiB,SAAjB;AACAhB,YAAI,CAACnK,KAAL,CAAWoL,IAAX,GAAkB,SAAlB;AACAjB,YAAI,CAACkB,MAAL,GAAc5Y,EAAd;AACA0X,YAAI,CAACmB,MAAL,GAAc,MAAd;AACAnB,YAAI,CAACoB,YAAL,CAAkB,gBAAlB,EAAoC,OAApC;AACAR,YAAI,CAAC7G,IAAL,GAAY,GAAZ;AACAiG,YAAI,CAACU,WAAL,CAAiBE,IAAjB;AACAjL,gBAAQ,CAAC8K,IAAT,CAAcC,WAAd,CAA0BV,IAA1B;AAEA,aAAKA,IAAL,GAAYA,IAAZ;AACA,aAAKY,IAAL,GAAYA,IAAZ;AACD;;AAED,WAAKZ,IAAL,CAAUqB,MAAV,GAAmB,KAAKtZ,GAAL,EAAnB;;AAEA,eAASuZ,QAAT,GAAoB;AAClBC,kBAAU;AACVtV,UAAE;AACH;;AAED,UAAMsV,UAAU,GAAG,SAAbA,UAAa,GAAM;AACvB,YAAI,MAAI,CAACtB,MAAT,EAAiB;AACf,cAAI;AACF,kBAAI,CAACD,IAAL,CAAUD,WAAV,CAAsB,MAAI,CAACE,MAA3B;AACD,WAFD,CAEE,OAAO9D,CAAP,EAAU;AACV,kBAAI,CAACK,OAAL,CAAa,oCAAb,EAAmDL,CAAnD;AACD;AACF;;AAED,YAAI;AACF;AACA,cAAMqF,IAAI,GAAG,sCAAsC,MAAI,CAACX,QAA3C,GAAsD,IAAnE;AACAZ,gBAAM,GAAGtK,QAAQ,CAACuK,aAAT,CAAuBsB,IAAvB,CAAT;AACD,SAJD,CAIE,OAAOrF,CAAP,EAAU;AACV8D,gBAAM,GAAGtK,QAAQ,CAACuK,aAAT,CAAuB,QAAvB,CAAT;AACAD,gBAAM,CAAClG,IAAP,GAAc,MAAI,CAAC8G,QAAnB;AACAZ,gBAAM,CAACG,GAAP,GAAa,cAAb;AACD;;AAEDH,cAAM,CAAC3X,EAAP,GAAY,MAAI,CAACuY,QAAjB;;AAEA,cAAI,CAACb,IAAL,CAAUU,WAAV,CAAsBT,MAAtB;;AACA,cAAI,CAACA,MAAL,GAAcA,MAAd;AACD,OAvBD;;AAyBAsB,gBAAU,GAvDM,CAyDhB;AACA;;AACAzT,UAAI,GAAGA,IAAI,CAAC8I,OAAL,CAAa6I,eAAb,EAA8B,MAA9B,CAAP;AACA,WAAKmB,IAAL,CAAU1Z,KAAV,GAAkB4G,IAAI,CAAC8I,OAAL,CAAa4I,QAAb,EAAuB,KAAvB,CAAlB;;AAEA,UAAI;AACF,aAAKQ,IAAL,CAAUyB,MAAV;AACD,OAFD,CAEE,OAAOtF,CAAP,EAAU,CAAE;;AAEd,UAAI,KAAK8D,MAAL,CAAYyB,WAAhB,EAA6B;AAC3B,aAAKzB,MAAL,CAAY0B,kBAAZ,GAAiC,YAAM;AACrC,cAAI,MAAI,CAAC1B,MAAL,CAAYvF,UAAZ,KAA2B,UAA/B,EAA2C;AACzC4G,oBAAQ;AACT;AACF,SAJD;AAKD,OAND,MAMO;AACL,aAAKrB,MAAL,CAAY2B,MAAZ,GAAqBN,QAArB;AACD;AACF;;;;EAnLwBhC,O;;AAsL3B3X,MAAM,CAACV,OAAP,GAAiByY,YAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMA;AAEA,IAAMhB,cAAc,GAAGlX,mBAAO,CAAC,iFAAD,CAA9B;;AACA,IAAM8X,OAAO,GAAG9X,mBAAO,CAAC,4EAAD,CAAvB;;AACA,IAAMkL,OAAO,GAAGlL,mBAAO,CAAC,oEAAD,CAAvB;;AACA,eAAwCA,mBAAO,CAAC,4DAAD,CAA/C;AAAA,IAAQqa,IAAR,YAAQA,IAAR;AAAA,IAAchY,qBAAd,YAAcA,qBAAd;;AACA,IAAM0V,UAAU,GAAG/X,mBAAO,CAAC,gFAAD,CAA1B;;AAEA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,8BAAjB,CAAd;AAEA;AACA;AACA;;;AAEA,SAASsa,KAAT,GAAiB,CAAE;;AAEnB,IAAMC,OAAO,GAAI,YAAW;AAC1B,MAAMhD,GAAG,GAAG,IAAIL,cAAJ,CAAmB;AAAES,WAAO,EAAE;AAAX,GAAnB,CAAZ;AACA,SAAO,QAAQJ,GAAG,CAACiD,YAAnB;AACD,CAHe,EAAhB;;IAKMrD,G;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,eAAY3W,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;;AAEA,QAAI,OAAO8K,QAAP,KAAoB,WAAxB,EAAqC;AACnC,UAAMoM,KAAK,GAAG,aAAapM,QAAQ,CAACxL,QAApC;AACA,UAAI4L,IAAI,GAAGJ,QAAQ,CAACI,IAApB,CAFmC,CAInC;;AACA,UAAI,CAACA,IAAL,EAAW;AACTA,YAAI,GAAGgM,KAAK,GAAG,GAAH,GAAS,EAArB;AACD;;AAED,YAAKF,EAAL,GACG,OAAOlM,QAAP,KAAoB,WAApB,IACC9K,IAAI,CAACwS,QAAL,KAAkB1H,QAAQ,CAAC0H,QAD7B,IAEAtH,IAAI,KAAKlL,IAAI,CAACkL,IAHhB;AAIA,YAAK+L,EAAL,GAAUjX,IAAI,CAACyS,MAAL,KAAgByE,KAA1B;AACD;AACD;AACJ;AACA;;;AACI,QAAM+C,WAAW,GAAGja,IAAI,IAAIA,IAAI,CAACia,WAAjC;AACA,UAAKC,cAAL,GAAsBH,OAAO,IAAI,CAACE,WAAlC;AAtBgB;AAuBjB;AAED;AACF;AACA;AACA;AACA;AACA;;;;;WACE,mBAAmB;AAAA,UAAXja,IAAW,uEAAJ,EAAI;;AACjB,eAAcA,IAAd,EAAoB;AAAEgX,UAAE,EAAE,KAAKA,EAAX;AAAeC,UAAE,EAAE,KAAKA;AAAxB,OAApB,EAAkD,KAAKjX,IAAvD;;AACA,aAAO,IAAIma,OAAJ,CAAY,KAAKpa,GAAL,EAAZ,EAAwBC,IAAxB,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;WACE,iBAAQ8F,IAAR,EAAc7B,EAAd,EAAkB;AAAA;;AAChB,UAAMmW,GAAG,GAAG,KAAKC,OAAL,CAAa;AACvBlB,cAAM,EAAE,MADe;AAEvBrT,YAAI,EAAEA;AAFiB,OAAb,CAAZ;AAIAsU,SAAG,CAAC7V,EAAJ,CAAO,SAAP,EAAkBN,EAAlB;AACAmW,SAAG,CAAC7V,EAAJ,CAAO,OAAP,EAAgB,UAAAG,GAAG,EAAI;AACrB,cAAI,CAAC8P,OAAL,CAAa,gBAAb,EAA+B9P,GAA/B;AACD,OAFD;AAGD;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AAAA;;AACPhF,WAAK,CAAC,UAAD,CAAL;AACA,UAAM0a,GAAG,GAAG,KAAKC,OAAL,EAAZ;AACAD,SAAG,CAAC7V,EAAJ,CAAO,MAAP,EAAe,KAAKqT,MAAL,CAAYnS,IAAZ,CAAiB,IAAjB,CAAf;AACA2U,SAAG,CAAC7V,EAAJ,CAAO,OAAP,EAAgB,UAAAG,GAAG,EAAI;AACrB,cAAI,CAAC8P,OAAL,CAAa,gBAAb,EAA+B9P,GAA/B;AACD,OAFD;AAGA,WAAK4V,OAAL,GAAeF,GAAf;AACD;;;;EA1Ee9C,O;;IA6EZ6C,O;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,mBAAYpa,GAAZ,EAAiBC,IAAjB,EAAuB;AAAA;;AAAA;;AACrB;AACA6B,yBAAqB,iCAAO7B,IAAP,CAArB;AACA,WAAKA,IAAL,GAAYA,IAAZ;AAEA,WAAKmZ,MAAL,GAAcnZ,IAAI,CAACmZ,MAAL,IAAe,KAA7B;AACA,WAAKpZ,GAAL,GAAWA,GAAX;AACA,WAAKoY,KAAL,GAAa,UAAUnY,IAAI,CAACmY,KAA5B;AACA,WAAKrS,IAAL,GAAY7F,SAAS,KAAKD,IAAI,CAAC8F,IAAnB,GAA0B9F,IAAI,CAAC8F,IAA/B,GAAsC,IAAlD;;AAEA,WAAKyU,MAAL;;AAVqB;AAWtB;AAED;AACF;AACA;AACA;AACA;;;;;WACE,kBAAS;AAAA;;AACP,UAAMva,IAAI,GAAG6Z,IAAI,CACf,KAAK7Z,IADU,EAEf,OAFe,EAGf,YAHe,EAIf,KAJe,EAKf,KALe,EAMf,YANe,EAOf,MAPe,EAQf,IARe,EASf,SATe,EAUf,oBAVe,EAWf,WAXe,CAAjB;AAaAA,UAAI,CAACmX,OAAL,GAAe,CAAC,CAAC,KAAKnX,IAAL,CAAUgX,EAA3B;AACAhX,UAAI,CAACoX,OAAL,GAAe,CAAC,CAAC,KAAKpX,IAAL,CAAUiX,EAA3B;AAEA,UAAMF,GAAG,GAAI,KAAKA,GAAL,GAAW,IAAIL,cAAJ,CAAmB1W,IAAnB,CAAxB;;AAEA,UAAI;AACFN,aAAK,CAAC,iBAAD,EAAoB,KAAKyZ,MAAzB,EAAiC,KAAKpZ,GAAtC,CAAL;AACAgX,WAAG,CAAC9T,IAAJ,CAAS,KAAKkW,MAAd,EAAsB,KAAKpZ,GAA3B,EAAgC,KAAKoY,KAArC;;AACA,YAAI;AACF,cAAI,KAAKnY,IAAL,CAAUwa,YAAd,EAA4B;AAC1BzD,eAAG,CAAC0D,qBAAJ,IAA6B1D,GAAG,CAAC0D,qBAAJ,CAA0B,IAA1B,CAA7B;;AACA,iBAAK,IAAIlU,CAAT,IAAc,KAAKvG,IAAL,CAAUwa,YAAxB,EAAsC;AACpC,kBAAI,KAAKxa,IAAL,CAAUwa,YAAV,CAAuB9R,cAAvB,CAAsCnC,CAAtC,CAAJ,EAA8C;AAC5CwQ,mBAAG,CAAC2D,gBAAJ,CAAqBnU,CAArB,EAAwB,KAAKvG,IAAL,CAAUwa,YAAV,CAAuBjU,CAAvB,CAAxB;AACD;AACF;AACF;AACF,SATD,CASE,OAAO4N,CAAP,EAAU,CAAE;;AAEd,YAAI,WAAW,KAAKgF,MAApB,EAA4B;AAC1B,cAAI;AACFpC,eAAG,CAAC2D,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC;AACD,WAFD,CAEE,OAAOvG,CAAP,EAAU,CAAE;AACf;;AAED,YAAI;AACF4C,aAAG,CAAC2D,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B;AACD,SAFD,CAEE,OAAOvG,CAAP,EAAU,CAAE,CAtBZ,CAwBF;;;AACA,YAAI,qBAAqB4C,GAAzB,EAA8B;AAC5BA,aAAG,CAACjE,eAAJ,GAAsB,KAAK9S,IAAL,CAAU8S,eAAhC;AACD;;AAED,YAAI,KAAK9S,IAAL,CAAU2a,cAAd,EAA8B;AAC5B5D,aAAG,CAACvU,OAAJ,GAAc,KAAKxC,IAAL,CAAU2a,cAAxB;AACD;;AAED,YAAI,KAAKC,MAAL,EAAJ,EAAmB;AACjB7D,aAAG,CAAC6C,MAAJ,GAAa,YAAM;AACjB,kBAAI,CAACiB,MAAL;AACD,WAFD;;AAGA9D,aAAG,CAACpR,OAAJ,GAAc,YAAM;AAClB,kBAAI,CAAC6O,OAAL,CAAauC,GAAG,CAAC+D,YAAjB;AACD,WAFD;AAGD,SAPD,MAOO;AACL/D,aAAG,CAAC4C,kBAAJ,GAAyB,YAAM;AAC7B,gBAAI,MAAM5C,GAAG,CAACrE,UAAd,EAA0B;;AAC1B,gBAAI,QAAQqE,GAAG,CAACgE,MAAZ,IAAsB,SAAShE,GAAG,CAACgE,MAAvC,EAA+C;AAC7C,oBAAI,CAACF,MAAL;AACD,aAFD,MAEO;AACL;AACA;AACA,oBAAI,CAAC9V,YAAL,CAAkB,YAAM;AACtB,sBAAI,CAACyP,OAAL,CAAa,OAAOuC,GAAG,CAACgE,MAAX,KAAsB,QAAtB,GAAiChE,GAAG,CAACgE,MAArC,GAA8C,CAA3D;AACD,eAFD,EAEG,CAFH;AAGD;AACF,WAXD;AAYD;;AAEDrb,aAAK,CAAC,aAAD,EAAgB,KAAKoG,IAArB,CAAL;AACAiR,WAAG,CAACnC,IAAJ,CAAS,KAAK9O,IAAd;AACD,OAzDD,CAyDE,OAAOqO,CAAP,EAAU;AACV;AACA;AACA;AACA,aAAKpP,YAAL,CAAkB,YAAM;AACtB,gBAAI,CAACyP,OAAL,CAAaL,CAAb;AACD,SAFD,EAEG,CAFH;AAGA;AACD;;AAED,UAAI,OAAOxG,QAAP,KAAoB,WAAxB,EAAqC;AACnC,aAAKe,KAAL,GAAayL,OAAO,CAACa,aAAR,EAAb;AACAb,eAAO,CAACc,QAAR,CAAiB,KAAKvM,KAAtB,IAA+B,IAA/B;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,qBAAY;AACV,WAAKzJ,IAAL,CAAU,SAAV;AACA,WAAKN,OAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,gBAAOmB,IAAP,EAAa;AACX,WAAKb,IAAL,CAAU,MAAV,EAAkBa,IAAlB;AACA,WAAKoV,SAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQxW,GAAR,EAAa;AACX,WAAKO,IAAL,CAAU,OAAV,EAAmBP,GAAnB;AACA,WAAKC,OAAL,CAAa,IAAb;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQwW,SAAR,EAAmB;AACjB,UAAI,gBAAgB,OAAO,KAAKpE,GAA5B,IAAmC,SAAS,KAAKA,GAArD,EAA0D;AACxD;AACD,OAHgB,CAIjB;;;AACA,UAAI,KAAK6D,MAAL,EAAJ,EAAmB;AACjB,aAAK7D,GAAL,CAAS6C,MAAT,GAAkB,KAAK7C,GAAL,CAASpR,OAAT,GAAmBmU,KAArC;AACD,OAFD,MAEO;AACL,aAAK/C,GAAL,CAAS4C,kBAAT,GAA8BG,KAA9B;AACD;;AAED,UAAIqB,SAAJ,EAAe;AACb,YAAI;AACF,eAAKpE,GAAL,CAASqE,KAAT;AACD,SAFD,CAEE,OAAOjH,CAAP,EAAU,CAAE;AACf;;AAED,UAAI,OAAOxG,QAAP,KAAoB,WAAxB,EAAqC;AACnC,eAAOwM,OAAO,CAACc,QAAR,CAAiB,KAAKvM,KAAtB,CAAP;AACD;;AAED,WAAKqI,GAAL,GAAW,IAAX;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AACP,UAAMjR,IAAI,GAAG,KAAKiR,GAAL,CAAS+D,YAAtB;;AACA,UAAIhV,IAAI,KAAK,IAAb,EAAmB;AACjB,aAAK8R,MAAL,CAAY9R,IAAZ;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AACP,aAAO,OAAOuV,cAAP,KAA0B,WAA1B,IAAyC,CAAC,KAAKpE,EAA/C,IAAqD,KAAKqE,UAAjE;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQ;AACN,WAAK3W,OAAL;AACD;;;;EA5MmB+F,O;AA+MtB;AACA;AACA;AACA;AACA;;;AAEAyP,OAAO,CAACa,aAAR,GAAwB,CAAxB;AACAb,OAAO,CAACc,QAAR,GAAmB,EAAnB;;AAEA,IAAI,OAAOtN,QAAP,KAAoB,WAAxB,EAAqC;AACnC,MAAI,OAAO+L,WAAP,KAAuB,UAA3B,EAAuC;AACrCA,eAAW,CAAC,UAAD,EAAa6B,aAAb,CAAX;AACD,GAFD,MAEO,IAAI,OAAOvP,gBAAP,KAA4B,UAAhC,EAA4C;AACjD,QAAMwP,gBAAgB,GAAG,gBAAgBjE,UAAhB,GAA6B,UAA7B,GAA0C,QAAnE;AACAvL,oBAAgB,CAACwP,gBAAD,EAAmBD,aAAnB,EAAkC,KAAlC,CAAhB;AACD;AACF;;AAED,SAASA,aAAT,GAAyB;AACvB,OAAK,IAAIhV,CAAT,IAAc4T,OAAO,CAACc,QAAtB,EAAgC;AAC9B,QAAId,OAAO,CAACc,QAAR,CAAiBvS,cAAjB,CAAgCnC,CAAhC,CAAJ,EAAwC;AACtC4T,aAAO,CAACc,QAAR,CAAiB1U,CAAjB,EAAoB6U,KAApB;AACD;AACF;AACF;;AAEDzb,MAAM,CAACV,OAAP,GAAiB0X,GAAjB;AACAhX,MAAM,CAACV,OAAP,CAAekb,OAAf,GAAyBA,OAAzB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5UA,IAAM9H,SAAS,GAAG7S,mBAAO,CAAC,sEAAD,CAAzB;;AACA,IAAM+S,OAAO,GAAG/S,mBAAO,CAAC,gDAAD,CAAvB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAMic,KAAK,GAAGjc,mBAAO,CAAC,4CAAD,CAArB;;AAEA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,0BAAjB,CAAd;;IAEM8X,O;;;;;;;;;;;;;;AACJ;AACF;AACA;AACE,mBAAW;AACT,aAAO,SAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,kBAAS;AACP,WAAKoE,IAAL;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,eAAMC,OAAN,EAAe;AAAA;;AACb,WAAKjJ,UAAL,GAAkB,SAAlB;;AAEA,UAAMqC,KAAK,GAAG,SAARA,KAAQ,GAAM;AAClBrV,aAAK,CAAC,QAAD,CAAL;AACA,aAAI,CAACgT,UAAL,GAAkB,QAAlB;AACAiJ,eAAO;AACR,OAJD;;AAMA,UAAI,KAAK7E,OAAL,IAAgB,CAAC,KAAK5N,QAA1B,EAAoC;AAClC,YAAI0S,KAAK,GAAG,CAAZ;;AAEA,YAAI,KAAK9E,OAAT,EAAkB;AAChBpX,eAAK,CAAC,6CAAD,CAAL;AACAkc,eAAK;AACL,eAAK1P,IAAL,CAAU,cAAV,EAA0B,YAAW;AACnCxM,iBAAK,CAAC,4BAAD,CAAL;AACA,cAAEkc,KAAF,IAAW7G,KAAK,EAAhB;AACD,WAHD;AAID;;AAED,YAAI,CAAC,KAAK7L,QAAV,EAAoB;AAClBxJ,eAAK,CAAC,6CAAD,CAAL;AACAkc,eAAK;AACL,eAAK1P,IAAL,CAAU,OAAV,EAAmB,YAAW;AAC5BxM,iBAAK,CAAC,4BAAD,CAAL;AACA,cAAEkc,KAAF,IAAW7G,KAAK,EAAhB;AACD,WAHD;AAID;AACF,OApBD,MAoBO;AACLA,aAAK;AACN;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,gBAAO;AACLrV,WAAK,CAAC,SAAD,CAAL;AACA,WAAKoX,OAAL,GAAe,IAAf;AACA,WAAK+E,MAAL;AACA,WAAK5W,IAAL,CAAU,MAAV;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,gBAAOa,IAAP,EAAa;AAAA;;AACXpG,WAAK,CAAC,qBAAD,EAAwBoG,IAAxB,CAAL;;AACA,UAAMgW,QAAQ,GAAG,SAAXA,QAAW,CAAA9V,MAAM,EAAI;AACzB;AACA,YAAI,cAAc,MAAI,CAAC0M,UAAnB,IAAiC1M,MAAM,CAAC2C,IAAP,KAAgB,MAArD,EAA6D;AAC3D,gBAAI,CAACiN,MAAL;AACD,SAJwB,CAMzB;;;AACA,YAAI,YAAY5P,MAAM,CAAC2C,IAAvB,EAA6B;AAC3B,gBAAI,CAACmL,OAAL;;AACA,iBAAO,KAAP;AACD,SAVwB,CAYzB;;;AACA,cAAI,CAACS,QAAL,CAAcvO,MAAd;AACD,OAdD,CAFW,CAkBX;;;AACA1E,YAAM,CAACya,aAAP,CAAqBjW,IAArB,EAA2B,KAAKjF,MAAL,CAAY4V,UAAvC,EAAmD/P,OAAnD,CAA2DoV,QAA3D,EAnBW,CAqBX;;AACA,UAAI,aAAa,KAAKpJ,UAAtB,EAAkC;AAChC;AACA,aAAKoE,OAAL,GAAe,KAAf;AACA,aAAK7R,IAAL,CAAU,cAAV;;AAEA,YAAI,WAAW,KAAKyN,UAApB,EAAgC;AAC9B,eAAKgJ,IAAL;AACD,SAFD,MAEO;AACLhc,eAAK,CAAC,sCAAD,EAAyC,KAAKgT,UAA9C,CAAL;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AAAA;;AACR,UAAM1N,KAAK,GAAG,SAARA,KAAQ,GAAM;AAClBtF,aAAK,CAAC,sBAAD,CAAL;;AACA,cAAI,CAAC8G,KAAL,CAAW,CAAC;AAAEmC,cAAI,EAAE;AAAR,SAAD,CAAX;AACD,OAHD;;AAKA,UAAI,WAAW,KAAK+J,UAApB,EAAgC;AAC9BhT,aAAK,CAAC,0BAAD,CAAL;AACAsF,aAAK;AACN,OAHD,MAGO;AACL;AACA;AACAtF,aAAK,CAAC,sCAAD,CAAL;AACA,aAAKwM,IAAL,CAAU,MAAV,EAAkBlH,KAAlB;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;WACE,eAAMuR,OAAN,EAAe;AAAA;;AACb,WAAKrN,QAAL,GAAgB,KAAhB;AAEA5H,YAAM,CAAC0a,aAAP,CAAqBzF,OAArB,EAA8B,UAAAzQ,IAAI,EAAI;AACpC,cAAI,CAACmW,OAAL,CAAanW,IAAb,EAAmB,YAAM;AACvB,gBAAI,CAACoD,QAAL,GAAgB,IAAhB;;AACA,gBAAI,CAACjE,IAAL,CAAU,OAAV;AACD,SAHD;AAID,OALD;AAMD;AAED;AACF;AACA;AACA;AACA;;;;WACE,eAAM;AACJ,UAAItE,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;AACA,UAAMub,MAAM,GAAG,KAAKlc,IAAL,CAAUyS,MAAV,GAAmB,OAAnB,GAA6B,MAA5C;AACA,UAAIvH,IAAI,GAAG,EAAX,CAHI,CAKJ;;AACA,UAAI,UAAU,KAAKlL,IAAL,CAAUmc,iBAAxB,EAA2C;AACzCxb,aAAK,CAAC,KAAKX,IAAL,CAAUiT,cAAX,CAAL,GAAkCwI,KAAK,EAAvC;AACD;;AAED,UAAI,CAAC,KAAKvB,cAAN,IAAwB,CAACvZ,KAAK,CAAC2I,GAAnC,EAAwC;AACtC3I,aAAK,CAACyb,GAAN,GAAY,CAAZ;AACD;;AAEDzb,WAAK,GAAG4R,OAAO,CAACjM,MAAR,CAAe3F,KAAf,CAAR,CAdI,CAgBJ;;AACA,UACE,KAAKX,IAAL,CAAUkL,IAAV,KACE,YAAYgR,MAAZ,IAAsBtL,MAAM,CAAC,KAAK5Q,IAAL,CAAUkL,IAAX,CAAN,KAA2B,GAAlD,IACE,WAAWgR,MAAX,IAAqBtL,MAAM,CAAC,KAAK5Q,IAAL,CAAUkL,IAAX,CAAN,KAA2B,EAFnD,CADF,EAIE;AACAA,YAAI,GAAG,MAAM,KAAKlL,IAAL,CAAUkL,IAAvB;AACD,OAvBG,CAyBJ;;;AACA,UAAIvK,KAAK,CAACyC,MAAV,EAAkB;AAChBzC,aAAK,GAAG,MAAMA,KAAd;AACD;;AAED,UAAMwK,IAAI,GAAG,KAAKnL,IAAL,CAAUwS,QAAV,CAAmBtO,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;AACA,aACEgY,MAAM,GACN,KADA,IAEC/Q,IAAI,GAAG,MAAM,KAAKnL,IAAL,CAAUwS,QAAhB,GAA2B,GAA9B,GAAoC,KAAKxS,IAAL,CAAUwS,QAFnD,IAGAtH,IAHA,GAIA,KAAKlL,IAAL,CAAUI,IAJV,GAKAO,KANF;AAQD;;;;EAlMmB0R,S;;AAqMtB1S,MAAM,CAACV,OAAP,GAAiBqY,OAAjB,C;;;;;;;;;;;AC5MA,IAAMC,UAAU,GAAG/X,mBAAO,CAAC,gFAAD,CAA1B;;AACA,IAAM6c,QAAQ,GAAI,YAAM;AACtB,MAAMC,kBAAkB,GACtB,OAAOC,OAAP,KAAmB,UAAnB,IAAiC,OAAOA,OAAO,CAACC,OAAf,KAA2B,UAD9D;;AAEA,MAAIF,kBAAJ,EAAwB;AACtB,WAAO,UAAAhQ,EAAE;AAAA,aAAIiQ,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuBnQ,EAAvB,CAAJ;AAAA,KAAT;AACD,GAFD,MAEO;AACL,WAAO,UAACA,EAAD,EAAKvH,YAAL;AAAA,aAAsBA,YAAY,CAACuH,EAAD,EAAK,CAAL,CAAlC;AAAA,KAAP;AACD;AACF,CARgB,EAAjB;;AAUA3M,MAAM,CAACV,OAAP,GAAiB;AACfyd,WAAS,EAAEnF,UAAU,CAACmF,SAAX,IAAwBnF,UAAU,CAACoF,YAD/B;AAEfC,uBAAqB,EAAE,IAFR;AAGfC,mBAAiB,EAAE,aAHJ;AAIfR,UAAQ,EAARA;AAJe,CAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXA,IAAMhK,SAAS,GAAG7S,mBAAO,CAAC,sEAAD,CAAzB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAM+S,OAAO,GAAG/S,mBAAO,CAAC,gDAAD,CAAvB;;AACA,IAAMic,KAAK,GAAGjc,mBAAO,CAAC,4CAAD,CAArB;;AACA,eAAiBA,mBAAO,CAAC,4DAAD,CAAxB;AAAA,IAAQqa,IAAR,YAAQA,IAAR;;AACA,gBAKIra,mBAAO,CAAC,gHAAD,CALX;AAAA,IACEkd,SADF,aACEA,SADF;AAAA,IAEEE,qBAFF,aAEEA,qBAFF;AAAA,IAGEC,iBAHF,aAGEA,iBAHF;AAAA,IAIER,QAJF,aAIEA,QAJF;;AAOA,IAAM3c,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,4BAAjB,CAAd,C,CAEA;;;AACA,IAAMsd,aAAa,GACjB,OAAOvP,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACwP,OAAjB,KAA6B,QAD7B,IAEAxP,SAAS,CAACwP,OAAV,CAAkBtP,WAAlB,OAAoC,aAHtC;;IAKMuP,E;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,cAAYhd,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;AAEA,UAAKka,cAAL,GAAsB,CAACla,IAAI,CAACia,WAA5B;AAHgB;AAIjB;AAED;AACF;AACA;AACA;AACA;;;;;SACE,eAAW;AACT,aAAO,WAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,kBAAS;AACP,UAAI,CAAC,KAAKgD,KAAL,EAAL,EAAmB;AACjB;AACA;AACD;;AAED,UAAMld,GAAG,GAAG,KAAKA,GAAL,EAAZ;AACA,UAAMmd,SAAS,GAAG,KAAKld,IAAL,CAAUkd,SAA5B,CAPO,CASP;;AACA,UAAMld,IAAI,GAAG8c,aAAa,GACtB,EADsB,GAEtBjD,IAAI,CACF,KAAK7Z,IADH,EAEF,OAFE,EAGF,mBAHE,EAIF,KAJE,EAKF,KALE,EAMF,YANE,EAOF,MAPE,EAQF,IARE,EASF,SATE,EAUF,oBAVE,EAWF,cAXE,EAYF,iBAZE,EAaF,QAbE,EAcF,YAdE,EAeF,QAfE,EAgBF,qBAhBE,CAFR;;AAqBA,UAAI,KAAKA,IAAL,CAAUwa,YAAd,EAA4B;AAC1Bxa,YAAI,CAACmd,OAAL,GAAe,KAAKnd,IAAL,CAAUwa,YAAzB;AACD;;AAED,UAAI;AACF,aAAK4C,EAAL,GACER,qBAAqB,IAAI,CAACE,aAA1B,GACII,SAAS,GACP,IAAIR,SAAJ,CAAc3c,GAAd,EAAmBmd,SAAnB,CADO,GAEP,IAAIR,SAAJ,CAAc3c,GAAd,CAHN,GAII,IAAI2c,SAAJ,CAAc3c,GAAd,EAAmBmd,SAAnB,EAA8Bld,IAA9B,CALN;AAMD,OAPD,CAOE,OAAO0E,GAAP,EAAY;AACZ,eAAO,KAAKO,IAAL,CAAU,OAAV,EAAmBP,GAAnB,CAAP;AACD;;AAED,WAAK0Y,EAAL,CAAQ3G,UAAR,GAAqB,KAAK5V,MAAL,CAAY4V,UAAZ,IAA0BoG,iBAA/C;AAEA,WAAKQ,iBAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,6BAAoB;AAAA;;AAClB,WAAKD,EAAL,CAAQ5Y,MAAR,GAAiB,YAAM;AACrB,YAAI,MAAI,CAACxE,IAAL,CAAUmF,SAAd,EAAyB;AACvB,gBAAI,CAACiY,EAAL,CAAQE,OAAR,CAAgBlY,KAAhB;AACD;;AACD,cAAI,CAACwQ,MAAL;AACD,OALD;;AAMA,WAAKwH,EAAL,CAAQxX,OAAR,GAAkB,KAAKkO,OAAL,CAAarO,IAAb,CAAkB,IAAlB,CAAlB;;AACA,WAAK2X,EAAL,CAAQG,SAAR,GAAoB,UAAAnW,EAAE;AAAA,eAAI,MAAI,CAACwQ,MAAL,CAAYxQ,EAAE,CAACtB,IAAf,CAAJ;AAAA,OAAtB;;AACA,WAAKsX,EAAL,CAAQzX,OAAR,GAAkB,UAAAwO,CAAC;AAAA,eAAI,MAAI,CAACK,OAAL,CAAa,iBAAb,EAAgCL,CAAhC,CAAJ;AAAA,OAAnB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,eAAMoC,OAAN,EAAe;AAAA;;AACb,WAAKrN,QAAL,GAAgB,KAAhB,CADa,CAGb;AACA;;AAJa,iCAKJ3C,CALI;AAMX,YAAMP,MAAM,GAAGuQ,OAAO,CAAChQ,CAAD,CAAtB;AACA,YAAMiX,UAAU,GAAGjX,CAAC,KAAKgQ,OAAO,CAACnT,MAAR,GAAiB,CAA1C;AAEA9B,cAAM,CAACmc,YAAP,CAAoBzX,MAApB,EAA4B,MAAI,CAACkU,cAAjC,EAAiD,UAAApU,IAAI,EAAI;AACvD;AACA,cAAM9F,IAAI,GAAG,EAAb;;AACA,cAAI,CAAC4c,qBAAL,EAA4B;AAC1B,gBAAI5W,MAAM,CAACS,OAAX,EAAoB;AAClBzG,kBAAI,CAAC8I,QAAL,GAAgB9C,MAAM,CAACS,OAAP,CAAeqC,QAA/B;AACD;;AAED,gBAAI,MAAI,CAAC9I,IAAL,CAAUoT,iBAAd,EAAiC;AAC/B,kBAAM5G,GAAG,GACP,aAAa,OAAO1G,IAApB,GAA2B4X,MAAM,CAACC,UAAP,CAAkB7X,IAAlB,CAA3B,GAAqDA,IAAI,CAAC1C,MAD5D;;AAEA,kBAAIoJ,GAAG,GAAG,MAAI,CAACxM,IAAL,CAAUoT,iBAAV,CAA4BC,SAAtC,EAAiD;AAC/CrT,oBAAI,CAAC8I,QAAL,GAAgB,KAAhB;AACD;AACF;AACF,WAfsD,CAiBvD;AACA;AACA;;;AACA,cAAI;AACF,gBAAI8T,qBAAJ,EAA2B;AACzB;AACA,oBAAI,CAACQ,EAAL,CAAQxI,IAAR,CAAa9O,IAAb;AACD,aAHD,MAGO;AACL,oBAAI,CAACsX,EAAL,CAAQxI,IAAR,CAAa9O,IAAb,EAAmB9F,IAAnB;AACD;AACF,WAPD,CAOE,OAAOmU,CAAP,EAAU;AACVzU,iBAAK,CAAC,uCAAD,CAAL;AACD;;AAED,cAAI8d,UAAJ,EAAgB;AACd;AACA;AACAnB,oBAAQ,CAAC,YAAM;AACb,oBAAI,CAACnT,QAAL,GAAgB,IAAhB;;AACA,oBAAI,CAACjE,IAAL,CAAU,OAAV;AACD,aAHO,EAGL,MAAI,CAACF,YAHA,CAAR;AAID;AACF,SAvCD;AATW;;AAKb,WAAK,IAAIwB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgQ,OAAO,CAACnT,MAA5B,EAAoCmD,CAAC,EAArC,EAAyC;AAAA,cAAhCA,CAAgC;AA4CxC;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AACR8L,eAAS,CAAC9G,SAAV,CAAoBuI,OAApB,CAA4B5C,IAA5B,CAAiC,IAAjC;AACD;AAED;AACF;AACA;AACA;AACA;;;;WACE,mBAAU;AACR,UAAI,OAAO,KAAKkM,EAAZ,KAAmB,WAAvB,EAAoC;AAClC,aAAKA,EAAL,CAAQpY,KAAR;AACA,aAAKoY,EAAL,GAAU,IAAV;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;WACE,eAAM;AACJ,UAAIzc,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;AACA,UAAMub,MAAM,GAAG,KAAKlc,IAAL,CAAUyS,MAAV,GAAmB,KAAnB,GAA2B,IAA1C;AACA,UAAIvH,IAAI,GAAG,EAAX,CAHI,CAKJ;;AACA,UACE,KAAKlL,IAAL,CAAUkL,IAAV,KACE,UAAUgR,MAAV,IAAoBtL,MAAM,CAAC,KAAK5Q,IAAL,CAAUkL,IAAX,CAAN,KAA2B,GAAhD,IACE,SAASgR,MAAT,IAAmBtL,MAAM,CAAC,KAAK5Q,IAAL,CAAUkL,IAAX,CAAN,KAA2B,EAFjD,CADF,EAIE;AACAA,YAAI,GAAG,MAAM,KAAKlL,IAAL,CAAUkL,IAAvB;AACD,OAZG,CAcJ;;;AACA,UAAI,KAAKlL,IAAL,CAAUmc,iBAAd,EAAiC;AAC/Bxb,aAAK,CAAC,KAAKX,IAAL,CAAUiT,cAAX,CAAL,GAAkCwI,KAAK,EAAvC;AACD,OAjBG,CAmBJ;;;AACA,UAAI,CAAC,KAAKvB,cAAV,EAA0B;AACxBvZ,aAAK,CAACyb,GAAN,GAAY,CAAZ;AACD;;AAEDzb,WAAK,GAAG4R,OAAO,CAACjM,MAAR,CAAe3F,KAAf,CAAR,CAxBI,CA0BJ;;AACA,UAAIA,KAAK,CAACyC,MAAV,EAAkB;AAChBzC,aAAK,GAAG,MAAMA,KAAd;AACD;;AAED,UAAMwK,IAAI,GAAG,KAAKnL,IAAL,CAAUwS,QAAV,CAAmBtO,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;AACA,aACEgY,MAAM,GACN,KADA,IAEC/Q,IAAI,GAAG,MAAM,KAAKnL,IAAL,CAAUwS,QAAhB,GAA2B,GAA9B,GAAoC,KAAKxS,IAAL,CAAUwS,QAFnD,IAGAtH,IAHA,GAIA,KAAKlL,IAAL,CAAUI,IAJV,GAKAO,KANF;AAQD;AAED;AACF;AACA;AACA;AACA;AACA;;;;WACE,iBAAQ;AACN,aACE,CAAC,CAAC+b,SAAF,IACA,EAAE,kBAAkBA,SAAlB,IAA+B,KAAK3K,IAAL,KAAciL,EAAE,CAACzR,SAAH,CAAawG,IAA5D,CAFF;AAID;;;;EAxOcM,S;;AA2OjB1S,MAAM,CAACV,OAAP,GAAiB+d,EAAjB,C;;;;;;;;;;;AC/PA,IAAMzF,UAAU,GAAG/X,mBAAO,CAAC,+EAAD,CAA1B;;AAEAG,MAAM,CAACV,OAAP,CAAe4a,IAAf,GAAsB,UAAC1S,GAAD,EAAkB;AAAA,oCAATyW,IAAS;AAATA,QAAS;AAAA;;AACtC,SAAOA,IAAI,CAACC,MAAL,CAAY,UAACC,GAAD,EAAMC,CAAN,EAAY;AAC7B,QAAI5W,GAAG,CAACuB,cAAJ,CAAmBqV,CAAnB,CAAJ,EAA2B;AACzBD,SAAG,CAACC,CAAD,CAAH,GAAS5W,GAAG,CAAC4W,CAAD,CAAZ;AACD;;AACD,WAAOD,GAAP;AACD,GALM,EAKJ,EALI,CAAP;AAMD,CAPD,C,CASA;;;AACA,IAAME,kBAAkB,GAAGC,UAA3B;AACA,IAAMC,oBAAoB,GAAG3Y,YAA7B;;AAEA5F,MAAM,CAACV,OAAP,CAAe4C,qBAAf,GAAuC,UAACsF,GAAD,EAAMnH,IAAN,EAAe;AACpD,MAAIA,IAAI,CAACme,eAAT,EAA0B;AACxBhX,OAAG,CAACpC,YAAJ,GAAmBiZ,kBAAkB,CAACvY,IAAnB,CAAwB8R,UAAxB,CAAnB;AACApQ,OAAG,CAAC0O,cAAJ,GAAqBqI,oBAAoB,CAACzY,IAArB,CAA0B8R,UAA1B,CAArB;AACD,GAHD,MAGO;AACLpQ,OAAG,CAACpC,YAAJ,GAAmBkZ,UAAU,CAACxY,IAAX,CAAgB8R,UAAhB,CAAnB;AACApQ,OAAG,CAAC0O,cAAJ,GAAqBtQ,YAAY,CAACE,IAAb,CAAkB8R,UAAlB,CAArB;AACD;AACF,CARD,C;;;;;;;;;;;ACfA;AAEA,IAAM6G,OAAO,GAAG5e,mBAAO,CAAC,kDAAD,CAAvB;;AACA,IAAM+X,UAAU,GAAG/X,mBAAO,CAAC,+EAAD,CAA1B;;AAEAG,MAAM,CAACV,OAAP,GAAiB,UAASe,IAAT,EAAe;AAC9B,MAAMmX,OAAO,GAAGnX,IAAI,CAACmX,OAArB,CAD8B,CAG9B;AACA;;AACA,MAAMC,OAAO,GAAGpX,IAAI,CAACoX,OAArB,CAL8B,CAO9B;AACA;;AACA,MAAMkE,UAAU,GAAGtb,IAAI,CAACsb,UAAxB,CAT8B,CAW9B;;AACA,MAAI;AACF,QAAI,gBAAgB,OAAO5E,cAAvB,KAA0C,CAACS,OAAD,IAAYiH,OAAtD,CAAJ,EAAoE;AAClE,aAAO,IAAI1H,cAAJ,EAAP;AACD;AACF,GAJD,CAIE,OAAOvC,CAAP,EAAU,CAAE,CAhBgB,CAkB9B;AACA;AACA;;;AACA,MAAI;AACF,QAAI,gBAAgB,OAAOkH,cAAvB,IAAyC,CAACjE,OAA1C,IAAqDkE,UAAzD,EAAqE;AACnE,aAAO,IAAID,cAAJ,EAAP;AACD;AACF,GAJD,CAIE,OAAOlH,CAAP,EAAU,CAAE;;AAEd,MAAI,CAACgD,OAAL,EAAc;AACZ,QAAI;AACF,aAAO,IAAII,UAAU,CAAC,CAAC,QAAD,EAAW8G,MAAX,CAAkB,QAAlB,EAA4BvM,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CACL,mBADK,CAAP;AAGD,KAJD,CAIE,OAAOqC,CAAP,EAAU,CAAE;AACf;AACF,CAlCD,C;;;;;;;;;;;ACLA,IAAMmK,YAAY,GAAGvf,MAAM,CAACwb,MAAP,CAAc,IAAd,CAArB,C,CAA0C;;AAC1C+D,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AAEA,IAAMC,oBAAoB,GAAGxf,MAAM,CAACwb,MAAP,CAAc,IAAd,CAA7B;AACAxb,MAAM,CAACmH,IAAP,CAAYoY,YAAZ,EAA0B5X,OAA1B,CAAkC,UAAAqF,GAAG,EAAI;AACvCwS,sBAAoB,CAACD,YAAY,CAACvS,GAAD,CAAb,CAApB,GAA0CA,GAA1C;AACD,CAFD;AAIA,IAAMyS,YAAY,GAAG;AAAE7V,MAAI,EAAE,OAAR;AAAiB7C,MAAI,EAAE;AAAvB,CAArB;AAEAnG,MAAM,CAACV,OAAP,GAAiB;AACfqf,cAAY,EAAZA,YADe;AAEfC,sBAAoB,EAApBA,oBAFe;AAGfC,cAAY,EAAZA;AAHe,CAAjB,C;;;;;;;;;;;AChBA,eAA+Chf,mBAAO,CAAC,iEAAD,CAAtD;AAAA,IAAQ+e,oBAAR,YAAQA,oBAAR;AAAA,IAA8BC,YAA9B,YAA8BA,YAA9B;;AAEA,IAAMC,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;AAEA,IAAIC,aAAJ;;AACA,IAAIF,qBAAJ,EAA2B;AACzBE,eAAa,GAAGnf,mBAAO,CAAC,qHAAD,CAAvB;AACD;;AAED,IAAMgX,YAAY,GAAG,SAAfA,YAAe,CAACoI,aAAD,EAAgBnI,UAAhB,EAA+B;AAClD,MAAI,OAAOmI,aAAP,KAAyB,QAA7B,EAAuC;AACrC,WAAO;AACLjW,UAAI,EAAE,SADD;AAEL7C,UAAI,EAAE+Y,SAAS,CAACD,aAAD,EAAgBnI,UAAhB;AAFV,KAAP;AAID;;AACD,MAAM9N,IAAI,GAAGiW,aAAa,CAAC5T,MAAd,CAAqB,CAArB,CAAb;;AACA,MAAIrC,IAAI,KAAK,GAAb,EAAkB;AAChB,WAAO;AACLA,UAAI,EAAE,SADD;AAEL7C,UAAI,EAAEgZ,kBAAkB,CAACF,aAAa,CAAC1M,SAAd,CAAwB,CAAxB,CAAD,EAA6BuE,UAA7B;AAFnB,KAAP;AAID;;AACD,MAAMsI,UAAU,GAAGR,oBAAoB,CAAC5V,IAAD,CAAvC;;AACA,MAAI,CAACoW,UAAL,EAAiB;AACf,WAAOP,YAAP;AACD;;AACD,SAAOI,aAAa,CAACxb,MAAd,GAAuB,CAAvB,GACH;AACEuF,QAAI,EAAE4V,oBAAoB,CAAC5V,IAAD,CAD5B;AAEE7C,QAAI,EAAE8Y,aAAa,CAAC1M,SAAd,CAAwB,CAAxB;AAFR,GADG,GAKH;AACEvJ,QAAI,EAAE4V,oBAAoB,CAAC5V,IAAD;AAD5B,GALJ;AAQD,CA1BD;;AA4BA,IAAMmW,kBAAkB,GAAG,SAArBA,kBAAqB,CAAChZ,IAAD,EAAO2Q,UAAP,EAAsB;AAC/C,MAAIkI,aAAJ,EAAmB;AACjB,QAAMK,OAAO,GAAGL,aAAa,CAACnL,MAAd,CAAqB1N,IAArB,CAAhB;AACA,WAAO+Y,SAAS,CAACG,OAAD,EAAUvI,UAAV,CAAhB;AACD,GAHD,MAGO;AACL,WAAO;AAAEwI,YAAM,EAAE,IAAV;AAAgBnZ,UAAI,EAAJA;AAAhB,KAAP,CADK,CAC0B;AAChC;AACF,CAPD;;AASA,IAAM+Y,SAAS,GAAG,SAAZA,SAAY,CAAC/Y,IAAD,EAAO2Q,UAAP,EAAsB;AACtC,UAAQA,UAAR;AACE,SAAK,MAAL;AACE,aAAO3Q,IAAI,YAAY4Y,WAAhB,GAA8B,IAAIQ,IAAJ,CAAS,CAACpZ,IAAD,CAAT,CAA9B,GAAiDA,IAAxD;;AACF,SAAK,aAAL;AACA;AACE,aAAOA,IAAP;AAAa;AALjB;AAOD,CARD;;AAUAnG,MAAM,CAACV,OAAP,GAAiBuX,YAAjB,C;;;;;;;;;;;ACxDA,eAAyBhX,mBAAO,CAAC,iEAAD,CAAhC;AAAA,IAAQ8e,YAAR,YAAQA,YAAR;;AAEA,IAAMa,cAAc,GAClB,OAAOD,IAAP,KAAgB,UAAhB,IACC,OAAOA,IAAP,KAAgB,WAAhB,IACCngB,MAAM,CAACwM,SAAP,CAAiB0G,QAAjB,CAA0Bf,IAA1B,CAA+BgO,IAA/B,MAAyC,0BAH7C;AAIA,IAAMT,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD,C,CAEA;;AACA,IAAMU,MAAM,GAAG,SAATA,MAAS,CAAAjY,GAAG,EAAI;AACpB,SAAO,OAAOuX,WAAW,CAACU,MAAnB,KAA8B,UAA9B,GACHV,WAAW,CAACU,MAAZ,CAAmBjY,GAAnB,CADG,GAEHA,GAAG,IAAIA,GAAG,CAACkY,MAAJ,YAAsBX,WAFjC;AAGD,CAJD;;AAMA,IAAMjB,YAAY,GAAG,SAAfA,YAAe,OAAiBvD,cAAjB,EAAiC4B,QAAjC,EAA8C;AAAA,MAA3CnT,IAA2C,QAA3CA,IAA2C;AAAA,MAArC7C,IAAqC,QAArCA,IAAqC;;AACjE,MAAIqZ,cAAc,IAAIrZ,IAAI,YAAYoZ,IAAtC,EAA4C;AAC1C,QAAIhF,cAAJ,EAAoB;AAClB,aAAO4B,QAAQ,CAAChW,IAAD,CAAf;AACD,KAFD,MAEO;AACL,aAAOwZ,kBAAkB,CAACxZ,IAAD,EAAOgW,QAAP,CAAzB;AACD;AACF,GAND,MAMO,IACL2C,qBAAqB,KACpB3Y,IAAI,YAAY4Y,WAAhB,IAA+BU,MAAM,CAACtZ,IAAD,CADjB,CADhB,EAGL;AACA,QAAIoU,cAAJ,EAAoB;AAClB,aAAO4B,QAAQ,CAAChW,IAAI,YAAY4Y,WAAhB,GAA8B5Y,IAA9B,GAAqCA,IAAI,CAACuZ,MAA3C,CAAf;AACD,KAFD,MAEO;AACL,aAAOC,kBAAkB,CAAC,IAAIJ,IAAJ,CAAS,CAACpZ,IAAD,CAAT,CAAD,EAAmBgW,QAAnB,CAAzB;AACD;AACF,GAhBgE,CAiBjE;;;AACA,SAAOA,QAAQ,CAACwC,YAAY,CAAC3V,IAAD,CAAZ,IAAsB7C,IAAI,IAAI,EAA9B,CAAD,CAAf;AACD,CAnBD;;AAqBA,IAAMwZ,kBAAkB,GAAG,SAArBA,kBAAqB,CAACxZ,IAAD,EAAOgW,QAAP,EAAoB;AAC7C,MAAMyD,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;AACAD,YAAU,CAAC3F,MAAX,GAAoB,YAAW;AAC7B,QAAM6F,OAAO,GAAGF,UAAU,CAACG,MAAX,CAAkBhO,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAhB;AACAoK,YAAQ,CAAC,MAAM2D,OAAP,CAAR;AACD,GAHD;;AAIA,SAAOF,UAAU,CAACI,aAAX,CAAyB7Z,IAAzB,CAAP;AACD,CAPD;;AASAnG,MAAM,CAACV,OAAP,GAAiBwe,YAAjB,C;;;;;;;;;;;AC7CA,IAAMA,YAAY,GAAGje,mBAAO,CAAC,mFAAD,CAA5B;;AACA,IAAMgX,YAAY,GAAGhX,mBAAO,CAAC,mFAAD,CAA5B;;AAEA,IAAMogB,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB,C,CAA2C;;AAE3C,IAAM9D,aAAa,GAAG,SAAhBA,aAAgB,CAACzF,OAAD,EAAUuF,QAAV,EAAuB;AAC3C;AACA,MAAM1Y,MAAM,GAAGmT,OAAO,CAACnT,MAAvB;AACA,MAAMiD,cAAc,GAAG,IAAIkG,KAAJ,CAAUnJ,MAAV,CAAvB;AACA,MAAI2c,KAAK,GAAG,CAAZ;AAEAxJ,SAAO,CAAC7P,OAAR,CAAgB,UAACV,MAAD,EAASO,CAAT,EAAe;AAC7B;AACAkX,gBAAY,CAACzX,MAAD,EAAS,KAAT,EAAgB,UAAA4Y,aAAa,EAAI;AAC3CvY,oBAAc,CAACE,CAAD,CAAd,GAAoBqY,aAApB;;AACA,UAAI,EAAEmB,KAAF,KAAY3c,MAAhB,EAAwB;AACtB0Y,gBAAQ,CAACzV,cAAc,CAACyL,IAAf,CAAoB8N,SAApB,CAAD,CAAR;AACD;AACF,KALW,CAAZ;AAMD,GARD;AASD,CAfD;;AAiBA,IAAM7D,aAAa,GAAG,SAAhBA,aAAgB,CAACiE,cAAD,EAAiBvJ,UAAjB,EAAgC;AACpD,MAAMpQ,cAAc,GAAG2Z,cAAc,CAACtO,KAAf,CAAqBkO,SAArB,CAAvB;AACA,MAAMrJ,OAAO,GAAG,EAAhB;;AACA,OAAK,IAAIhQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAACjD,MAAnC,EAA2CmD,CAAC,EAA5C,EAAgD;AAC9C,QAAM0Z,aAAa,GAAGzJ,YAAY,CAACnQ,cAAc,CAACE,CAAD,CAAf,EAAoBkQ,UAApB,CAAlC;AACAF,WAAO,CAAClR,IAAR,CAAa4a,aAAb;;AACA,QAAIA,aAAa,CAACtX,IAAd,KAAuB,OAA3B,EAAoC;AAClC;AACD;AACF;;AACD,SAAO4N,OAAP;AACD,CAXD;;AAaA5W,MAAM,CAACV,OAAP,GAAiB;AACfK,UAAQ,EAAE,CADK;AAEfme,cAAY,EAAZA,YAFe;AAGfzB,eAAa,EAAbA,aAHe;AAIfxF,cAAY,EAAZA,YAJe;AAKfuF,eAAa,EAAbA;AALe,CAAjB,C;;;;;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASmE,KAAT,EAAe;AACd;;AAEAjhB,SAAO,CAACqH,MAAR,GAAiB,UAAS6Z,WAAT,EAAsB;AACrC,QAAIC,KAAK,GAAG,IAAIC,UAAJ,CAAeF,WAAf,CAAZ;AAAA,QACA5Z,CADA;AAAA,QACGiG,GAAG,GAAG4T,KAAK,CAAChd,MADf;AAAA,QACuB6b,MAAM,GAAG,EADhC;;AAGA,SAAK1Y,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGiG,GAAhB,EAAqBjG,CAAC,IAAE,CAAxB,EAA2B;AACzB0Y,YAAM,IAAIiB,KAAK,CAACE,KAAK,CAAC7Z,CAAD,CAAL,IAAY,CAAb,CAAf;AACA0Y,YAAM,IAAIiB,KAAK,CAAE,CAACE,KAAK,CAAC7Z,CAAD,CAAL,GAAW,CAAZ,KAAkB,CAAnB,GAAyB6Z,KAAK,CAAC7Z,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA1C,CAAf;AACA0Y,YAAM,IAAIiB,KAAK,CAAE,CAACE,KAAK,CAAC7Z,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,KAAuB,CAAxB,GAA8B6Z,KAAK,CAAC7Z,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA/C,CAAf;AACA0Y,YAAM,IAAIiB,KAAK,CAACE,KAAK,CAAC7Z,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,CAAf;AACD;;AAED,QAAKiG,GAAG,GAAG,CAAP,KAAc,CAAlB,EAAqB;AACnByS,YAAM,GAAGA,MAAM,CAAC/M,SAAP,CAAiB,CAAjB,EAAoB+M,MAAM,CAAC7b,MAAP,GAAgB,CAApC,IAAyC,GAAlD;AACD,KAFD,MAEO,IAAIoJ,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;AACxByS,YAAM,GAAGA,MAAM,CAAC/M,SAAP,CAAiB,CAAjB,EAAoB+M,MAAM,CAAC7b,MAAP,GAAgB,CAApC,IAAyC,IAAlD;AACD;;AAED,WAAO6b,MAAP;AACD,GAlBD;;AAoBAhgB,SAAO,CAACuU,MAAR,GAAkB,UAASyL,MAAT,EAAiB;AACjC,QAAIqB,YAAY,GAAGrB,MAAM,CAAC7b,MAAP,GAAgB,IAAnC;AAAA,QACAoJ,GAAG,GAAGyS,MAAM,CAAC7b,MADb;AAAA,QACqBmD,CADrB;AAAA,QACwBga,CAAC,GAAG,CAD5B;AAAA,QAEAC,QAFA;AAAA,QAEUC,QAFV;AAAA,QAEoBC,QAFpB;AAAA,QAE8BC,QAF9B;;AAIA,QAAI1B,MAAM,CAACA,MAAM,CAAC7b,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;AACrCkd,kBAAY;;AACZ,UAAIrB,MAAM,CAACA,MAAM,CAAC7b,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;AACrCkd,oBAAY;AACb;AACF;;AAED,QAAIH,WAAW,GAAG,IAAIzB,WAAJ,CAAgB4B,YAAhB,CAAlB;AAAA,QACAF,KAAK,GAAG,IAAIC,UAAJ,CAAeF,WAAf,CADR;;AAGA,SAAK5Z,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGiG,GAAhB,EAAqBjG,CAAC,IAAE,CAAxB,EAA2B;AACzBia,cAAQ,GAAGN,KAAK,CAAChc,OAAN,CAAc+a,MAAM,CAAC1Y,CAAD,CAApB,CAAX;AACAka,cAAQ,GAAGP,KAAK,CAAChc,OAAN,CAAc+a,MAAM,CAAC1Y,CAAC,GAAC,CAAH,CAApB,CAAX;AACAma,cAAQ,GAAGR,KAAK,CAAChc,OAAN,CAAc+a,MAAM,CAAC1Y,CAAC,GAAC,CAAH,CAApB,CAAX;AACAoa,cAAQ,GAAGT,KAAK,CAAChc,OAAN,CAAc+a,MAAM,CAAC1Y,CAAC,GAAC,CAAH,CAApB,CAAX;AAEA6Z,WAAK,CAACG,CAAC,EAAF,CAAL,GAAcC,QAAQ,IAAI,CAAb,GAAmBC,QAAQ,IAAI,CAA5C;AACAL,WAAK,CAACG,CAAC,EAAF,CAAL,GAAc,CAACE,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0BC,QAAQ,IAAI,CAAnD;AACAN,WAAK,CAACG,CAAC,EAAF,CAAL,GAAc,CAACG,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyBC,QAAQ,GAAG,EAAjD;AACD;;AAED,WAAOR,WAAP;AACD,GA3BD;AA4BD,CAnDD,EAmDG,kEAnDH,E;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAI;AACFxgB,QAAM,CAACV,OAAP,GAAiB,OAAOyX,cAAP,KAA0B,WAA1B,IACf,qBAAqB,IAAIA,cAAJ,EADvB;AAED,CAHD,CAGE,OAAOhS,GAAP,EAAY;AACZ;AACA;AACA/E,QAAM,CAACV,OAAP,GAAiB,KAAjB;AACD,C;;;;;;;;;;;;;AChBD;AACA;AACA;AAEA,IAAI2hB,CAAC,GAAG,IAAR;AACA,IAAIC,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAZ;AACA,IAAIE,CAAC,GAAGF,CAAC,GAAG,MAAZ;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAphB,MAAM,CAACV,OAAP,GAAiB,UAASgS,GAAT,EAAcxK,OAAd,EAAuB;AACtCA,SAAO,GAAGA,OAAO,IAAI,EAArB;;AACA,MAAIkC,IAAI,WAAUsI,GAAV,CAAR;;AACA,MAAItI,IAAI,KAAK,QAAT,IAAqBsI,GAAG,CAAC7N,MAAJ,GAAa,CAAtC,EAAyC;AACvC,WAAOmS,KAAK,CAACtE,GAAD,CAAZ;AACD,GAFD,MAEO,IAAItI,IAAI,KAAK,QAAT,IAAqBuY,QAAQ,CAACjQ,GAAD,CAAjC,EAAwC;AAC7C,WAAOxK,OAAO,QAAP,GAAe0a,OAAO,CAAClQ,GAAD,CAAtB,GAA8BmQ,QAAQ,CAACnQ,GAAD,CAA7C;AACD;;AACD,QAAM,IAAI/L,KAAJ,CACJ,0DACEuK,IAAI,CAACC,SAAL,CAAeuB,GAAf,CAFE,CAAN;AAID,CAZD;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASsE,KAAT,CAAe8L,GAAf,EAAoB;AAClBA,KAAG,GAAGxB,MAAM,CAACwB,GAAD,CAAZ;;AACA,MAAIA,GAAG,CAACje,MAAJ,GAAa,GAAjB,EAAsB;AACpB;AACD;;AACD,MAAIsK,KAAK,GAAG,mIAAmI4T,IAAnI,CACVD,GADU,CAAZ;;AAGA,MAAI,CAAC3T,KAAL,EAAY;AACV;AACD;;AACD,MAAI6T,CAAC,GAAGC,UAAU,CAAC9T,KAAK,CAAC,CAAD,CAAN,CAAlB;AACA,MAAI/E,IAAI,GAAG,CAAC+E,KAAK,CAAC,CAAD,CAAL,IAAY,IAAb,EAAmBD,WAAnB,EAAX;;AACA,UAAQ9E,IAAR;AACE,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,IAAL;AACA,SAAK,GAAL;AACE,aAAO4Y,CAAC,GAAGN,CAAX;;AACF,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,GAAL;AACE,aAAOM,CAAC,GAAGP,CAAX;;AACF,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOO,CAAC,GAAGR,CAAX;;AACF,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,IAAL;AACA,SAAK,GAAL;AACE,aAAOQ,CAAC,GAAGT,CAAX;;AACF,SAAK,SAAL;AACA,SAAK,QAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOS,CAAC,GAAGV,CAAX;;AACF,SAAK,SAAL;AACA,SAAK,QAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOU,CAAC,GAAGX,CAAX;;AACF,SAAK,cAAL;AACA,SAAK,aAAL;AACA,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,IAAL;AACE,aAAOW,CAAP;;AACF;AACE,aAAOthB,SAAP;AAxCJ;AA0CD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASmhB,QAAT,CAAkB/V,EAAlB,EAAsB;AACpB,MAAIoW,KAAK,GAAGjW,IAAI,CAAC8E,GAAL,CAASjF,EAAT,CAAZ;;AACA,MAAIoW,KAAK,IAAIV,CAAb,EAAgB;AACd,WAAOvV,IAAI,CAACkW,KAAL,CAAWrW,EAAE,GAAG0V,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIU,KAAK,IAAIX,CAAb,EAAgB;AACd,WAAOtV,IAAI,CAACkW,KAAL,CAAWrW,EAAE,GAAGyV,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIW,KAAK,IAAIZ,CAAb,EAAgB;AACd,WAAOrV,IAAI,CAACkW,KAAL,CAAWrW,EAAE,GAAGwV,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIY,KAAK,IAAIb,CAAb,EAAgB;AACd,WAAOpV,IAAI,CAACkW,KAAL,CAAWrW,EAAE,GAAGuV,CAAhB,IAAqB,GAA5B;AACD;;AACD,SAAOvV,EAAE,GAAG,IAAZ;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAAS8V,OAAT,CAAiB9V,EAAjB,EAAqB;AACnB,MAAIoW,KAAK,GAAGjW,IAAI,CAAC8E,GAAL,CAASjF,EAAT,CAAZ;;AACA,MAAIoW,KAAK,IAAIV,CAAb,EAAgB;AACd,WAAOY,MAAM,CAACtW,EAAD,EAAKoW,KAAL,EAAYV,CAAZ,EAAe,KAAf,CAAb;AACD;;AACD,MAAIU,KAAK,IAAIX,CAAb,EAAgB;AACd,WAAOa,MAAM,CAACtW,EAAD,EAAKoW,KAAL,EAAYX,CAAZ,EAAe,MAAf,CAAb;AACD;;AACD,MAAIW,KAAK,IAAIZ,CAAb,EAAgB;AACd,WAAOc,MAAM,CAACtW,EAAD,EAAKoW,KAAL,EAAYZ,CAAZ,EAAe,QAAf,CAAb;AACD;;AACD,MAAIY,KAAK,IAAIb,CAAb,EAAgB;AACd,WAAOe,MAAM,CAACtW,EAAD,EAAKoW,KAAL,EAAYb,CAAZ,EAAe,QAAf,CAAb;AACD;;AACD,SAAOvV,EAAE,GAAG,KAAZ;AACD;AAED;AACA;AACA;;;AAEA,SAASsW,MAAT,CAAgBtW,EAAhB,EAAoBoW,KAApB,EAA2BF,CAA3B,EAA8BxP,IAA9B,EAAoC;AAClC,MAAI6P,QAAQ,GAAGH,KAAK,IAAIF,CAAC,GAAG,GAA5B;AACA,SAAO/V,IAAI,CAACkW,KAAL,CAAWrW,EAAE,GAAGkW,CAAhB,IAAqB,GAArB,GAA2BxP,IAA3B,IAAmC6P,QAAQ,GAAG,GAAH,GAAS,EAApD,CAAP;AACD,C;;;;;;;;;;;ACjKD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA3iB,OAAO,CAACqH,MAAR,GAAiB,UAAUa,GAAV,EAAe;AAC9B,MAAIka,GAAG,GAAG,EAAV;;AAEA,OAAK,IAAI9a,CAAT,IAAcY,GAAd,EAAmB;AACjB,QAAIA,GAAG,CAACuB,cAAJ,CAAmBnC,CAAnB,CAAJ,EAA2B;AACzB,UAAI8a,GAAG,CAACje,MAAR,EAAgBie,GAAG,IAAI,GAAP;AAChBA,SAAG,IAAIQ,kBAAkB,CAACtb,CAAD,CAAlB,GAAwB,GAAxB,GAA8Bsb,kBAAkB,CAAC1a,GAAG,CAACZ,CAAD,CAAJ,CAAvD;AACD;AACF;;AAED,SAAO8a,GAAP;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;;AAEApiB,OAAO,CAACuU,MAAR,GAAiB,UAASsO,EAAT,EAAY;AAC3B,MAAIC,GAAG,GAAG,EAAV;AACA,MAAIC,KAAK,GAAGF,EAAE,CAACpQ,KAAH,CAAS,GAAT,CAAZ;;AACA,OAAK,IAAInL,CAAC,GAAG,CAAR,EAAW8O,CAAC,GAAG2M,KAAK,CAAC5e,MAA1B,EAAkCmD,CAAC,GAAG8O,CAAtC,EAAyC9O,CAAC,EAA1C,EAA8C;AAC5C,QAAI0b,IAAI,GAAGD,KAAK,CAACzb,CAAD,CAAL,CAASmL,KAAT,CAAe,GAAf,CAAX;AACAqQ,OAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD;AACD;;AACD,SAAOF,GAAP;AACD,CARD,C;;;;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAII,EAAE,GAAG,yOAAT;AAEA,IAAIC,KAAK,GAAG,CACR,QADQ,EACE,UADF,EACc,WADd,EAC2B,UAD3B,EACuC,MADvC,EAC+C,UAD/C,EAC2D,MAD3D,EACmE,MADnE,EAC2E,UAD3E,EACuF,MADvF,EAC+F,WAD/F,EAC4G,MAD5G,EACoH,OADpH,EAC6H,QAD7H,CAAZ;;AAIAziB,MAAM,CAACV,OAAP,GAAiB,SAAS2L,QAAT,CAAkByW,GAAlB,EAAuB;AACpC,MAAIjJ,GAAG,GAAGiJ,GAAV;AAAA,MACIgB,CAAC,GAAGhB,GAAG,CAACnd,OAAJ,CAAY,GAAZ,CADR;AAAA,MAEIiQ,CAAC,GAAGkN,GAAG,CAACnd,OAAJ,CAAY,GAAZ,CAFR;;AAIA,MAAIme,CAAC,IAAI,CAAC,CAAN,IAAWlO,CAAC,IAAI,CAAC,CAArB,EAAwB;AACpBkN,OAAG,GAAGA,GAAG,CAACnP,SAAJ,CAAc,CAAd,EAAiBmQ,CAAjB,IAAsBhB,GAAG,CAACnP,SAAJ,CAAcmQ,CAAd,EAAiBlO,CAAjB,EAAoBvF,OAApB,CAA4B,IAA5B,EAAkC,GAAlC,CAAtB,GAA+DyS,GAAG,CAACnP,SAAJ,CAAciC,CAAd,EAAiBkN,GAAG,CAACje,MAArB,CAArE;AACH;;AAED,MAAIyd,CAAC,GAAGsB,EAAE,CAACb,IAAH,CAAQD,GAAG,IAAI,EAAf,CAAR;AAAA,MACIthB,GAAG,GAAG,EADV;AAAA,MAEIwG,CAAC,GAAG,EAFR;;AAIA,SAAOA,CAAC,EAAR,EAAY;AACRxG,OAAG,CAACqiB,KAAK,CAAC7b,CAAD,CAAN,CAAH,GAAgBsa,CAAC,CAACta,CAAD,CAAD,IAAQ,EAAxB;AACH;;AAED,MAAI8b,CAAC,IAAI,CAAC,CAAN,IAAWlO,CAAC,IAAI,CAAC,CAArB,EAAwB;AACpBpU,OAAG,CAACM,MAAJ,GAAa+X,GAAb;AACArY,OAAG,CAACgL,IAAJ,GAAWhL,GAAG,CAACgL,IAAJ,CAASmH,SAAT,CAAmB,CAAnB,EAAsBnS,GAAG,CAACgL,IAAJ,CAAS3H,MAAT,GAAkB,CAAxC,EAA2CwL,OAA3C,CAAmD,IAAnD,EAAyD,GAAzD,CAAX;AACA7O,OAAG,CAACuiB,SAAJ,GAAgBviB,GAAG,CAACuiB,SAAJ,CAAc1T,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,EAA+BA,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,EAAgDA,OAAhD,CAAwD,IAAxD,EAA8D,GAA9D,CAAhB;AACA7O,OAAG,CAACwiB,OAAJ,GAAc,IAAd;AACH;;AAEDxiB,KAAG,CAACyiB,SAAJ,GAAgBA,SAAS,CAACziB,GAAD,EAAMA,GAAG,CAAC,MAAD,CAAT,CAAzB;AACAA,KAAG,CAACa,QAAJ,GAAeA,QAAQ,CAACb,GAAD,EAAMA,GAAG,CAAC,OAAD,CAAT,CAAvB;AAEA,SAAOA,GAAP;AACH,CA5BD;;AA8BA,SAASyiB,SAAT,CAAmBrb,GAAnB,EAAwB/G,IAAxB,EAA8B;AAC1B,MAAIqiB,IAAI,GAAG,UAAX;AAAA,MACIxS,KAAK,GAAG7P,IAAI,CAACwO,OAAL,CAAa6T,IAAb,EAAmB,GAAnB,EAAwB/Q,KAAxB,CAA8B,GAA9B,CADZ;;AAGA,MAAItR,IAAI,CAACuR,MAAL,CAAY,CAAZ,EAAe,CAAf,KAAqB,GAArB,IAA4BvR,IAAI,CAACgD,MAAL,KAAgB,CAAhD,EAAmD;AAC/C6M,SAAK,CAACxF,MAAN,CAAa,CAAb,EAAgB,CAAhB;AACH;;AACD,MAAIrK,IAAI,CAACuR,MAAL,CAAYvR,IAAI,CAACgD,MAAL,GAAc,CAA1B,EAA6B,CAA7B,KAAmC,GAAvC,EAA4C;AACxC6M,SAAK,CAACxF,MAAN,CAAawF,KAAK,CAAC7M,MAAN,GAAe,CAA5B,EAA+B,CAA/B;AACH;;AAED,SAAO6M,KAAP;AACH;;AAED,SAASrP,QAAT,CAAkBb,GAAlB,EAAuBY,KAAvB,EAA8B;AAC1B,MAAImF,IAAI,GAAG,EAAX;AAEAnF,OAAK,CAACiO,OAAN,CAAc,2BAAd,EAA2C,UAAU8T,EAAV,EAActU,EAAd,EAAkBuU,EAAlB,EAAsB;AAC7D,QAAIvU,EAAJ,EAAQ;AACJtI,UAAI,CAACsI,EAAD,CAAJ,GAAWuU,EAAX;AACH;AACJ,GAJD;AAMA,SAAO7c,IAAP;AACH,C;;;;;;;;;;;;ACnEY;;;;AACb/G,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAAC2jB,iBAAR,GAA4B3jB,OAAO,CAAC4jB,iBAAR,GAA4B,KAAK,CAA7D;;AACA,IAAMC,WAAW,GAAGtjB,mBAAO,CAAC,sEAAD,CAA3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASqjB,iBAAT,CAA2B7c,MAA3B,EAAmC;AAC/B,MAAM+c,OAAO,GAAG,EAAhB;AACA,MAAMC,UAAU,GAAGhd,MAAM,CAACF,IAA1B;AACA,MAAMmd,IAAI,GAAGjd,MAAb;AACAid,MAAI,CAACnd,IAAL,GAAYod,kBAAkB,CAACF,UAAD,EAAaD,OAAb,CAA9B;AACAE,MAAI,CAACE,WAAL,GAAmBJ,OAAO,CAAC3f,MAA3B,CAL+B,CAKI;;AACnC,SAAO;AAAE4C,UAAM,EAAEid,IAAV;AAAgBF,WAAO,EAAEA;AAAzB,GAAP;AACH;;AACD9jB,OAAO,CAAC4jB,iBAAR,GAA4BA,iBAA5B;;AACA,SAASK,kBAAT,CAA4Bpd,IAA5B,EAAkCid,OAAlC,EAA2C;AACvC,MAAI,CAACjd,IAAL,EACI,OAAOA,IAAP;;AACJ,MAAIgd,WAAW,CAACM,QAAZ,CAAqBtd,IAArB,CAAJ,EAAgC;AAC5B,QAAMud,WAAW,GAAG;AAAEC,kBAAY,EAAE,IAAhB;AAAsBC,SAAG,EAAER,OAAO,CAAC3f;AAAnC,KAApB;AACA2f,WAAO,CAAC1d,IAAR,CAAaS,IAAb;AACA,WAAOud,WAAP;AACH,GAJD,MAKK,IAAI9W,KAAK,CAACiX,OAAN,CAAc1d,IAAd,CAAJ,EAAyB;AAC1B,QAAM2d,OAAO,GAAG,IAAIlX,KAAJ,CAAUzG,IAAI,CAAC1C,MAAf,CAAhB;;AACA,SAAK,IAAImD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,IAAI,CAAC1C,MAAzB,EAAiCmD,CAAC,EAAlC,EAAsC;AAClCkd,aAAO,CAACld,CAAD,CAAP,GAAa2c,kBAAkB,CAACpd,IAAI,CAACS,CAAD,CAAL,EAAUwc,OAAV,CAA/B;AACH;;AACD,WAAOU,OAAP;AACH,GANI,MAOA,IAAI,QAAO3d,IAAP,MAAgB,QAAhB,IAA4B,EAAEA,IAAI,YAAY+K,IAAlB,CAAhC,EAAyD;AAC1D,QAAM4S,QAAO,GAAG,EAAhB;;AACA,SAAK,IAAM1X,GAAX,IAAkBjG,IAAlB,EAAwB;AACpB,UAAIA,IAAI,CAAC4C,cAAL,CAAoBqD,GAApB,CAAJ,EAA8B;AAC1B0X,gBAAO,CAAC1X,GAAD,CAAP,GAAemX,kBAAkB,CAACpd,IAAI,CAACiG,GAAD,CAAL,EAAYgX,OAAZ,CAAjC;AACH;AACJ;;AACD,WAAOU,QAAP;AACH;;AACD,SAAO3d,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS8c,iBAAT,CAA2B5c,MAA3B,EAAmC+c,OAAnC,EAA4C;AACxC/c,QAAM,CAACF,IAAP,GAAc4d,kBAAkB,CAAC1d,MAAM,CAACF,IAAR,EAAcid,OAAd,CAAhC;AACA/c,QAAM,CAACmd,WAAP,GAAqBljB,SAArB,CAFwC,CAER;;AAChC,SAAO+F,MAAP;AACH;;AACD/G,OAAO,CAAC2jB,iBAAR,GAA4BA,iBAA5B;;AACA,SAASc,kBAAT,CAA4B5d,IAA5B,EAAkCid,OAAlC,EAA2C;AACvC,MAAI,CAACjd,IAAL,EACI,OAAOA,IAAP;;AACJ,MAAIA,IAAI,IAAIA,IAAI,CAACwd,YAAjB,EAA+B;AAC3B,WAAOP,OAAO,CAACjd,IAAI,CAACyd,GAAN,CAAd,CAD2B,CACD;AAC7B,GAFD,MAGK,IAAIhX,KAAK,CAACiX,OAAN,CAAc1d,IAAd,CAAJ,EAAyB;AAC1B,SAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,IAAI,CAAC1C,MAAzB,EAAiCmD,CAAC,EAAlC,EAAsC;AAClCT,UAAI,CAACS,CAAD,CAAJ,GAAUmd,kBAAkB,CAAC5d,IAAI,CAACS,CAAD,CAAL,EAAUwc,OAAV,CAA5B;AACH;AACJ,GAJI,MAKA,IAAI,QAAOjd,IAAP,MAAgB,QAApB,EAA8B;AAC/B,SAAK,IAAMiG,GAAX,IAAkBjG,IAAlB,EAAwB;AACpB,UAAIA,IAAI,CAAC4C,cAAL,CAAoBqD,GAApB,CAAJ,EAA8B;AAC1BjG,YAAI,CAACiG,GAAD,CAAJ,GAAY2X,kBAAkB,CAAC5d,IAAI,CAACiG,GAAD,CAAL,EAAYgX,OAAZ,CAA9B;AACH;AACJ;AACJ;;AACD,SAAOjd,IAAP;AACH,C;;;;;;;;;;;;AC/EY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACb/G,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAAC6D,OAAR,GAAkB7D,OAAO,CAAC2D,OAAR,GAAkB3D,OAAO,CAAC2J,UAAR,GAAqB3J,OAAO,CAACK,QAAR,GAAmB,KAAK,CAAjF;;AACA,IAAMoL,OAAO,GAAGlL,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAMmkB,QAAQ,GAAGnkB,mBAAO,CAAC,gEAAD,CAAxB;;AACA,IAAMsjB,WAAW,GAAGtjB,mBAAO,CAAC,sEAAD,CAA3B;;AACA,IAAME,KAAK,GAAGF,mBAAO,CAAC,kDAAD,CAAP,CAAiB,kBAAjB,CAAd;AACA;AACA;AACA;AACA;AACA;;;AACAP,OAAO,CAACK,QAAR,GAAmB,CAAnB;AACA,IAAIsJ,UAAJ;;AACA,CAAC,UAAUA,UAAV,EAAsB;AACnBA,YAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC;AACAA,YAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;AACAA,YAAU,CAACA,UAAU,CAAC,OAAD,CAAV,GAAsB,CAAvB,CAAV,GAAsC,OAAtC;AACAA,YAAU,CAACA,UAAU,CAAC,KAAD,CAAV,GAAoB,CAArB,CAAV,GAAoC,KAApC;AACAA,YAAU,CAACA,UAAU,CAAC,eAAD,CAAV,GAA8B,CAA/B,CAAV,GAA8C,eAA9C;AACAA,YAAU,CAACA,UAAU,CAAC,cAAD,CAAV,GAA6B,CAA9B,CAAV,GAA6C,cAA7C;AACAA,YAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;AACH,CARD,EAQGA,UAAU,GAAG3J,OAAO,CAAC2J,UAAR,KAAuB3J,OAAO,CAAC2J,UAAR,GAAqB,EAA5C,CARhB;AASA;AACA;AACA;;;IACMhG,O;;;;;;;;AACF;AACJ;AACA;AACA;AACA;AACA;AACI,oBAAOuE,GAAP,EAAY;AACRzH,WAAK,CAAC,oBAAD,EAAuByH,GAAvB,CAAL;;AACA,UAAIA,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACC,KAAxB,IAAiC1B,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACc,GAA7D,EAAkE;AAC9D,YAAIoZ,WAAW,CAACc,SAAZ,CAAsBzc,GAAtB,CAAJ,EAAgC;AAC5BA,aAAG,CAACwB,IAAJ,GACIxB,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACC,KAAxB,GACMD,UAAU,CAACa,YADjB,GAEMb,UAAU,CAACgB,UAHrB;AAIA,iBAAO,KAAKia,cAAL,CAAoB1c,GAApB,CAAP;AACH;AACJ;;AACD,aAAO,CAAC,KAAK2c,cAAL,CAAoB3c,GAApB,CAAD,CAAP;AACH;AACD;AACJ;AACA;;;;WACI,wBAAeA,GAAf,EAAoB;AAChB;AACA,UAAIka,GAAG,GAAG,KAAKla,GAAG,CAACwB,IAAnB,CAFgB,CAGhB;;AACA,UAAIxB,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACa,YAAxB,IACAtC,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACgB,UAD5B,EACwC;AACpCyX,WAAG,IAAIla,GAAG,CAACgc,WAAJ,GAAkB,GAAzB;AACH,OAPe,CAQhB;AACA;;;AACA,UAAIhc,GAAG,CAAClB,GAAJ,IAAW,QAAQkB,GAAG,CAAClB,GAA3B,EAAgC;AAC5Bob,WAAG,IAAIla,GAAG,CAAClB,GAAJ,GAAU,GAAjB;AACH,OAZe,CAahB;;;AACA,UAAI,QAAQkB,GAAG,CAAC7G,EAAhB,EAAoB;AAChB+gB,WAAG,IAAIla,GAAG,CAAC7G,EAAX;AACH,OAhBe,CAiBhB;;;AACA,UAAI,QAAQ6G,GAAG,CAACrB,IAAhB,EAAsB;AAClBub,WAAG,IAAI5R,IAAI,CAACC,SAAL,CAAevI,GAAG,CAACrB,IAAnB,CAAP;AACH;;AACDpG,WAAK,CAAC,kBAAD,EAAqByH,GAArB,EAA0Bka,GAA1B,CAAL;AACA,aAAOA,GAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;WACI,wBAAela,GAAf,EAAoB;AAChB,UAAM4c,cAAc,GAAGJ,QAAQ,CAACd,iBAAT,CAA2B1b,GAA3B,CAAvB;AACA,UAAM8b,IAAI,GAAG,KAAKa,cAAL,CAAoBC,cAAc,CAAC/d,MAAnC,CAAb;AACA,UAAM+c,OAAO,GAAGgB,cAAc,CAAChB,OAA/B;AACAA,aAAO,CAACva,OAAR,CAAgBya,IAAhB,EAJgB,CAIO;;AACvB,aAAOF,OAAP,CALgB,CAKA;AACnB;;;;;;AAEL9jB,OAAO,CAAC2D,OAAR,GAAkBA,OAAlB;AACA;AACA;AACA;AACA;AACA;;IACME,O;;;;;AACF,qBAAc;AAAA;;AAAA;AAEb;AACD;AACJ;AACA;AACA;AACA;;;;;WACI,aAAIqE,GAAJ,EAAS;AACL,UAAInB,MAAJ;;AACA,UAAI,OAAOmB,GAAP,KAAe,QAAnB,EAA6B;AACzBnB,cAAM,GAAG,KAAKge,YAAL,CAAkB7c,GAAlB,CAAT;;AACA,YAAInB,MAAM,CAAC2C,IAAP,KAAgBC,UAAU,CAACa,YAA3B,IACAzD,MAAM,CAAC2C,IAAP,KAAgBC,UAAU,CAACgB,UAD/B,EAC2C;AACvC;AACA,eAAKqa,aAAL,GAAqB,IAAIC,mBAAJ,CAAwBle,MAAxB,CAArB,CAFuC,CAGvC;;AACA,cAAIA,MAAM,CAACmd,WAAP,KAAuB,CAA3B,EAA8B;AAC1B,8EAAW,SAAX,EAAsBnd,MAAtB;AACH;AACJ,SARD,MASK;AACD;AACA,4EAAW,SAAX,EAAsBA,MAAtB;AACH;AACJ,OAfD,MAgBK,IAAI8c,WAAW,CAACM,QAAZ,CAAqBjc,GAArB,KAA6BA,GAAG,CAAC8X,MAArC,EAA6C;AAC9C;AACA,YAAI,CAAC,KAAKgF,aAAV,EAAyB;AACrB,gBAAM,IAAI/e,KAAJ,CAAU,kDAAV,CAAN;AACH,SAFD,MAGK;AACDc,gBAAM,GAAG,KAAKie,aAAL,CAAmBE,cAAnB,CAAkChd,GAAlC,CAAT;;AACA,cAAInB,MAAJ,EAAY;AACR;AACA,iBAAKie,aAAL,GAAqB,IAArB;;AACA,8EAAW,SAAX,EAAsBje,MAAtB;AACH;AACJ;AACJ,OAbI,MAcA;AACD,cAAM,IAAId,KAAJ,CAAU,mBAAmBiC,GAA7B,CAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;WACI,sBAAaka,GAAb,EAAkB;AACd,UAAI9a,CAAC,GAAG,CAAR,CADc,CAEd;;AACA,UAAMga,CAAC,GAAG;AACN5X,YAAI,EAAEiI,MAAM,CAACyQ,GAAG,CAACrW,MAAJ,CAAW,CAAX,CAAD;AADN,OAAV;;AAGA,UAAIpC,UAAU,CAAC2X,CAAC,CAAC5X,IAAH,CAAV,KAAuB1I,SAA3B,EAAsC;AAClC,cAAM,IAAIiF,KAAJ,CAAU,yBAAyBqb,CAAC,CAAC5X,IAArC,CAAN;AACH,OARa,CASd;;;AACA,UAAI4X,CAAC,CAAC5X,IAAF,KAAWC,UAAU,CAACa,YAAtB,IACA8W,CAAC,CAAC5X,IAAF,KAAWC,UAAU,CAACgB,UAD1B,EACsC;AAClC,YAAMwa,KAAK,GAAG7d,CAAC,GAAG,CAAlB;;AACA,eAAO8a,GAAG,CAACrW,MAAJ,CAAW,EAAEzE,CAAb,MAAoB,GAApB,IAA2BA,CAAC,IAAI8a,GAAG,CAACje,MAA3C,EAAmD,CAAG;;AACtD,YAAMihB,GAAG,GAAGhD,GAAG,CAACnP,SAAJ,CAAckS,KAAd,EAAqB7d,CAArB,CAAZ;;AACA,YAAI8d,GAAG,IAAIzT,MAAM,CAACyT,GAAD,CAAb,IAAsBhD,GAAG,CAACrW,MAAJ,CAAWzE,CAAX,MAAkB,GAA5C,EAAiD;AAC7C,gBAAM,IAAIrB,KAAJ,CAAU,qBAAV,CAAN;AACH;;AACDqb,SAAC,CAAC4C,WAAF,GAAgBvS,MAAM,CAACyT,GAAD,CAAtB;AACH,OAnBa,CAoBd;;;AACA,UAAI,QAAQhD,GAAG,CAACrW,MAAJ,CAAWzE,CAAC,GAAG,CAAf,CAAZ,EAA+B;AAC3B,YAAM6d,MAAK,GAAG7d,CAAC,GAAG,CAAlB;;AACA,eAAO,EAAEA,CAAT,EAAY;AACR,cAAMiI,CAAC,GAAG6S,GAAG,CAACrW,MAAJ,CAAWzE,CAAX,CAAV;AACA,cAAI,QAAQiI,CAAZ,EACI;AACJ,cAAIjI,CAAC,KAAK8a,GAAG,CAACje,MAAd,EACI;AACP;;AACDmd,SAAC,CAACta,GAAF,GAAQob,GAAG,CAACnP,SAAJ,CAAckS,MAAd,EAAqB7d,CAArB,CAAR;AACH,OAVD,MAWK;AACDga,SAAC,CAACta,GAAF,GAAQ,GAAR;AACH,OAlCa,CAmCd;;;AACA,UAAMqe,IAAI,GAAGjD,GAAG,CAACrW,MAAJ,CAAWzE,CAAC,GAAG,CAAf,CAAb;;AACA,UAAI,OAAO+d,IAAP,IAAe1T,MAAM,CAAC0T,IAAD,CAAN,IAAgBA,IAAnC,EAAyC;AACrC,YAAMF,OAAK,GAAG7d,CAAC,GAAG,CAAlB;;AACA,eAAO,EAAEA,CAAT,EAAY;AACR,cAAMiI,EAAC,GAAG6S,GAAG,CAACrW,MAAJ,CAAWzE,CAAX,CAAV;;AACA,cAAI,QAAQiI,EAAR,IAAaoC,MAAM,CAACpC,EAAD,CAAN,IAAaA,EAA9B,EAAiC;AAC7B,cAAEjI,CAAF;AACA;AACH;;AACD,cAAIA,CAAC,KAAK8a,GAAG,CAACje,MAAd,EACI;AACP;;AACDmd,SAAC,CAACjgB,EAAF,GAAOsQ,MAAM,CAACyQ,GAAG,CAACnP,SAAJ,CAAckS,OAAd,EAAqB7d,CAAC,GAAG,CAAzB,CAAD,CAAb;AACH,OAjDa,CAkDd;;;AACA,UAAI8a,GAAG,CAACrW,MAAJ,CAAW,EAAEzE,CAAb,CAAJ,EAAqB;AACjB,YAAMge,OAAO,GAAGC,QAAQ,CAACnD,GAAG,CAAC1P,MAAJ,CAAWpL,CAAX,CAAD,CAAxB;;AACA,YAAIzD,OAAO,CAAC2hB,cAAR,CAAuBlE,CAAC,CAAC5X,IAAzB,EAA+B4b,OAA/B,CAAJ,EAA6C;AACzChE,WAAC,CAACza,IAAF,GAASye,OAAT;AACH,SAFD,MAGK;AACD,gBAAM,IAAIrf,KAAJ,CAAU,iBAAV,CAAN;AACH;AACJ;;AACDxF,WAAK,CAAC,kBAAD,EAAqB2hB,GAArB,EAA0Bd,CAA1B,CAAL;AACA,aAAOA,CAAP;AACH;;;;AAiBD;AACJ;AACA;AACI,uBAAU;AACN,UAAI,KAAK0D,aAAT,EAAwB;AACpB,aAAKA,aAAL,CAAmBS,sBAAnB;AACH;AACJ;;;WAvBD,wBAAsB/b,IAAtB,EAA4B4b,OAA5B,EAAqC;AACjC,cAAQ5b,IAAR;AACI,aAAKC,UAAU,CAACS,OAAhB;AACI,iBAAO,QAAOkb,OAAP,MAAmB,QAA1B;;AACJ,aAAK3b,UAAU,CAACiB,UAAhB;AACI,iBAAO0a,OAAO,KAAKtkB,SAAnB;;AACJ,aAAK2I,UAAU,CAACmB,aAAhB;AACI,iBAAO,OAAOwa,OAAP,KAAmB,QAAnB,IAA+B,QAAOA,OAAP,MAAmB,QAAzD;;AACJ,aAAK3b,UAAU,CAACC,KAAhB;AACA,aAAKD,UAAU,CAACa,YAAhB;AACI,iBAAO8C,KAAK,CAACiX,OAAN,CAAce,OAAd,KAA0BA,OAAO,CAACnhB,MAAR,GAAiB,CAAlD;;AACJ,aAAKwF,UAAU,CAACc,GAAhB;AACA,aAAKd,UAAU,CAACgB,UAAhB;AACI,iBAAO2C,KAAK,CAACiX,OAAN,CAAce,OAAd,CAAP;AAZR;AAcH;;;;EAjIiB7Z,O;;AA2ItBzL,OAAO,CAAC6D,OAAR,GAAkBA,OAAlB;;AACA,SAAS0hB,QAAT,CAAkBnD,GAAlB,EAAuB;AACnB,MAAI;AACA,WAAO5R,IAAI,CAAC8F,KAAL,CAAW8L,GAAX,CAAP;AACH,GAFD,CAGA,OAAOlN,CAAP,EAAU;AACN,WAAO,KAAP;AACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACM+P,mB;AACF,+BAAYle,MAAZ,EAAoB;AAAA;;AAChB,SAAKA,MAAL,GAAcA,MAAd;AACA,SAAK+c,OAAL,GAAe,EAAf;AACA,SAAK4B,SAAL,GAAiB3e,MAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;;;WACI,wBAAe4e,OAAf,EAAwB;AACpB,WAAK7B,OAAL,CAAa1d,IAAb,CAAkBuf,OAAlB;;AACA,UAAI,KAAK7B,OAAL,CAAa3f,MAAb,KAAwB,KAAKuhB,SAAL,CAAexB,WAA3C,EAAwD;AACpD;AACA,YAAMnd,MAAM,GAAG2d,QAAQ,CAACf,iBAAT,CAA2B,KAAK+B,SAAhC,EAA2C,KAAK5B,OAAhD,CAAf;AACA,aAAK2B,sBAAL;AACA,eAAO1e,MAAP;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;;;;WACI,kCAAyB;AACrB,WAAK2e,SAAL,GAAiB,IAAjB;AACA,WAAK5B,OAAL,GAAe,EAAf;AACH;;;;;;;;;;;;;;;;ACtRQ;;;;AACbhkB,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAAC2kB,SAAR,GAAoB3kB,OAAO,CAACmkB,QAAR,GAAmB,KAAK,CAA5C;AACA,IAAM3E,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;AACA,IAAMU,MAAM,GAAG,SAATA,MAAS,CAACjY,GAAD,EAAS;AACpB,SAAO,OAAOuX,WAAW,CAACU,MAAnB,KAA8B,UAA9B,GACDV,WAAW,CAACU,MAAZ,CAAmBjY,GAAnB,CADC,GAEDA,GAAG,CAACkY,MAAJ,YAAsBX,WAF5B;AAGH,CAJD;;AAKA,IAAMzM,QAAQ,GAAGlT,MAAM,CAACwM,SAAP,CAAiB0G,QAAlC;AACA,IAAMkN,cAAc,GAAG,OAAOD,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGjN,QAAQ,CAACf,IAAT,CAAcgO,IAAd,MAAwB,0BAFhC;AAGA,IAAM2F,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACG7S,QAAQ,CAACf,IAAT,CAAc4T,IAAd,MAAwB,0BAFhC;AAGA;AACA;AACA;AACA;AACA;;AACA,SAAS1B,QAAT,CAAkBjc,GAAlB,EAAuB;AACnB,SAASsX,qBAAqB,KAAKtX,GAAG,YAAYuX,WAAf,IAA8BU,MAAM,CAACjY,GAAD,CAAzC,CAAtB,IACHgY,cAAc,IAAIhY,GAAG,YAAY+X,IAD9B,IAEH2F,cAAc,IAAI1d,GAAG,YAAY2d,IAFtC;AAGH;;AACD7lB,OAAO,CAACmkB,QAAR,GAAmBA,QAAnB;;AACA,SAASQ,SAAT,CAAmBzc,GAAnB,EAAwB4d,MAAxB,EAAgC;AAC5B,MAAI,CAAC5d,GAAD,IAAQ,QAAOA,GAAP,MAAe,QAA3B,EAAqC;AACjC,WAAO,KAAP;AACH;;AACD,MAAIoF,KAAK,CAACiX,OAAN,CAAcrc,GAAd,CAAJ,EAAwB;AACpB,SAAK,IAAIZ,CAAC,GAAG,CAAR,EAAW8O,CAAC,GAAGlO,GAAG,CAAC/D,MAAxB,EAAgCmD,CAAC,GAAG8O,CAApC,EAAuC9O,CAAC,EAAxC,EAA4C;AACxC,UAAIqd,SAAS,CAACzc,GAAG,CAACZ,CAAD,CAAJ,CAAb,EAAuB;AACnB,eAAO,IAAP;AACH;AACJ;;AACD,WAAO,KAAP;AACH;;AACD,MAAI6c,QAAQ,CAACjc,GAAD,CAAZ,EAAmB;AACf,WAAO,IAAP;AACH;;AACD,MAAIA,GAAG,CAAC4d,MAAJ,IACA,OAAO5d,GAAG,CAAC4d,MAAX,KAAsB,UADtB,IAEA5hB,SAAS,CAACC,MAAV,KAAqB,CAFzB,EAE4B;AACxB,WAAOwgB,SAAS,CAACzc,GAAG,CAAC4d,MAAJ,EAAD,EAAe,IAAf,CAAhB;AACH;;AACD,OAAK,IAAMhZ,GAAX,IAAkB5E,GAAlB,EAAuB;AACnB,QAAIpI,MAAM,CAACwM,SAAP,CAAiB7C,cAAjB,CAAgCwI,IAAhC,CAAqC/J,GAArC,EAA0C4E,GAA1C,KAAkD6X,SAAS,CAACzc,GAAG,CAAC4E,GAAD,CAAJ,CAA/D,EAA2E;AACvE,aAAO,IAAP;AACH;AACJ;;AACD,SAAO,KAAP;AACH;;AACD9M,OAAO,CAAC2kB,SAAR,GAAoBA,SAApB,C;;;;;;;;;;;;ACtDa;;AAEb,IAAIoB,QAAQ,GAAG,mEAAmEtT,KAAnE,CAAyE,EAAzE,CAAf;AAAA,IACItO,MAAM,GAAG,EADb;AAAA,IAEIwO,GAAG,GAAG,EAFV;AAAA,IAGIqT,IAAI,GAAG,CAHX;AAAA,IAII1e,CAAC,GAAG,CAJR;AAAA,IAKIuK,IALJ;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASxK,MAAT,CAAgBid,GAAhB,EAAqB;AACnB,MAAI2B,OAAO,GAAG,EAAd;;AAEA,KAAG;AACDA,WAAO,GAAGF,QAAQ,CAACzB,GAAG,GAAGngB,MAAP,CAAR,GAAyB8hB,OAAnC;AACA3B,OAAG,GAAG/X,IAAI,CAACK,KAAL,CAAW0X,GAAG,GAAGngB,MAAjB,CAAN;AACD,GAHD,QAGSmgB,GAAG,GAAG,CAHf;;AAKA,SAAO2B,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS1R,MAAT,CAAgB6N,GAAhB,EAAqB;AACnB,MAAIrC,OAAO,GAAG,CAAd;;AAEA,OAAKzY,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG8a,GAAG,CAACje,MAApB,EAA4BmD,CAAC,EAA7B,EAAiC;AAC/ByY,WAAO,GAAGA,OAAO,GAAG5b,MAAV,GAAmBwO,GAAG,CAACyP,GAAG,CAACrW,MAAJ,CAAWzE,CAAX,CAAD,CAAhC;AACD;;AAED,SAAOyY,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASvD,KAAT,GAAiB;AACf,MAAI0J,GAAG,GAAG7e,MAAM,CAAC,CAAC,IAAIuK,IAAJ,EAAF,CAAhB;AAEA,MAAIsU,GAAG,KAAKrU,IAAZ,EAAkB,OAAOmU,IAAI,GAAG,CAAP,EAAUnU,IAAI,GAAGqU,GAAxB;AAClB,SAAOA,GAAG,GAAE,GAAL,GAAU7e,MAAM,CAAC2e,IAAI,EAAL,CAAvB;AACD,C,CAED;AACA;AACA;;;AACA,OAAO1e,CAAC,GAAGnD,MAAX,EAAmBmD,CAAC,EAApB;AAAwBqL,KAAG,CAACoT,QAAQ,CAACze,CAAD,CAAT,CAAH,GAAmBA,CAAnB;AAAxB,C,CAEA;AACA;AACA;;;AACAkV,KAAK,CAACnV,MAAN,GAAeA,MAAf;AACAmV,KAAK,CAACjI,MAAN,GAAeA,MAAf;AACA7T,MAAM,CAACV,OAAP,GAAiBwc,KAAjB,C", "file": "socket.io.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(self, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./build/index.js\");\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.io = exports.Socket = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\nconst debug = require(\"debug\")(\"socket.io-client\");\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0, url_1.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\nvar socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\nexports.default = lookup;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst util_1 = require(\"engine.io-client/lib/util\");\nconst socket_1 = require(\"./socket\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\nconst typed_events_1 = require(\"./typed-events\");\nconst debug = require(\"debug\")(\"socket.io-client:manager\");\nclass Manager extends typed_events_1.StrictEventEmitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0, util_1.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0, on_1.on)(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = (0, on_1.on)(socket, \"error\", (err) => {\n            debug(\"error\");\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0, on_1.on)(socket, \"ping\", this.onping.bind(this)), (0, on_1.on)(socket, \"data\", this.ondata.bind(this)), (0, on_1.on)(socket, \"error\", this.onerror.bind(this)), (0, on_1.on)(socket, \"close\", this.onclose.bind(this)), (0, on_1.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n        debug(\"onclose\");\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst typed_events_1 = require(\"./typed-events\");\nconst debug = require(\"debug\")(\"socket.io-client:socket\");\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends typed_events_1.StrictEventEmitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.disconnected = true;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            (0, on_1.on)(io, \"open\", this.onopen.bind(this)),\n            (0, on_1.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0, on_1.on)(io, \"error\", this.onerror.bind(this)),\n            (0, on_1.on)(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            debug(\"emitting packet with ack id %d\", this.ids);\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            debug(\"calling ack %s with %j\", packet.id, packet.data);\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n            debug(\"bad ack %s\", packet.id);\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.StrictEventEmitter = void 0;\nconst Emitter = require(\"component-emitter\");\n/**\n * Strictly typed version of an `EventEmitter`. A `TypedEventEmitter` takes type\n * parameters for mappings of event names to event data types, and strictly\n * types method calls to the `EventEmitter` according to these event maps.\n *\n * @typeParam ListenEvents - `EventsMap` of user-defined events that can be\n * listened to with `on` or `once`\n * @typeParam EmitEvents - `EventsMap` of user-defined events that can be\n * emitted with `emit`\n * @typeParam ReservedEvents - `EventsMap` of reserved events, that can be\n * emitted by socket.io with `emitReserved`, and can be listened to with\n * `listen`.\n */\nclass StrictEventEmitter extends Emitter {\n    /**\n     * Adds the `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    on(ev, listener) {\n        super.on(ev, listener);\n        return this;\n    }\n    /**\n     * Adds a one-time `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    once(ev, listener) {\n        super.once(ev, listener);\n        return this;\n    }\n    /**\n     * Emits an event.\n     *\n     * @param ev Name of the event\n     * @param args Values to send to listeners of this event\n     */\n    emit(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Emits a reserved event.\n     *\n     * This method is `protected`, so that only a class extending\n     * `StrictEventEmitter` can emit its own reserved events.\n     *\n     * @param ev Reserved event name\n     * @param args Arguments to emit along with the event\n     */\n    emitReserved(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Returns the listeners listening to an event.\n     *\n     * @param event Event name\n     * @returns Array of listeners subscribed to `event`\n     */\n    listeners(event) {\n        return super.listeners(event);\n    }\n}\nexports.StrictEventEmitter = StrictEventEmitter;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\nconst debug = require(\"debug\")(\"socket.io-client:url\");\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\nconst debug = require(\"debug\")(\"engine.io-client:socket\");\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\nconst { installTimerFunctions } = require(\"./util\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    installTimerFunctions(this, opts);\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {},\n        closeOnBeforeunload: true\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      if (this.opts.closeOnBeforeunload) {\n        // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n        // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n        // closed/reloaded)\n        addEventListener(\n          \"beforeunload\",\n          () => {\n            if (this.transport) {\n              // silently close the transport\n              this.transport.removeAllListeners();\n              this.transport.close();\n            }\n          },\n          false\n        );\n      }\n      if (this.hostname !== \"localhost\") {\n        this.offlineEventListener = () => {\n          this.onClose(\"transport close\");\n        };\n        addEventListener(\"offline\", this.offlineEventListener, false);\n      }\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n    debug('creating transport \"%s\"', name);\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n    debug(\"options: %j\", opts);\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      this.setTimeoutFn(() => {\n        this.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n      debug(\"error while creating transport: %s\", e);\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n    debug(\"setting transport %s\", transport.name);\n\n    if (this.transport) {\n      debug(\"clearing existing transport %s\", this.transport.name);\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", this.onDrain.bind(this))\n      .on(\"packet\", this.onPacket.bind(this))\n      .on(\"error\", this.onError.bind(this))\n      .on(\"close\", () => {\n        this.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n    debug('probing transport \"%s\"', name);\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n\n    Socket.priorWebsocketSuccess = false;\n\n    const onTransportOpen = () => {\n      if (failed) return;\n\n      debug('probe transport \"%s\" opened', name);\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n          debug('probe transport \"%s\" pong', name);\n          this.upgrading = true;\n          this.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n          debug('pausing current transport \"%s\"', this.transport.name);\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n            debug(\"changing transport and sending upgrade packet\");\n\n            cleanup();\n\n            this.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            this.emit(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n          debug('probe transport \"%s\" failed', name);\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          this.emit(\"upgradeError\", err);\n        }\n      });\n    };\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n      debug('probe transport \"%s\" failed because of error: %s', name, err);\n\n      this.emit(\"upgradeError\", error);\n    };\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n        debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.removeListener(\"close\", onclose);\n      this.removeListener(\"upgrading\", onupgrade);\n    };\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n    debug(\"socket open\");\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n      debug(\"starting upgrade probes\");\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n      debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"ping\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n      debug('packet received with socket readyState \"%s\"', this.readyState);\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    this.clearTimeoutFn(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = this.setTimeoutFn(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n    if (this.opts.autoUnref) {\n      this.pingTimeoutTimer.unref();\n    }\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n      debug(\"flushing %d packets in socket\", this.writeBuffer.length);\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const close = () => {\n      this.onClose(\"forced close\");\n      debug(\"socket closing - telling transport to close\");\n      this.transport.close();\n    };\n\n    const cleanupAndClose = () => {\n      this.removeListener(\"upgrade\", cleanupAndClose);\n      this.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    };\n\n    const waitForUpgrade = () => {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      this.once(\"upgrade\", cleanupAndClose);\n      this.once(\"upgradeError\", cleanupAndClose);\n    };\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", () => {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n    debug(\"socket error %j\", err);\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n      debug('socket close with reason: \"%s\"', reason);\n\n      // clear timers\n      this.clearTimeoutFn(this.pingIntervalTimer);\n      this.clearTimeoutFn(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      if (typeof removeEventListener === \"function\") {\n        removeEventListener(\"offline\", this.offlineEventListener, false);\n      }\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      this.writeBuffer = [];\n      this.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst { installTimerFunctions } = require(\"./util\");\nconst debug = require(\"debug\")(\"engine.io-client:transport\");\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n    installTimerFunctions(this, opts);\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      // this might happen if the transport was silently closed in the beforeunload event handler\n      debug(\"transport is not open, discarding packets\");\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    callbacks.push(this.onData.bind(this));\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = e => {\n      this.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      this.setTimeoutFn(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    const initIframe = () => {\n      if (this.iframe) {\n        try {\n          this.form.removeChild(this.iframe);\n        } catch (e) {\n          this.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + this.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = this.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = this.iframeId;\n\n      this.form.appendChild(iframe);\n      this.iframe = iframe;\n    };\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = () => {\n        if (this.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick, installTimerFunctions } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\nconst debug = require(\"debug\")(\"engine.io-client:polling-xhr\");\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", err => {\n      this.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    debug(\"xhr poll\");\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", err => {\n      this.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\",\n      \"autoUnref\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n\n    try {\n      debug(\"xhr open %s: %s\", this.method, this.uri);\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = () => {\n          this.onLoad();\n        };\n        xhr.onerror = () => {\n          this.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = () => {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            this.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            this.setTimeoutFn(() => {\n              this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n      debug(\"xhr data %s\", this.data);\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\nconst debug = require(\"debug\")(\"engine.io-client:polling\");\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n\n    const pause = () => {\n      debug(\"paused\");\n      this.readyState = \"paused\";\n      onPause();\n    };\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n        debug(\"we are currently polling - waiting to pause\");\n        total++;\n        this.once(\"pollComplete\", function() {\n          debug(\"pre-pause polling complete\");\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n        debug(\"we are currently writing - waiting to pause\");\n        total++;\n        this.once(\"drain\", function() {\n          debug(\"pre-pause writing complete\");\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n    debug(\"polling\");\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n    debug(\"polling got data %s\", data);\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n        debug('ignoring poll - transport state \"%s\"', this.readyState);\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const close = () => {\n      debug(\"writing close packet\");\n      this.write([{ type: \"close\" }]);\n    };\n\n    if (\"open\" === this.readyState) {\n      debug(\"transport open - closing\");\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      debug(\"transport not open - deferring close\");\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const globalThis = require(\"../globalThis\");\nconst nextTick = (() => {\n  const isPromiseAvailable =\n    typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n  if (isPromiseAvailable) {\n    return cb => Promise.resolve().then(cb);\n  } else {\n    return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n  }\n})();\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\",\n  nextTick\n};\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType,\n  nextTick\n} = require(\"./websocket-constructor\");\n\nconst debug = require(\"debug\")(\"engine.io-client:websocket\");\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n      this.onOpen();\n    };\n    this.ws.onclose = this.onClose.bind(this);\n    this.ws.onmessage = ev => this.onData(ev.data);\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n\n      parser.encodePacket(packet, this.supportsBinary, data => {\n        // always create a new object (GH-437)\n        const opts = {};\n        if (!usingBrowserWebSocket) {\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (this.opts.perMessageDeflate) {\n            const len =\n              \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            this.ws.send(data);\n          } else {\n            this.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug(\"websocket closed before onclose event\");\n        }\n\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emit(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "const globalThis = require(\"./globalThis\");\n\nmodule.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\n\nmodule.exports.installTimerFunctions = (obj, opts) => {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n  }\n};\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reconstructPacket = exports.deconstructPacket = void 0;\nconst is_binary_1 = require(\"./is-binary\");\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nfunction deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nexports.deconstructPacket = deconstructPacket;\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (is_binary_1.isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nfunction reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nexports.reconstructPacket = reconstructPacket;\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Decoder = exports.Encoder = exports.PacketType = exports.protocol = void 0;\nconst Emitter = require(\"component-emitter\");\nconst binary_1 = require(\"./binary\");\nconst is_binary_1 = require(\"./is-binary\");\nconst debug = require(\"debug\")(\"socket.io-parser\");\n/**\n * Protocol version.\n *\n * @public\n */\nexports.protocol = 5;\nvar PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType = exports.PacketType || (exports.PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nclass Encoder {\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        debug(\"encoding packet %j\", obj);\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (is_binary_1.hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data);\n        }\n        debug(\"encoded %j as %s\", obj, str);\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = binary_1.deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\nexports.Encoder = Encoder;\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nclass Decoder extends Emitter {\n    constructor() {\n        super();\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emit(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emit(\"decoded\", packet);\n            }\n        }\n        else if (is_binary_1.isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emit(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        debug(\"decoded %s as %j\", str, p);\n        return p;\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\nexports.Decoder = Decoder;\nfunction tryParse(str) {\n    try {\n        return JSON.parse(str);\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = binary_1.reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hasBinary = exports.isBinary = void 0;\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nfunction isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexports.isBinary = isBinary;\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\nexports.hasBinary = hasBinary;\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n"], "sourceRoot": ""}