{"_from": "socket.io-client@^4.1.2", "_id": "socket.io-client@4.2.0", "_inBundle": false, "_integrity": "sha512-3GJ2KMh7inJUNAOjgf8NaKJZJa9uRyfryh2LrVJyKyxmzoXlfW9DeDNqylJn0ovOFt4e/kRLNWzMt/YqqEWYSA==", "_location": "/socket.io-client", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "socket.io-client@^4.1.2", "name": "socket.io-client", "escapedName": "socket.io-client", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.2.0.tgz", "_shasum": "195feed3de40283b1ae3f7d02cf91d3eb2c905c1", "_spec": "socket.io-client@^4.1.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket", "browser": {"./test/node.ts": false}, "bugs": {"url": "https://github.com/socketio/socket.io-client/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"@types/component-emitter": "^1.2.10", "backo2": "~1.0.2", "component-emitter": "~1.3.0", "debug": "~4.3.2", "engine.io-client": "~5.2.0", "parseuri": "0.0.6", "socket.io-parser": "~4.0.4"}, "deprecated": false, "description": "[![Build Status](https://github.com/socketio/socket.io-client/workflows/CI/badge.svg?branch=master)](https://github.com/socketio/socket.io-client/actions) [![Dependency Status](https://david-dm.org/socketio/socket.io-client.svg)](https://david-dm.org/socketio/socket.io-client) [![devDependency Status](https://david-dm.org/socketio/socket.io-client/dev-status.svg)](https://david-dm.org/socketio/socket.io-client#info=devDependencies) [![NPM version](https://badge.fury.io/js/socket.io-client.svg)](https://www.npmjs.com/package/socket.io-client) ![Downloads](http://img.shields.io/npm/dm/socket.io-client.svg?style=flat) [![](http://slack.socket.io/badge.svg?)](http://slack.socket.io)", "devDependencies": {"@babel/core": "^7.15.0", "@babel/plugin-transform-object-assign": "^7.14.5", "@babel/preset-env": "^7.15.0", "@sinonjs/fake-timers": "^7.1.2", "@types/mocha": "^9.0.0", "@types/node": "^16.7.6", "@types/sinonjs__fake-timers": "^6.0.3", "babel-eslint": "^10.1.0", "babel-loader": "^8.1.0", "babel-preset-es2015": "6.24.1", "base64-arraybuffer": "^0.1.5", "expect.js": "0.3.1", "has-cors": "^1.1.0", "istanbul": "^0.4.5", "mocha": "^3.3.0", "prettier": "^2.3.2", "rimraf": "^3.0.2", "socket.io": "3.0.0", "socket.io-browsers": "^1.0.0", "socket.io-msgpack-parser": "^3.0.0", "text-blob-builder": "0.0.1", "ts-loader": "^8.3.0", "ts-node": "^10.2.1", "tsd": "^0.17.0", "typescript": "^4.4.2", "webpack": "^4.44.2", "webpack-cli": "^3.3.12", "webpack-remove-debug": "^0.1.0", "zuul": "~3.11.1", "zuul-builder-webpack": "^1.2.0", "zuul-ngrok": "4.0.0"}, "engines": {"node": ">=10.0.0"}, "exports": {"./package.json": "./package.json", "./dist/socket.io.js": "./dist/socket.io.js", "./dist/socket.io.js.map": "./dist/socket.io.js.map", ".": {"import": "./wrapper.mjs", "require": "./build/index.js"}}, "files": ["dist/", "build/", "wrapper.mjs"], "homepage": "https://github.com/socketio/socket.io-client#readme", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "client"], "license": "MIT", "main": "./build/index.js", "name": "socket.io-client", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io-client.git"}, "scripts": {"build": "npm run compile && webpack --config ./support/webpack.config.js --config ./support/prod.config.js --config ./support/msgpack-parser.config.js", "compile": "rimraf ./build && tsc", "format:check": "prettier --check \"lib/**/*.ts\" \"test/**/*.js\" \"test/**/*.ts\" \"support/**/*.js\"", "format:fix": "prettier --write \"lib/**/*.ts\" \"test/**/*.js\" \"test/**/*.ts\" \"support/**/*.js\"", "prepack": "npm run compile", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:browser": "zuul test/index.js", "test:node": "mocha --require ts-node/register --reporter dot --require test/support/server.js test/index.js", "test:types": "tsd"}, "tsd": {"directory": "test"}, "type": "commonjs", "types": "./build/index.d.ts", "version": "4.2.0"}