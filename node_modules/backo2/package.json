{"_from": "backo2@~1.0.2", "_id": "backo2@1.0.2", "_inBundle": false, "_integrity": "sha1-MasayLEpNjRj41s+u2n038+6eUc=", "_location": "/backo2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "backo2@~1.0.2", "name": "backo2", "escapedName": "backo2", "rawSpec": "~1.0.2", "saveSpec": null, "fetchSpec": "~1.0.2"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://registry.npmjs.org/backo2/-/backo2-1.0.2.tgz", "_shasum": "31ab1ac8b129363463e35b3ebb69f4dfcfba7947", "_spec": "backo2@~1.0.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io-client", "bugs": {"url": "https://github.com/mokesmokes/backo/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "simple backoff based on segmentio/backo", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/mokesmokes/backo#readme", "keywords": ["backoff"], "license": "MIT", "name": "backo2", "repository": {"type": "git", "url": "git+https://github.com/mokesmokes/backo.git"}, "version": "1.0.2"}