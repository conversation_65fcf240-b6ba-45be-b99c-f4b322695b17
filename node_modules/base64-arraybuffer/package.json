{"_from": "base64-arraybuffer@0.1.4", "_id": "base64-arraybuffer@0.1.4", "_inBundle": false, "_integrity": "sha1-mBjHngWbE1X5fgQooBfIOOkLqBI=", "_location": "/base64-arraybuffer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64-arraybuffer@0.1.4", "name": "base64-arraybuffer", "escapedName": "base64-arraybuffer", "rawSpec": "0.1.4", "saveSpec": null, "fetchSpec": "0.1.4"}, "_requiredBy": ["/engine.io-client", "/engine.io-parser"], "_resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.4.tgz", "_shasum": "9818c79e059b1355f97e0428a017c838e90ba812", "_spec": "base64-arraybuffer@0.1.4", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hertzen.com"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode/decode base64 data into ArrayBuffers", "devDependencies": {"grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1", "grunt-contrib-watch": "^0.6.1"}, "engines": {"node": ">= 0.6.0"}, "homepage": "https://github.com/niklasvh/base64-arraybuffer", "keywords": [], "licenses": [{"type": "MIT", "url": "https://github.com/niklasvh/base64-arraybuffer/blob/master/LICENSE-MIT"}], "main": "lib/base64-arraybuffer", "name": "base64-arraybuffer", "repository": {"type": "git", "url": "git+https://github.com/niklasvh/base64-arraybuffer.git"}, "scripts": {"test": "grunt nodeunit"}, "version": "0.1.4"}