{"_from": "socket.io-adapter@~2.3.2", "_id": "socket.io-adapter@2.3.2", "_inBundle": false, "_integrity": "sha512-PBZpxUPYjmoogY0aoaTmo1643JelsaS1CiAwNjRVdrI0X9Seuc19Y2Wife8k88avW6haG8cznvwbubAZwH4Mtg==", "_location": "/socket.io-adapter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "socket.io-adapter@~2.3.2", "name": "socket.io-adapter", "escapedName": "socket.io-adapter", "rawSpec": "~2.3.2", "saveSpec": null, "fetchSpec": "~2.3.2"}, "_requiredBy": ["/socket.io"], "_resolved": "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.3.2.tgz", "_shasum": "039cd7c71a52abad984a6d57da2c0b7ecdd3c289", "_spec": "socket.io-adapter@~2.3.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "bugs": {"url": "https://github.com/socketio/socket.io-adapter/issues"}, "bundleDependencies": false, "deprecated": false, "description": "default socket.io in-memory adapter", "devDependencies": {"@types/node": "^14.11.2", "expect.js": "^0.3.1", "mocha": "^8.1.3", "nyc": "^15.1.0", "prettier": "^1.19.1", "typescript": "^4.0.3"}, "files": ["dist/"], "homepage": "https://github.com/socketio/socket.io-adapter#readme", "license": "MIT", "main": "./dist/index.js", "name": "socket.io-adapter", "repository": {"type": "git", "url": "git://github.com/socketio/socket.io-adapter.git"}, "scripts": {"format:check": "prettier --parser typescript --check 'lib/**/*.ts' 'test/**/*.js'", "format:fix": "prettier --parser typescript --write 'lib/**/*.ts' 'test/**/*.js'", "prepack": "tsc", "test": "npm run format:check && tsc && nyc mocha test/index.js"}, "types": "./dist/index.d.ts", "version": "2.3.2"}