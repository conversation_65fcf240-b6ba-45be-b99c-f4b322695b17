{"_from": "mime-types@~2.1.24", "_id": "mime-types@2.1.32", "_inBundle": false, "_integrity": "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A==", "_location": "/mime-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mime-types@~2.1.24", "name": "mime-types", "escapedName": "mime-types", "rawSpec": "~2.1.24", "saveSpec": null, "fetchSpec": "~2.1.24"}, "_requiredBy": ["/accepts"], "_resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "_shasum": "1d00e89e7de7fe02008db61001d9e02852670fd5", "_spec": "mime-types@~2.1.24", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\accepts", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-db": "1.49.0"}, "deprecated": false, "description": "The ultimate javascript content-type utility.", "devDependencies": {"eslint": "7.31.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.0.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/mime-types#readme", "keywords": ["mime", "types"], "license": "MIT", "name": "mime-types", "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.1.32"}