{"_from": "debug@~4.3.2", "_id": "debug@4.3.2", "_inBundle": false, "_integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "_location": "/debug", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "debug@~4.3.2", "name": "debug", "escapedName": "debug", "rawSpec": "~4.3.2", "saveSpec": null, "fetchSpec": "~4.3.2"}, "_requiredBy": ["/engine.io", "/engine.io-client", "/socket.io", "/socket.io-client", "/socket.io-parser"], "_resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "_shasum": "f0a49c18ac8779e31d4a0c6029dfb76873c7428b", "_spec": "debug@~4.3.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "2.1.2"}, "deprecated": false, "description": "small debugging utility", "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "engines": {"node": ">=6.0"}, "files": ["src", "LICENSE", "README.md"], "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "peerDependenciesMeta": {"supports-color": {"optional": true}}, "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls", "test:node": "istanbul cover _mocha -- test.js"}, "version": "4.3.2"}