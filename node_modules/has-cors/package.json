{"_from": "has-cors@1.1.0", "_id": "has-cors@1.1.0", "_inBundle": false, "_integrity": "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk=", "_location": "/has-cors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-cors@1.1.0", "name": "has-cors", "escapedName": "has-cors", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://registry.npmjs.org/has-cors/-/has-cors-1.1.0.tgz", "_shasum": "5e474793f7ea9843d1bb99c23eef49ff126fff39", "_spec": "has-cors@1.1.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-client", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/component/has-cors/issues"}, "bundleDependencies": false, "component": {"scripts": {"has-cors/index.js": "index.js"}}, "deprecated": false, "description": "Detects support for Cross-Origin Resource Sharing", "devDependencies": {"chai": "^1.10", "mocha": "^2.0"}, "homepage": "https://github.com/component/has-cors#readme", "keywords": ["cors", "cross", "origin", "resource", "sharing", "domain"], "license": "MIT", "main": "index.js", "name": "has-cors", "repository": {"type": "git", "url": "git://github.com/component/has-cors.git"}, "scripts": {"test": "mocha"}, "version": "1.1.0"}