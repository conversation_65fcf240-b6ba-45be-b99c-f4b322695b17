{"_from": "engine.io-client@~5.2.0", "_id": "engine.io-client@5.2.0", "_inBundle": false, "_integrity": "sha512-BcIBXGBkT7wKecwnfrSV79G2X5lSUSgeAGgoo60plXf8UsQEvCQww/KMwXSMhVjb98fFYNq20CC5eo8IOAPqsg==", "_location": "/engine.io-client", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "engine.io-client@~5.2.0", "name": "engine.io-client", "escapedName": "engine.io-client", "rawSpec": "~5.2.0", "saveSpec": null, "fetchSpec": "~5.2.0"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://registry.npmjs.org/engine.io-client/-/engine.io-client-5.2.0.tgz", "_shasum": "ae38c79a4af16258c0300e6819c0ea8ecc1597cd", "_spec": "engine.io-client@~5.2.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io-client", "browser": {"ws": false, "xmlhttprequest-ssl": "./lib/xmlhttprequest.js", "./test/node.js": false, "./lib/transports/websocket-constructor.js": "./lib/transports/websocket-constructor.browser.js", "./lib/globalThis.js": "./lib/globalThis.browser.js"}, "bugs": {"url": "https://github.com/socketio/engine.io-client/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-arraybuffer": "0.1.4", "component-emitter": "~1.3.0", "debug": "~4.3.1", "engine.io-parser": "~4.0.1", "has-cors": "1.1.0", "parseqs": "0.0.6", "parseuri": "0.0.6", "ws": "~7.4.2", "xmlhttprequest-ssl": "~2.0.0", "yeast": "0.1.2"}, "deprecated": false, "description": "Client for the realtime Engine", "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-transform-object-assign": "^7.12.1", "@babel/preset-env": "^7.12.7", "@sinonjs/fake-timers": "^7.1.2", "@types/sinonjs__fake-timers": "^6.0.3", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "blob": "0.0.5", "engine.io": "4.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.15.0", "expect.js": "^0.3.1", "express": "^4.17.1", "mocha": "^3.2.0", "prettier": "^1.19.1", "socket.io-browsers": "~1.0.4", "webpack": "^4.44.2", "webpack-cli": "^3.3.12", "webpack-remove-debug": "^0.1.0", "zuul": "~3.11.1", "zuul-builder-webpack": "^1.2.0", "zuul-ngrok": "4.0.0"}, "files": ["index.js", "lib/", "dist/", "contrib/"], "homepage": "https://github.com/socketio/engine.io-client", "license": "MIT", "main": "lib/index.js", "name": "engine.io-client", "repository": {"type": "git", "url": "git+https://github.com/socketio/engine.io-client.git"}, "scripts": {"build": "webpack --config ./support/webpack.config.js --config ./support/prod.config.js", "format:check": "prettier --check 'lib/**/*.js' 'test/**/*.js' 'support/**/*.js'", "format:fix": "prettier --write 'lib/**/*.js' 'test/**/*.js' 'support/**/*.js'", "lint": "eslint lib/**/*.js test/**/*.js", "test": "npm run lint && npm run format:check && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:browser": "zuul test/index.js", "test:node": "mocha --reporter dot --require test/support/server.js test/index.js"}, "version": "5.2.0"}