{"_from": "ws@~7.4.2", "_id": "ws@7.4.6", "_inBundle": false, "_integrity": "sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==", "_location": "/engine.io-client/ws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ws@~7.4.2", "name": "ws", "escapedName": "ws", "rawSpec": "~7.4.2", "saveSpec": null, "fetchSpec": "~7.4.2"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://registry.npmjs.org/ws/-/ws-7.4.6.tgz", "_shasum": "5654ca8ecdeee47c33a9a4bf6d28e2be2980377c", "_spec": "ws@~7.4.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-client", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "browser": "browser.js", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^7.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1", "mocha": "^7.0.0", "nyc": "^15.0.0", "prettier": "^2.0.5", "utf-8-validate": "^5.0.2"}, "engines": {"node": ">=8.3.0"}, "files": ["browser.js", "index.js", "lib/*.js"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js"}, "version": "7.4.6"}