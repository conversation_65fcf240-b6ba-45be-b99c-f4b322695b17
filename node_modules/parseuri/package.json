{"_from": "parseuri@0.0.6", "_id": "parseuri@0.0.6", "_inBundle": false, "_integrity": "sha512-AUjen8sAkGgao7UyCX6Ahv0gIK2fABKmYjvP4xmy5JaKvcbTRueIqIPHLAfq30xJddqSE033IOMUSOMCcK3Sow==", "_location": "/parseuri", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "parseuri@0.0.6", "name": "parseuri", "escapedName": "parseuri", "rawSpec": "0.0.6", "saveSpec": null, "fetchSpec": "0.0.6"}, "_requiredBy": ["/engine.io-client", "/socket.io-client"], "_resolved": "https://registry.npmjs.org/parseuri/-/parseuri-0.0.6.tgz", "_shasum": "e1496e829e3ac2ff47f39a4dd044b32823c4a25a", "_spec": "parseuri@0.0.6", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io-client", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/get/parseuri/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Method that parses a URI and returns an array of its components", "devDependencies": {"better-assert": "~1.0.0", "expect.js": "^0.3.1", "mocha": "1.17.1"}, "homepage": "https://github.com/get/parseuri", "license": "MIT", "name": "parseuri", "repository": {"type": "git", "url": "git+https://github.com/get/parseuri.git"}, "scripts": {"test": "make test"}, "version": "0.0.6"}