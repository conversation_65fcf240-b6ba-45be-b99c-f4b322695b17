{"_from": "@types/cookie@^0.4.1", "_id": "@types/cookie@0.4.1", "_inBundle": false, "_integrity": "sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==", "_location": "/@types/cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/cookie@^0.4.1", "name": "@types/cookie", "escapedName": "@types%2fcookie", "scope": "@types", "rawSpec": "^0.4.1", "saveSpec": null, "fetchSpec": "^0.4.1"}, "_requiredBy": ["/socket.io"], "_resolved": "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz", "_shasum": "bfd02c1f2224567676c1545199f87c3a861d878d", "_spec": "@types/cookie@^0.4.1", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Pine Mizune", "url": "https://github.com/pine"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for cookie", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie", "license": "MIT", "main": "", "name": "@types/cookie", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "7d4a6dd505c896319459ae131b5fa8fc0a2ed25552db53dac87946119bb21559", "version": "0.4.1"}