{"_from": "@types/cors@^2.8.12", "_id": "@types/cors@2.8.12", "_inBundle": false, "_integrity": "sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw==", "_location": "/@types/cors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/cors@^2.8.12", "name": "@types/cors", "escapedName": "@types%2fcors", "scope": "@types", "rawSpec": "^2.8.12", "saveSpec": null, "fetchSpec": "^2.8.12"}, "_requiredBy": ["/socket.io"], "_resolved": "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz", "_shasum": "6b2c510a7ad7039e98e7b8d3d6598f4359e5c080", "_spec": "@types/cors@^2.8.12", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/pluma"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gtpan77"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for cors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "license": "MIT", "main": "", "name": "@types/cors", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cors"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "53ea51a6543d58d3c1b9035a9c361d8f06d7be01973be2895820b2fb7ad9563a", "version": "2.8.12"}