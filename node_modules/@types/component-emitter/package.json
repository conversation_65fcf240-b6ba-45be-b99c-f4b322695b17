{"_from": "@types/component-emitter@^1.2.10", "_id": "@types/component-emitter@1.2.10", "_inBundle": false, "_integrity": "sha512-bsjleuRKWmGqajMerkzox19aGbscQX5rmmvvXl3wlIp5gMG1HgkiwPxsN5p070fBDKTNSPgojVbuY1+HWMbFhg==", "_location": "/@types/component-emitter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/component-emitter@^1.2.10", "name": "@types/component-emitter", "escapedName": "@types%2fcomponent-emitter", "scope": "@types", "rawSpec": "^1.2.10", "saveSpec": null, "fetchSpec": "^1.2.10"}, "_requiredBy": ["/socket.io-client", "/socket.io-parser"], "_resolved": "https://registry.npmjs.org/@types/component-emitter/-/component-emitter-1.2.10.tgz", "_shasum": "ef5b1589b9f16544642e473db5ea5639107ef3ea", "_spec": "@types/component-emitter@^1.2.10", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io-parser", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/psnider"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for component-emitter", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/component-emitter", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/component-emitter"}, "scripts": {}, "typeScriptVersion": "3.1", "types": "index.d.ts", "typesPublisherContentHash": "446bfc6b04cc0bdf3afbaa65ac393c5c9056ab15d158199b96d266059c762403", "version": "1.2.10"}