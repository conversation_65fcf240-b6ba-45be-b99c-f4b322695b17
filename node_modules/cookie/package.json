{"_from": "cookie@~0.4.1", "_id": "cookie@0.4.1", "_inBundle": false, "_integrity": "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==", "_location": "/cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cookie@~0.4.1", "name": "cookie", "escapedName": "cookie", "rawSpec": "~0.4.1", "saveSpec": null, "fetchSpec": "~0.4.1"}, "_requiredBy": ["/engine.io"], "_resolved": "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz", "_shasum": "afd713fe26ebd21ba95ceb61f9a8116e50a537d1", "_spec": "cookie@~0.4.1", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "6.8.0", "eslint-plugin-markdown": "1.0.2", "mocha": "7.1.1", "nyc": "15.0.1"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks --ui qunit test/", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "0.4.1"}