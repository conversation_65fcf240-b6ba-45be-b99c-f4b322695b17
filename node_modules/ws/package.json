{"_from": "ws@^7.5.0", "_id": "ws@7.5.5", "_inBundle": false, "_integrity": "sha512-BAkMFcAzl8as1G/hArkxOxq3G7pjUqQ3gzYbLL0/5zNkph70e+lCoxBGnm6AW1+/aiNeV4fnKqZ8m4GZewmH2w==", "_location": "/ws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ws@^7.5.0", "name": "ws", "escapedName": "ws", "rawSpec": "^7.5.0", "saveSpec": null, "fetchSpec": "^7.5.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/ws/-/ws-7.5.5.tgz", "_shasum": "8b4bc4af518cfabd0473ae4f99144287b33eb881", "_spec": "ws@^7.5.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "browser": "browser.js", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^7.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "prettier": "^2.0.5", "utf-8-validate": "^5.0.2"}, "engines": {"node": ">=8.3.0"}, "files": ["browser.js", "index.js", "lib/*.js"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js"}, "version": "7.5.5"}