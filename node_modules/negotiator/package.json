{"_from": "negotiator@0.6.2", "_id": "negotiator@0.6.2", "_inBundle": false, "_integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==", "_location": "/negotiator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "negotiator@0.6.2", "name": "negotiator", "escapedName": "negotiator", "rawSpec": "0.6.2", "saveSpec": null, "fetchSpec": "0.6.2"}, "_requiredBy": ["/accepts"], "_resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz", "_shasum": "feacf7ccf525a77ae9634436a64883ffeca346fb", "_spec": "negotiator@0.6.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\accepts", "bugs": {"url": "https://github.com/jshttp/negotiator/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "deprecated": false, "description": "HTTP content negotiation", "devDependencies": {"eslint": "5.16.0", "eslint-plugin-markdown": "1.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "homepage": "https://github.com/jshttp/negotiator#readme", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "license": "MIT", "name": "negotiator", "repository": {"type": "git", "url": "git+https://github.com/jshttp/negotiator.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "version": "0.6.2"}