{"_from": "yeast@0.1.2", "_id": "yeast@0.1.2", "_inBundle": false, "_integrity": "sha1-AI4G2AlDIMNy28L47XagymyKxBk=", "_location": "/yeast", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "yeast@0.1.2", "name": "yeast", "escapedName": "yeast", "rawSpec": "0.1.2", "saveSpec": null, "fetchSpec": "0.1.2"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://registry.npmjs.org/yeast/-/yeast-0.1.2.tgz", "_shasum": "008e06d8094320c372dbc2f8ed76a0ca6c8ac419", "_spec": "yeast@0.1.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-client", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/yeast/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny but linear growing unique id generator", "devDependencies": {"assume": "1.3.x", "istanbul": "0.3.x", "mocha": "2.3.x", "pre-commit": "1.1.x", "zuul": "3.4.x"}, "homepage": "https://github.com/unshiftio/yeast", "keywords": ["yeast", "id", "generator", "unique"], "license": "MIT", "main": "index.js", "name": "yeast", "repository": {"type": "git", "url": "git+https://github.com/unshiftio/yeast.git"}, "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "coverage": "istanbul cover _mocha -- test.js", "test": "mocha test.js", "test-browser": "zuul -- test.js", "test-node": "istanbul cover _mocha --report lcovonly -- test.js", "watch": "mocha --watch test.js"}, "version": "0.1.2"}