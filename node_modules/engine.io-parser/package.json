{"_from": "engine.io-parser@~4.0.0", "_id": "engine.io-parser@4.0.3", "_inBundle": false, "_integrity": "sha512-xEAAY0msNnESNPc00e19y5heTPX4y/TJ36gr8t1voOaNmTojP9b3oK3BbJLFufW2XFPQaaijpFewm2g2Um3uqA==", "_location": "/engine.io-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "engine.io-parser@~4.0.0", "name": "engine.io-parser", "escapedName": "engine.io-parser", "rawSpec": "~4.0.0", "saveSpec": null, "fetchSpec": "~4.0.0"}, "_requiredBy": ["/engine.io", "/engine.io-client"], "_resolved": "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-4.0.3.tgz", "_shasum": "83d3a17acfd4226f19e721bb22a1ee8f7662d2f6", "_spec": "engine.io-parser@~4.0.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io", "browser": {"./lib/encodePacket.js": "./lib/encodePacket.browser.js", "./lib/decodePacket.js": "./lib/decodePacket.browser.js"}, "bugs": {"url": "https://github.com/socketio/engine.io-parser/issues"}, "bundleDependencies": false, "dependencies": {"base64-arraybuffer": "0.1.4"}, "deprecated": false, "description": "Parser for the client for the realtime Engine", "devDependencies": {"@babel/core": "~7.9.6", "@babel/preset-env": "~7.9.6", "babel-eslint": "^10.0.3", "babelify": "^10.0.0", "benchmark": "^2.1.4", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "expect.js": "0.3.1", "mocha": "^5.2.0", "nyc": "~15.0.1", "prettier": "^1.19.1", "socket.io-browsers": "^1.0.4", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "engines": {"node": ">=8.0.0"}, "files": ["lib/"], "homepage": "https://github.com/socketio/engine.io-parser", "license": "MIT", "main": "lib/index.js", "name": "engine.io-parser", "repository": {"type": "git", "url": "git+ssh://**************/socketio/engine.io-parser.git"}, "scripts": {"format:check": "prettier --check 'lib/**/*.js' 'test/**/*.js'", "format:fix": "prettier --write 'lib/**/*.js' 'test/**/*.js'", "lint": "eslint 'lib/**/*.js' 'test/**/*.js'", "test": "npm run lint && npm run format:check && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:browser": "zuul test/index.js --no-coverage", "test:node": "nyc mocha test/index.js"}, "version": "4.0.3"}