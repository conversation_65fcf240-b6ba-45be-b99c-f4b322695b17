{"_from": "engine.io@~5.2.0", "_id": "engine.io@5.2.0", "_inBundle": false, "_integrity": "sha512-d1DexkQx87IFr1FLuV+0f5kAm1Hk1uOVijLOb+D1sDO2QMb7YjE02VHtZtxo7xIXMgcWLb+vl3HRT0rI9tr4jQ==", "_location": "/engine.io", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "engine.io@~5.2.0", "name": "engine.io", "escapedName": "engine.io", "rawSpec": "~5.2.0", "saveSpec": null, "fetchSpec": "~5.2.0"}, "_requiredBy": ["/socket.io"], "_resolved": "https://registry.npmjs.org/engine.io/-/engine.io-5.2.0.tgz", "_shasum": "554cdd0230d89de7b1a49a809d7ee5a129d36809", "_spec": "engine.io@~5.2.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/socketio/engine.io/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/EugenD<PERSON>ck"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/afshinm"}, {"name": "<PERSON>", "url": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.4.1", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~4.0.0", "ws": "~7.4.2"}, "deprecated": false, "description": "The realtime engine behind Socket.IO. Provides the foundation of a bidirectional connection between client and server", "devDependencies": {"babel-eslint": "^8.0.2", "eiows": "^3.3.0", "engine.io-client": "5.2.0", "engine.io-client-v3": "npm:engine.io-client@3.5.0", "eslint": "^4.19.1", "eslint-config-prettier": "^6.9.0", "expect.js": "^0.3.1", "mocha": "^4.0.1", "prettier": "^1.19.1", "s": "0.1.1", "superagent": "^3.8.1"}, "engines": {"node": ">=10.0.0"}, "files": ["lib/"], "homepage": "https://github.com/socketio/engine.io", "license": "MIT", "main": "lib/engine.io.js", "name": "engine.io", "repository": {"type": "git", "url": "git+ssh://**************/socketio/engine.io.git"}, "scripts": {"format:check": "prettier --check 'lib/**/*.js' 'test/**/*.js'", "format:fix": "prettier --write 'lib/**/*.js' 'test/**/*.js'", "lint": "eslint lib/ test/ *.js", "test": "npm run lint && npm run format:check && mocha && EIO_CLIENT=3 mocha && EIO_WS_ENGINE=eiows mocha"}, "version": "5.2.0"}