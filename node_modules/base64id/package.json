{"_from": "base64id@~2.0.0", "_id": "base64id@2.0.0", "_inBundle": false, "_integrity": "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==", "_location": "/base64id", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "base64id@~2.0.0", "name": "base64id", "escapedName": "base64id", "rawSpec": "~2.0.0", "saveSpec": null, "fetchSpec": "~2.0.0"}, "_requiredBy": ["/engine.io", "/socket.io"], "_resolved": "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz", "_shasum": "2770ac6bc47d312af97a8bf9a634342e0cd25cb6", "_spec": "base64id@~2.0.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "author": {"name": "<PERSON><PERSON>", "email": "fael<PERSON><EMAIL>"}, "bugs": {"url": "https://github.com/faeldt/base64id/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Generates a base64 id", "engines": {"node": "^4.5.0 || >= 5.9"}, "homepage": "https://github.com/faeldt/base64id#readme", "license": "MIT", "main": "./lib/base64id.js", "name": "base64id", "repository": {"type": "git", "url": "git+https://github.com/faeldt/base64id.git"}, "version": "2.0.0"}