{"_from": "xmlhttprequest-ssl@~2.0.0", "_id": "xmlhttprequest-ssl@2.0.0", "_inBundle": false, "_integrity": "sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A==", "_location": "/xmlhttprequest-ssl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xmlhttprequest-ssl@~2.0.0", "name": "xmlhttprequest-ssl", "escapedName": "xmlhttprequest-ssl", "rawSpec": "~2.0.0", "saveSpec": null, "fetchSpec": "~2.0.0"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.0.0.tgz", "_shasum": "91360c86b914e67f44dce769180027c0da618c67", "_spec": "xmlhttprequest-ssl@~2.0.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-client", "author": {"name": "<PERSON>"}, "bugs": {"url": "http://github.com/mjwwit/node-XMLHttpRequest/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "XMLHttpRequest for Node", "directories": {"lib": "./lib", "example": "./example"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/mjwwit/node-XMLHttpRequest#readme", "keywords": ["xhr", "ajax"], "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "main": "./lib/XMLHttpRequest.js", "name": "xmlhttprequest-ssl", "repository": {"type": "git", "url": "git://github.com/mjwwit/node-XMLHttpRequest.git"}, "scripts": {"test": "cd ./tests && node test-constants.js && node test-events.js && node test-exceptions.js && node test-headers.js && node test-redirect-302.js && node test-redirect-303.js && node test-redirect-307.js && node test-request-methods.js && node test-request-protocols.js"}, "version": "2.0.0"}