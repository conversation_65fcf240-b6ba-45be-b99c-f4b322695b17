{"_from": "parseqs@0.0.6", "_id": "parseqs@0.0.6", "_inBundle": false, "_integrity": "sha512-jeAGzMDbfSHHA091hr0r31eYfTig+29g3GKKE/PPbEQ65X0lmMwlEoqmhzu0iztID5uJpZsFlUPDP8ThPL7M8w==", "_location": "/parseqs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "parseqs@0.0.6", "name": "parseqs", "escapedName": "parseqs", "rawSpec": "0.0.6", "saveSpec": null, "fetchSpec": "0.0.6"}, "_requiredBy": ["/engine.io-client"], "_resolved": "https://registry.npmjs.org/parseqs/-/parseqs-0.0.6.tgz", "_shasum": "8e4bb5a19d1cdc844a08ac974d34e273afa670d5", "_spec": "parseqs@0.0.6", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\engine.io-client", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/get/querystring/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provides methods for parsing a query string into an object, and vice versa.", "devDependencies": {"better-assert": "~1.0.0", "mocha": "1.17.1"}, "homepage": "https://github.com/get/querystring", "license": "MIT", "name": "parseqs", "repository": {"type": "git", "url": "git+https://github.com/get/querystring.git"}, "scripts": {"test": "make test"}, "version": "0.0.6"}