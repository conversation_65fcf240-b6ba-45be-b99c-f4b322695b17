{"_from": "socket.io-parser@~4.0.4", "_id": "socket.io-parser@4.0.4", "_inBundle": false, "_integrity": "sha512-t+b0SS+IxG7Rxzda2EVvyBZbvFPBCjJoyHuE0P//7OAsN23GItzDRdWa6ALxZI/8R5ygK7jAR6t028/z+7295g==", "_location": "/socket.io-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "socket.io-parser@~4.0.4", "name": "socket.io-parser", "escapedName": "socket.io-parser", "rawSpec": "~4.0.4", "saveSpec": null, "fetchSpec": "~4.0.4"}, "_requiredBy": ["/socket.io", "/socket.io-client"], "_resolved": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.0.4.tgz", "_shasum": "9ea21b0d61508d18196ef04a2c6b9ab630f4c2b0", "_spec": "socket.io-parser@~4.0.4", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io", "bugs": {"url": "https://github.com/socketio/socket.io-parser/issues"}, "bundleDependencies": false, "dependencies": {"@types/component-emitter": "^1.2.10", "component-emitter": "~1.3.0", "debug": "~4.3.1"}, "deprecated": false, "description": "socket.io protocol parser", "devDependencies": {"@babel/core": "~7.9.6", "@babel/preset-env": "~7.9.6", "@types/debug": "^4.1.5", "@types/node": "^14.11.1", "babelify": "~10.0.0", "benchmark": "2.1.2", "expect.js": "0.3.1", "mocha": "3.2.0", "prettier": "^2.1.2", "rimraf": "^3.0.2", "socket.io-browsers": "^1.0.0", "typescript": "^4.0.3", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "engines": {"node": ">=10.0.0"}, "files": ["dist/"], "homepage": "https://github.com/socketio/socket.io-parser#readme", "license": "MIT", "main": "./dist/index.js", "name": "socket.io-parser", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io-parser.git"}, "scripts": {"compile": "rimraf ./dist && tsc", "format:check": "prettier --check --parser typescript 'lib/**/*.ts' 'test/**/*.js'", "format:fix": "prettier --write --parser typescript 'lib/**/*.ts' 'test/**/*.js'", "prepack": "npm run compile", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:browser": "zuul test/index.js --no-coverage", "test:node": "mocha --reporter dot --bail test/index.js"}, "types": "./dist/index.d.ts", "version": "4.0.4"}