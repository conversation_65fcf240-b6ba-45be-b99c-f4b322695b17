{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/socket.io-msgpack-parser/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./build/manager.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/yeast/index.js", "webpack://io/./build/socket.js", "webpack://io/./build/on.js", "webpack://io/./build/typed-events.js", "webpack://io/./build/index.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/notepack.io/lib/index.js", "webpack://io/./node_modules/notepack.io/browser/encode.js", "webpack://io/./node_modules/notepack.io/browser/decode.js", "webpack://io/./node_modules/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "window", "Function", "encodePacket", "require", "decodePacket", "SEPARATOR", "String", "fromCharCode", "protocol", "encodePayload", "packets", "callback", "length", "encodedPackets", "Array", "count", "for<PERSON>ach", "packet", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "push", "type", "Emitter", "obj", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "len", "slice", "listeners", "hasListeners", "globalThis", "pick", "attr", "reduce", "acc", "k", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "clearTimeoutFn", "parser", "Transport", "query", "readyState", "socket", "msg", "desc", "err", "Error", "description", "doOpen", "doClose", "onClose", "write", "writable", "data", "onPacket", "encode", "str", "encodeURIComponent", "decode", "qs", "qry", "pairs", "pair", "decodeURIComponent", "msgpack", "PacketType", "CONNECT", "DISCONNECT", "EVENT", "ACK", "CONNECT_ERROR", "isInteger", "Number", "isFinite", "Math", "floor", "isString", "isObject", "toString", "Encoder", "Decoder", "add", "decoded", "checkPacket", "nsp", "undefined", "isArray", "isDataValid", "id", "destroy", "re", "parts", "src", "b", "indexOf", "e", "substring", "replace", "exec", "uri", "source", "host", "authority", "ipv6uri", "pathNames", "path", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Manager", "eio", "util_1", "socket_1", "on_1", "Backoff", "_a", "nsps", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "decoder", "_autoConnect", "autoConnect", "open", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "engine", "skipReconnect", "openSubDestroy", "onopen", "errorSub", "cleanup", "emit<PERSON><PERSON><PERSON><PERSON>", "maybeReconnectOnOpen", "timer", "close", "autoUnref", "unref", "onping", "ondata", "onerror", "onclose", "ondecoded", "Socket", "keys", "active", "_close", "options", "subDestroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "StrictEventEmitter", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xd", "xs", "jsonp", "location", "isSSL", "port", "hostname", "secure", "xdomain", "xscheme", "forceJSONP", "hasCORS", "enablesXDR", "XDomainRequest", "concat", "parseqs", "yeast", "Polling", "poll", "onPause", "pause", "total", "doPoll", "onOpen", "doWrite", "schema", "timestampRequests", "timestampParam", "supportsBinary", "sid", "b64", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "prev", "alphabet", "map", "seed", "num", "encoded", "now", "Date", "char<PERSON>t", "socket_io_parser_1", "typed_events_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "disconnected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "onpacket", "subEvents", "unshift", "ev", "compress", "pop", "isTransportWritable", "transport", "discardPacket", "_packet", "onconnect", "BINARY_EVENT", "onevent", "BINARY_ACK", "onack", "ondisconnect", "message", "ack", "emitEvent", "_anyListeners", "sent", "emitBuffered", "listener", "url_1", "manager_1", "lookup", "cache", "managers", "parsed", "url", "sameNamespace", "forceNew", "multiplex", "manager_2", "parseuri", "loc", "test", "ipv6", "href", "transports", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "offlineEventListener", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "onError", "probe", "failed", "onTransportOpen", "send", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "parse", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "cleanupAndClose", "waitForUpgrade", "pingIntervalTimer", "filteredUpgrades", "j", "empty", "hasXHR2", "responseType", "forceBase64", "Request", "req", "request", "method", "onData", "pollXhr", "async", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onload", "onLoad", "responseText", "onreadystatechange", "status", "document", "index", "requestsCount", "requests", "onSuccess", "fromError", "abort", "attachEvent", "unload<PERSON><PERSON><PERSON>", "withNativeBlob", "Blob", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "base64decoder", "decodeBase64Packet", "mapBinary", "base64", "chars", "arraybuffer", "bytes", "Uint8Array", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "navigator", "userAgent", "area", "iframeId", "className", "style", "position", "top", "left", "target", "setAttribute", "complete", "initIframe", "action", "html", "submit", "WebSocket", "usingBrowserWebSocket", "defaultBinaryType", "nextTick", "isReactNative", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "_socket", "onmessage", "lastPacket", "<PERSON><PERSON><PERSON>", "byteLength", "Promise", "resolve", "then", "MozWebSocket", "utf8Write", "view", "offset", "charCodeAt", "setUint8", "defers", "size", "_encode", "hi", "lo", "utf8Length", "_str", "_length", "_offset", "pow", "_float", "time", "getTime", "_bin", "toJSON", "allKeys", "buf", "DataView", "deferIndex", "defer<PERSON><PERSON>ten", "nextOffset", "defer", "deferL<PERSON>th", "bin", "setFloat64", "_buffer", "_view", "byteOffset", "_array", "_parse", "_map", "string", "chr", "end", "byte", "getUint8", "utf8Read", "prefix", "getUint16", "getUint32", "getInt8", "getFloat32", "getFloat64", "getInt16", "getInt32", "ms", "factor", "rand", "random", "deviation"], "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAY,GAAID,IAEhBD,EAAS,GAAIC,IARf,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,I,gBClFrDtC,EAAOD,QACe,oBAATI,KACFA,KACoB,oBAAXoC,OACTA,OAEAC,SAAS,cAATA,I,gBCNX,IAAMC,EAAeC,EAAQ,IACvBC,EAAeD,EAAQ,IAEvBE,EAAYC,OAAOC,aAAa,IAgCtC9C,EAAOD,QAAU,CACfgD,SAAU,EACVN,eACAO,cAjCoB,SAACC,EAASC,GAE9B,IAAMC,EAASF,EAAQE,OACjBC,EAAiB,IAAIC,MAAMF,GAC7BG,EAAQ,EAEZL,EAAQM,SAAQ,SAACC,EAAQjD,GAEvBkC,EAAae,GAAQ,GAAO,SAAAC,GAC1BL,EAAe7C,GAAKkD,IACdH,IAAUH,GACdD,EAASE,EAAeM,KAAKd,WAuBnCD,eACAgB,cAlBoB,SAACC,EAAgBC,GAGrC,IAFA,IAAMT,EAAiBQ,EAAeE,MAAMlB,GACtCK,EAAU,GACP1C,EAAI,EAAGA,EAAI6C,EAAeD,OAAQ5C,IAAK,CAC9C,IAAMwD,EAAgBpB,EAAaS,EAAe7C,GAAIsD,GAEtD,GADAZ,EAAQe,KAAKD,GACc,UAAvBA,EAAcE,KAChB,MAGJ,OAAOhB,K,gBCjBT,SAASiB,EAAQC,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIrC,KAAOoC,EAAQ/B,UACtBgC,EAAIrC,GAAOoC,EAAQ/B,UAAUL,GAE/B,OAAOqC,EAfSC,CAAMD,GAVtBnE,EAAOD,QAAUmE,EAqCnBA,EAAQ/B,UAAUkC,GAClBH,EAAQ/B,UAAUmC,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DP,KAAKQ,GACDC,MAaTP,EAAQ/B,UAAUwC,KAAO,SAASJ,EAAOC,GACvC,SAASH,IACPI,KAAKG,IAAIL,EAAOF,GAChBG,EAAGK,MAAMJ,KAAMK,WAKjB,OAFAT,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTP,EAAQ/B,UAAUyC,IAClBV,EAAQ/B,UAAU4C,eAClBb,EAAQ/B,UAAU6C,mBAClBd,EAAQ/B,UAAU8C,oBAAsB,SAASV,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKI,UAAU3B,OAEjB,OADAsB,KAAKC,WAAa,GACXD,KAIT,IAUIS,EAVAC,EAAYV,KAAKC,WAAW,IAAMH,GACtC,IAAKY,EAAW,OAAOV,KAGvB,GAAI,GAAKK,UAAU3B,OAEjB,cADOsB,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAIlE,EAAI,EAAGA,EAAI4E,EAAUhC,OAAQ5C,IAEpC,IADA2E,EAAKC,EAAU5E,MACJiE,GAAMU,EAAGV,KAAOA,EAAI,CAC7BW,EAAUC,OAAO7E,EAAG,GACpB,MAUJ,OAJyB,IAArB4E,EAAUhC,eACLsB,KAAKC,WAAW,IAAMH,GAGxBE,MAWTP,EAAQ/B,UAAUkD,KAAO,SAASd,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIY,EAAO,IAAIjC,MAAMyB,UAAU3B,OAAS,GACpCgC,EAAYV,KAAKC,WAAW,IAAMH,GAE7BhE,EAAI,EAAGA,EAAIuE,UAAU3B,OAAQ5C,IACpC+E,EAAK/E,EAAI,GAAKuE,UAAUvE,GAG1B,GAAI4E,EAEG,CAAI5E,EAAI,EAAb,IAAK,IAAWgF,GADhBJ,EAAYA,EAAUK,MAAM,IACIrC,OAAQ5C,EAAIgF,IAAOhF,EACjD4E,EAAU5E,GAAGsE,MAAMJ,KAAMa,GAI7B,OAAOb,MAWTP,EAAQ/B,UAAUsD,UAAY,SAASlB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,IAWzCL,EAAQ/B,UAAUuD,aAAe,SAASnB,GACxC,QAAUE,KAAKgB,UAAUlB,GAAOpB,S,gBC7KlC,IAAMwC,EAAajD,EAAQ,GAE3B1C,EAAOD,QAAQ6F,KAAO,SAACzB,GAAiB,2BAAT0B,EAAS,iCAATA,EAAS,kBACtC,OAAOA,EAAKC,QAAO,SAACC,EAAKC,GAIvB,OAHI7B,EAAI/B,eAAe4D,KACrBD,EAAIC,GAAK7B,EAAI6B,IAERD,IACN,KAIL,IAAME,EAAqBC,WACrBC,EAAuBC,aAE7BpG,EAAOD,QAAQsG,sBAAwB,SAAClC,EAAKmC,GACvCA,EAAKC,iBACPpC,EAAIqC,aAAeP,EAAmBlE,KAAK4D,GAC3CxB,EAAIsC,eAAiBN,EAAqBpE,KAAK4D,KAE/CxB,EAAIqC,aAAeN,WAAWnE,KAAK4D,GACnCxB,EAAIsC,eAAiBL,aAAarE,KAAK4D,M,20CCrB3C,IAAMe,EAAShE,EAAQ,GACjBwB,EAAUxB,EAAQ,GAChB2D,EAA0B3D,EAAQ,GAAlC2D,sBAIFM,E,sQAOJ,WAAYL,GAAM,a,4FAAA,SAChB,eACAD,EAAsB,EAAD,GAAOC,GAE5B,EAAKA,KAAOA,EACZ,EAAKM,MAAQN,EAAKM,MAClB,EAAKC,WAAa,GAClB,EAAKC,OAASR,EAAKQ,OAPH,E,oCAiBlB,SAAQC,EAAKC,GACX,IAAMC,EAAM,IAAIC,MAAMH,GAItB,OAHAE,EAAIhD,KAAO,iBACXgD,EAAIE,YAAcH,EAClBvC,KAAKY,KAAK,QAAS4B,GACZxC,O,kBAQT,WAME,MALI,WAAaA,KAAKoC,YAAc,KAAOpC,KAAKoC,aAC9CpC,KAAKoC,WAAa,UAClBpC,KAAK2C,UAGA3C,O,mBAQT,WAME,MALI,YAAcA,KAAKoC,YAAc,SAAWpC,KAAKoC,aACnDpC,KAAK4C,UACL5C,KAAK6C,WAGA7C,O,kBAST,SAAKxB,GACC,SAAWwB,KAAKoC,YAClBpC,KAAK8C,MAAMtE,K,oBAaf,WACEwB,KAAKoC,WAAa,OAClBpC,KAAK+C,UAAW,EAChB/C,KAAKY,KAAK,U,oBASZ,SAAOoC,GACL,IAAMjE,EAASkD,EAAO/D,aAAa8E,EAAMhD,KAAKqC,OAAOjD,YACrDY,KAAKiD,SAASlE,K,sBAMhB,SAASA,GACPiB,KAAKY,KAAK,SAAU7B,K,qBAQtB,WACEiB,KAAKoC,WAAa,SAClBpC,KAAKY,KAAK,c,8BAhHUnB,GAoHxBlE,EAAOD,QAAU4G,G,cClHjB5G,EAAQ4H,OAAS,SAAUxD,GACzB,IAAIyD,EAAM,GAEV,IAAK,IAAIrH,KAAK4D,EACRA,EAAI/B,eAAe7B,KACjBqH,EAAIzE,SAAQyE,GAAO,KACvBA,GAAOC,mBAAmBtH,GAAK,IAAMsH,mBAAmB1D,EAAI5D,KAIhE,OAAOqH,GAUT7H,EAAQ+H,OAAS,SAASC,GAGxB,IAFA,IAAIC,EAAM,GACNC,EAAQF,EAAGjE,MAAM,KACZvD,EAAI,EAAGC,EAAIyH,EAAM9E,OAAQ5C,EAAIC,EAAGD,IAAK,CAC5C,IAAI2H,EAAOD,EAAM1H,GAAGuD,MAAM,KAC1BkE,EAAIG,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE7D,OAAOF,I,gBCnCT,IAAII,EAAU1F,EAAQ,IAClBwB,EAAUxB,EAAQ,GAEtB3C,EAAQgD,SAAW,EAMnB,IAAIsF,EAActI,EAAQsI,WAAa,CACrCC,QAAS,EACTC,WAAY,EACZC,MAAO,EACPC,IAAK,EACLC,cAAe,GAGbC,EACFC,OAAOD,WACP,SAAUnH,GACR,MACmB,iBAAVA,GACPqH,SAASrH,IACTsH,KAAKC,MAAMvH,KAAWA,GAIxBwH,EAAW,SAAUxH,GACvB,MAAwB,iBAAVA,GAGZyH,EAAW,SAAUzH,GACvB,MAAiD,oBAA1CP,OAAOkB,UAAU+G,SAASxI,KAAKc,IAGxC,SAAS2H,KAMT,SAASC,KAJTD,EAAQhH,UAAUwF,OAAS,SAAUnE,GACnC,MAAO,CAAC4E,EAAQT,OAAOnE,KAKzBU,EAAQkF,EAAQjH,WAEhBiH,EAAQjH,UAAUkH,IAAM,SAAUlF,GAChC,IAAImF,EAAUlB,EAAQN,OAAO3D,GAC7BM,KAAK8E,YAAYD,GACjB7E,KAAKY,KAAK,UAAWiE,IAgBvBF,EAAQjH,UAAUoH,YAAc,SAAUD,GAKxC,KAHEX,EAAUW,EAAQrF,OAClBqF,EAAQrF,MAAQoE,EAAWC,SAC3BgB,EAAQrF,MAAQoE,EAAWK,eAE3B,MAAM,IAAIxB,MAAM,uBAGlB,IAAK8B,EAASM,EAAQE,KACpB,MAAM,IAAItC,MAAM,qBAGlB,IA1BF,SAAqBoC,GACnB,OAAQA,EAAQrF,MACd,KAAKoE,EAAWC,QACd,YAAwBmB,IAAjBH,EAAQ7B,MAAsBwB,EAASK,EAAQ7B,MACxD,KAAKY,EAAWE,WACd,YAAwBkB,IAAjBH,EAAQ7B,KACjB,KAAKY,EAAWK,cACd,OAAOM,EAASM,EAAQ7B,OAASwB,EAASK,EAAQ7B,MACpD,QACE,OAAOpE,MAAMqG,QAAQJ,EAAQ7B,OAiB5BkC,CAAYL,GACf,MAAM,IAAIpC,MAAM,mBAIlB,UADgCuC,IAAfH,EAAQM,IAAoBjB,EAAUW,EAAQM,KAE7D,MAAM,IAAI1C,MAAM,sBAIpBkC,EAAQjH,UAAU0H,QAAU,aAE5B9J,EAAQoJ,QAAUA,EAClBpJ,EAAQqJ,QAAUA,G,cCnFlB,IAAIU,EAAK,0OAELC,EAAQ,CACR,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAGzI/J,EAAOD,QAAU,SAAkB6H,GAC/B,IAAIoC,EAAMpC,EACNqC,EAAIrC,EAAIsC,QAAQ,KAChBC,EAAIvC,EAAIsC,QAAQ,MAEV,GAAND,IAAiB,GAANE,IACXvC,EAAMA,EAAIwC,UAAU,EAAGH,GAAKrC,EAAIwC,UAAUH,EAAGE,GAAGE,QAAQ,KAAM,KAAOzC,EAAIwC,UAAUD,EAAGvC,EAAIzE,SAO9F,IAJA,IAmCmByD,EACfa,EApCA9G,EAAImJ,EAAGQ,KAAK1C,GAAO,IACnB2C,EAAM,GACNhK,EAAI,GAEDA,KACHgK,EAAIR,EAAMxJ,IAAMI,EAAEJ,IAAM,GAa5B,OAVU,GAAN0J,IAAiB,GAANE,IACXI,EAAIC,OAASR,EACbO,EAAIE,KAAOF,EAAIE,KAAKL,UAAU,EAAGG,EAAIE,KAAKtH,OAAS,GAAGkH,QAAQ,KAAM,KACpEE,EAAIG,UAAYH,EAAIG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EE,EAAII,SAAU,GAGlBJ,EAAIK,UAMR,SAAmBzG,EAAK0G,GACpB,IACIC,EAAQD,EAAKR,QADN,WACoB,KAAKvG,MAAM,KAEjB,KAArB+G,EAAKE,OAAO,EAAG,IAA6B,IAAhBF,EAAK1H,QACjC2H,EAAM1F,OAAO,EAAG,GAEmB,KAAnCyF,EAAKE,OAAOF,EAAK1H,OAAS,EAAG,IAC7B2H,EAAM1F,OAAO0F,EAAM3H,OAAS,EAAG,GAGnC,OAAO2H,EAjBSF,CAAUL,EAAKA,EAAG,MAClCA,EAAIS,UAmBepE,EAnBU2D,EAAG,MAoB5B9C,EAAO,GAEXb,EAAMyD,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACAzD,EAAKyD,GAAMC,MAIZ1D,GA1BA8C,I,w1CCvCXtJ,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQqL,aAAU,EAClB,IAAMC,EAAM3I,EAAQ,IACd4I,EAAS5I,EAAQ,GACjB6I,EAAW7I,EAAQ,IACnBgE,EAAShE,EAAQ,GACjB8I,EAAO9I,EAAQ,IACf+I,EAAU/I,EAAQ,IAIlB0I,E,sQACF,WAAYb,EAAKjE,GAAM,MACfoF,G,4FADe,UAEnB,gBACKC,KAAO,GACZ,EAAKC,KAAO,GACRrB,GAAO,WAAa,EAAOA,KAC3BjE,EAAOiE,EACPA,OAAMd,IAEVnD,EAAOA,GAAQ,IACVuE,KAAOvE,EAAKuE,MAAQ,aACzB,EAAKvE,KAAOA,GACZ,EAAIgF,EAAOjF,uBAAX,KAAwCC,GACxC,EAAKuF,cAAmC,IAAtBvF,EAAKuF,cACvB,EAAKC,qBAAqBxF,EAAKwF,sBAAwBC,KACvD,EAAKC,kBAAkB1F,EAAK0F,mBAAqB,KACjD,EAAKC,qBAAqB3F,EAAK2F,sBAAwB,KACvD,EAAKC,oBAAwD,QAAnCR,EAAKpF,EAAK4F,2BAAwC,IAAPR,EAAgBA,EAAK,IAC1F,EAAKS,QAAU,IAAIV,EAAQ,CACvBW,IAAK,EAAKJ,oBACVK,IAAK,EAAKJ,uBACVK,OAAQ,EAAKJ,wBAEjB,EAAKK,QAAQ,MAAQjG,EAAKiG,QAAU,IAAQjG,EAAKiG,SACjD,EAAKC,YAAc,SACnB,EAAKjC,IAAMA,EACX,IAAMkC,EAAUnG,EAAKI,QAAUA,EA1BZ,OA2BnB,EAAKgG,QAAU,IAAID,EAAQtD,QAC3B,EAAKwD,QAAU,IAAIF,EAAQrD,QAC3B,EAAKwD,cAAoC,IAArBtG,EAAKuG,YACrB,EAAKD,cACL,EAAKE,OA/BU,E,yCAiCvB,SAAaC,GACT,OAAKjI,UAAU3B,QAEfsB,KAAKuI,gBAAkBD,EAChBtI,MAFIA,KAAKuI,gB,kCAIpB,SAAqBD,GACjB,YAAUtD,IAANsD,EACOtI,KAAKwI,uBAChBxI,KAAKwI,sBAAwBF,EACtBtI,Q,+BAEX,SAAkBsI,GACd,IAAIrB,EACJ,YAAUjC,IAANsD,EACOtI,KAAKyI,oBAChBzI,KAAKyI,mBAAqBH,EACF,QAAvBrB,EAAKjH,KAAK0H,eAA4B,IAAPT,GAAyBA,EAAGyB,OAAOJ,GAC5DtI,Q,iCAEX,SAAoBsI,GAChB,IAAIrB,EACJ,YAAUjC,IAANsD,EACOtI,KAAK2I,sBAChB3I,KAAK2I,qBAAuBL,EACJ,QAAvBrB,EAAKjH,KAAK0H,eAA4B,IAAPT,GAAyBA,EAAG2B,UAAUN,GAC/DtI,Q,kCAEX,SAAqBsI,GACjB,IAAIrB,EACJ,YAAUjC,IAANsD,EACOtI,KAAK6I,uBAChB7I,KAAK6I,sBAAwBP,EACL,QAAvBrB,EAAKjH,KAAK0H,eAA4B,IAAPT,GAAyBA,EAAG6B,OAAOR,GAC5DtI,Q,qBAEX,SAAQsI,GACJ,OAAKjI,UAAU3B,QAEfsB,KAAK+I,SAAWT,EACTtI,MAFIA,KAAK+I,W,kCAUpB,YAES/I,KAAKgJ,eACNhJ,KAAKuI,eACqB,IAA1BvI,KAAK0H,QAAQuB,UAEbjJ,KAAKkJ,c,kBAUb,SAAKnJ,GAAI,WAGL,IAAKC,KAAK+H,YAAYtC,QAAQ,QAC1B,OAAOzF,KAGXA,KAAKmJ,OAASvC,EAAI5G,KAAK8F,IAAK9F,KAAK6B,MACjC,IAAMQ,EAASrC,KAAKmJ,OACdzN,EAAOsE,KACbA,KAAK+H,YAAc,UACnB/H,KAAKoJ,eAAgB,EAErB,IAAMC,GAAiB,EAAItC,EAAKnH,IAAIyC,EAAQ,QAAQ,WAChD3G,EAAK4N,SACLvJ,GAAMA,OAGJwJ,GAAW,EAAIxC,EAAKnH,IAAIyC,EAAQ,SAAS,SAACG,GAG5C9G,EAAK8N,UACL9N,EAAKqM,YAAc,SACnB,EAAK0B,aAAa,QAASjH,GACvBzC,EACAA,EAAGyC,GAIH9G,EAAKgO,0BAGb,IAAI,IAAU1J,KAAK+I,SAAU,CACzB,IAAMjB,EAAU9H,KAAK+I,SAGL,IAAZjB,GACAuB,IAGJ,IAAMM,EAAQ3J,KAAK+B,cAAa,WAG5BsH,IACAhH,EAAOuH,QACPvH,EAAOzB,KAAK,QAAS,IAAI6B,MAAM,cAChCqF,GACC9H,KAAK6B,KAAKgI,WACVF,EAAMG,QAEV9J,KAAKmH,KAAK5H,MAAK,WACXoC,aAAagI,MAKrB,OAFA3J,KAAKmH,KAAK5H,KAAK8J,GACfrJ,KAAKmH,KAAK5H,KAAKgK,GACRvJ,O,qBAQX,SAAQD,GACJ,OAAOC,KAAKqI,KAAKtI,K,oBAOrB,WAIIC,KAAKwJ,UAELxJ,KAAK+H,YAAc,OACnB/H,KAAKyJ,aAAa,QAElB,IAAMpH,EAASrC,KAAKmJ,OACpBnJ,KAAKmH,KAAK5H,MAAK,EAAIwH,EAAKnH,IAAIyC,EAAQ,OAAQrC,KAAK+J,OAAOzM,KAAK0C,QAAQ,EAAI+G,EAAKnH,IAAIyC,EAAQ,OAAQrC,KAAKgK,OAAO1M,KAAK0C,QAAQ,EAAI+G,EAAKnH,IAAIyC,EAAQ,QAASrC,KAAKiK,QAAQ3M,KAAK0C,QAAQ,EAAI+G,EAAKnH,IAAIyC,EAAQ,QAASrC,KAAKkK,QAAQ5M,KAAK0C,QAAQ,EAAI+G,EAAKnH,IAAII,KAAKkI,QAAS,UAAWlI,KAAKmK,UAAU7M,KAAK0C,U,oBAOzS,WACIA,KAAKyJ,aAAa,U,oBAOtB,SAAOzG,GACHhD,KAAKkI,QAAQtD,IAAI5B,K,uBAOrB,SAAUjE,GACNiB,KAAKyJ,aAAa,SAAU1K,K,qBAOhC,SAAQyD,GAGJxC,KAAKyJ,aAAa,QAASjH,K,oBAQ/B,SAAOuC,EAAKlD,GACR,IAAIQ,EAASrC,KAAKkH,KAAKnC,GAKvB,OAJK1C,IACDA,EAAS,IAAIyE,EAASsD,OAAOpK,KAAM+E,EAAKlD,GACxC7B,KAAKkH,KAAKnC,GAAO1C,GAEdA,I,sBAQX,SAASA,GAEL,IADA,IACA,MADa7F,OAAO6N,KAAKrK,KAAKkH,MAC9B,eAAwB,CAAnB,IAAMnC,EAAG,KAEV,GADe/E,KAAKkH,KAAKnC,GACduF,OAGP,OAGRtK,KAAKuK,W,qBAQT,SAAQxL,GAIJ,IADA,IAAMJ,EAAiBqB,KAAKiI,QAAQ/E,OAAOnE,GAClCjD,EAAI,EAAGA,EAAI6C,EAAeD,OAAQ5C,IACvCkE,KAAKmJ,OAAOrG,MAAMnE,EAAe7C,GAAIiD,EAAOyL,W,qBAQpD,WAGIxK,KAAKmH,KAAKrI,SAAQ,SAAC2L,GAAD,OAAgBA,OAClCzK,KAAKmH,KAAKzI,OAAS,EACnBsB,KAAKkI,QAAQ9C,Y,oBAOjB,WAGIpF,KAAKoJ,eAAgB,EACrBpJ,KAAKgJ,eAAgB,EACjB,YAAchJ,KAAK+H,aAGnB/H,KAAKwJ,UAETxJ,KAAK0H,QAAQgD,QACb1K,KAAK+H,YAAc,SACf/H,KAAKmJ,QACLnJ,KAAKmJ,OAAOS,U,wBAOpB,WACI,OAAO5J,KAAKuK,W,qBAOhB,SAAQI,GAGJ3K,KAAKwJ,UACLxJ,KAAK0H,QAAQgD,QACb1K,KAAK+H,YAAc,SACnB/H,KAAKyJ,aAAa,QAASkB,GACvB3K,KAAKuI,gBAAkBvI,KAAKoJ,eAC5BpJ,KAAKkJ,c,uBAQb,WAAY,WACR,GAAIlJ,KAAKgJ,eAAiBhJ,KAAKoJ,cAC3B,OAAOpJ,KACX,IAAMtE,EAAOsE,KACb,GAAIA,KAAK0H,QAAQuB,UAAYjJ,KAAKwI,sBAG9BxI,KAAK0H,QAAQgD,QACb1K,KAAKyJ,aAAa,oBAClBzJ,KAAKgJ,eAAgB,MAEpB,CACD,IAAM4B,EAAQ5K,KAAK0H,QAAQmD,WAG3B7K,KAAKgJ,eAAgB,EACrB,IAAMW,EAAQ3J,KAAK+B,cAAa,WACxBrG,EAAK0N,gBAIT,EAAKK,aAAa,oBAAqB/N,EAAKgM,QAAQuB,UAEhDvN,EAAK0N,eAET1N,EAAK2M,MAAK,SAAC7F,GACHA,GAGA9G,EAAKsN,eAAgB,EACrBtN,EAAKwN,YACL,EAAKO,aAAa,kBAAmBjH,IAKrC9G,EAAKoP,oBAGdF,GACC5K,KAAK6B,KAAKgI,WACVF,EAAMG,QAEV9J,KAAKmH,KAAK5H,MAAK,WACXoC,aAAagI,S,yBASzB,WACI,IAAMoB,EAAU/K,KAAK0H,QAAQuB,SAC7BjJ,KAAKgJ,eAAgB,EACrBhJ,KAAK0H,QAAQgD,QACb1K,KAAKyJ,aAAa,YAAasB,Q,8BA/XhB9M,EAAQ,IAGM+M,oBA+XrC1P,EAAQqL,QAAUA,G,gBC3YlB,IAAMsE,EAAiBhN,EAAQ,IACzBiN,EAAMjN,EAAQ,IACdkN,EAAQlN,EAAQ,IAChBmN,EAAYnN,EAAQ,IAE1B3C,EAAQ+P,QAUR,SAAiBxJ,GACf,IACIyJ,GAAK,EACLC,GAAK,EACHC,GAAQ,IAAU3J,EAAK2J,MAE7B,GAAwB,oBAAbC,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASnN,SAChCqN,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvBJ,EAAKzJ,EAAK+J,WAAaH,SAASG,UAAYD,IAAS9J,EAAK8J,KAC1DJ,EAAK1J,EAAKgK,SAAWH,EAOvB,GAJA7J,EAAKiK,QAAUR,EACfzJ,EAAKkK,QAAUR,EAGX,SAFE,IAAIN,EAAepJ,KAEHA,EAAKmK,WACzB,OAAO,IAAId,EAAIrJ,GAEf,IAAK2J,EAAO,MAAM,IAAI/I,MAAM,kBAC5B,OAAO,IAAI0I,EAAMtJ,IApCrBvG,EAAQ8P,UAAYA,G,gBCJpB,IAAMa,EAAUhO,EAAQ,IAClBiD,EAAajD,EAAQ,GAE3B1C,EAAOD,QAAU,SAASuG,GACxB,IAAMiK,EAAUjK,EAAKiK,QAIfC,EAAUlK,EAAKkK,QAIfG,EAAarK,EAAKqK,WAGxB,IACE,GAAI,oBAAuBjB,kBAAoBa,GAAWG,GACxD,OAAO,IAAIhB,eAEb,MAAOvF,IAKT,IACE,GAAI,oBAAuByG,iBAAmBJ,GAAWG,EACvD,OAAO,IAAIC,eAEb,MAAOzG,IAET,IAAKoG,EACH,IACE,OAAO,IAAI5K,EAAW,CAAC,UAAUkL,OAAO,UAAUnN,KAAK,OACrD,qBAEF,MAAOyG,O,s6CCrCb,IAAMxD,EAAYjE,EAAQ,GACpBoO,EAAUpO,EAAQ,GAClBgE,EAAShE,EAAQ,GACjBqO,EAAQrO,EAAQ,IAKhBsO,E,2VAIJ,WACE,MAAO,Y,oBAST,WACEvM,KAAKwM,S,mBASP,SAAMC,GAAS,WACbzM,KAAKoC,WAAa,UAElB,IAAMsK,EAAQ,WAGZ,EAAKtK,WAAa,SAClBqK,KAGF,GAAIzM,KAAKqL,UAAYrL,KAAK+C,SAAU,CAClC,IAAI4J,EAAQ,EAER3M,KAAKqL,UAGPsB,IACA3M,KAAKE,KAAK,gBAAgB,aAGtByM,GAASD,QAIV1M,KAAK+C,WAGR4J,IACA3M,KAAKE,KAAK,SAAS,aAGfyM,GAASD,aAIfA,M,kBASJ,WAGE1M,KAAKqL,SAAU,EACfrL,KAAK4M,SACL5M,KAAKY,KAAK,U,oBAQZ,SAAOoC,GAAM,WAoBXf,EAAO/C,cAAc8D,EAAMhD,KAAKqC,OAAOjD,YAAYN,SAjBlC,SAAAC,GAOf,GALI,YAAc,EAAKqD,YAA8B,SAAhBrD,EAAOS,MAC1C,EAAKqN,SAIH,UAAY9N,EAAOS,KAErB,OADA,EAAKqD,WACE,EAIT,EAAKI,SAASlE,MAOZ,WAAaiB,KAAKoC,aAEpBpC,KAAKqL,SAAU,EACfrL,KAAKY,KAAK,gBAEN,SAAWZ,KAAKoC,YAClBpC,KAAKwM,U,qBAaX,WAAU,WACF5C,EAAQ,WAGZ,EAAK9G,MAAM,CAAC,CAAEtD,KAAM,YAGlB,SAAWQ,KAAKoC,WAGlBwH,IAMA5J,KAAKE,KAAK,OAAQ0J,K,mBAWtB,SAAMpL,GAAS,WACbwB,KAAK+C,UAAW,EAEhBd,EAAO1D,cAAcC,GAAS,SAAAwE,GAC5B,EAAK8J,QAAQ9J,GAAM,WACjB,EAAKD,UAAW,EAChB,EAAKnC,KAAK,iB,iBAUhB,WACE,IAAIuB,EAAQnC,KAAKmC,OAAS,GACpB4K,EAAS/M,KAAK6B,KAAKgK,OAAS,QAAU,OACxCF,EAAO,GA4BX,OAzBI,IAAU3L,KAAK6B,KAAKmL,oBACtB7K,EAAMnC,KAAK6B,KAAKoL,gBAAkBX,KAG/BtM,KAAKkN,gBAAmB/K,EAAMgL,MACjChL,EAAMiL,IAAM,GAGdjL,EAAQkK,EAAQnJ,OAAOf,GAIrBnC,KAAK6B,KAAK8J,OACR,UAAYoB,GAAqC,MAA3B5I,OAAOnE,KAAK6B,KAAK8J,OACtC,SAAWoB,GAAqC,KAA3B5I,OAAOnE,KAAK6B,KAAK8J,SAEzCA,EAAO,IAAM3L,KAAK6B,KAAK8J,MAIrBxJ,EAAMzD,SACRyD,EAAQ,IAAMA,GAKd4K,EACA,QAHgD,IAArC/M,KAAK6B,KAAK+J,SAASnG,QAAQ,KAI9B,IAAMzF,KAAK6B,KAAK+J,SAAW,IAAM5L,KAAK6B,KAAK+J,UACnDD,EACA3L,KAAK6B,KAAKuE,KACVjE,O,8BA3MgBD,GAgNtB3G,EAAOD,QAAUiR,G,cCxNjB,IAAMc,EAAe7Q,OAAOY,OAAO,MACnCiQ,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IAEvB,IAAMC,EAAuB9Q,OAAOY,OAAO,MAC3CZ,OAAO6N,KAAKgD,GAAcvO,SAAQ,SAAAzB,GAChCiQ,EAAqBD,EAAahQ,IAAQA,KAK5C9B,EAAOD,QAAU,CACf+R,eACAC,uBACAC,aALmB,CAAE/N,KAAM,QAASwD,KAAM,kB,6BCZ5C,IAKIwK,EALAC,EAAW,mEAAmEpO,MAAM,IAEpFqO,EAAM,GACNC,EAAO,EACP7R,EAAI,EAUR,SAASoH,EAAO0K,GACd,IAAIC,EAAU,GAEd,GACEA,EAAUJ,EAASG,EAjBV,IAiB0BC,EACnCD,EAAMvJ,KAAKC,MAAMsJ,EAlBR,UAmBFA,EAAM,GAEf,OAAOC,EA0BT,SAASvB,IACP,IAAIwB,EAAM5K,GAAQ,IAAI6K,MAEtB,OAAID,IAAQN,GAAaG,EAAO,EAAGH,EAAOM,GACnCA,EAAK,IAAK5K,EAAOyK,KAM1B,KAAO7R,EAzDM,GAyDMA,IAAK4R,EAAID,EAAS3R,IAAMA,EAK3CwQ,EAAMpJ,OAASA,EACfoJ,EAAMjJ,OAhCN,SAAgBF,GACd,IAAI0B,EAAU,EAEd,IAAK/I,EAAI,EAAGA,EAAIqH,EAAIzE,OAAQ5C,IAC1B+I,EAnCS,GAmCCA,EAAmB6I,EAAIvK,EAAI6K,OAAOlS,IAG9C,OAAO+I,GA0BTtJ,EAAOD,QAAUgR,G,gpFClEjB9P,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ8O,YAAS,EACjB,IAAM6D,EAAqBhQ,EAAQ,GAC7B8I,EAAO9I,EAAQ,IACfiQ,EAAiBjQ,EAAQ,IAOzBkQ,EAAkB3R,OAAO4R,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbnO,eAAgB,IAEd8J,E,sQAMF,WAAYsE,EAAI3J,EAAKlD,GAAM,a,4FAAA,UACvB,gBACK8M,WAAY,EACjB,EAAKC,cAAe,EACpB,EAAKC,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKC,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKC,MAAQ,GACb,EAAKP,GAAKA,EACV,EAAK3J,IAAMA,EACPlD,GAAQA,EAAKqN,OACb,EAAKA,KAAOrN,EAAKqN,MAEjB,EAAKR,GAAGvG,cACR,EAAKE,OAfc,E,sCAsB3B,WACI,IAAIrI,KAAKmH,KAAT,CAEA,IAAMuH,EAAK1O,KAAK0O,GAChB1O,KAAKmH,KAAO,EACR,EAAIJ,EAAKnH,IAAI8O,EAAI,OAAQ1O,KAAKsJ,OAAOhM,KAAK0C,QAC1C,EAAI+G,EAAKnH,IAAI8O,EAAI,SAAU1O,KAAKmP,SAAS7R,KAAK0C,QAC9C,EAAI+G,EAAKnH,IAAI8O,EAAI,QAAS1O,KAAKiK,QAAQ3M,KAAK0C,QAC5C,EAAI+G,EAAKnH,IAAI8O,EAAI,QAAS1O,KAAKkK,QAAQ5M,KAAK0C,W,kBAMpD,WACI,QAASA,KAAKmH,O,qBAOlB,WACI,OAAInH,KAAK2O,YAET3O,KAAKoP,YACApP,KAAK0O,GAAL,eACD1O,KAAK0O,GAAGrG,OACR,SAAWrI,KAAK0O,GAAG3G,aACnB/H,KAAKsJ,UALEtJ,O,kBAWf,WACI,OAAOA,KAAKqO,Y,kBAQhB,WAAc,2BAANxN,EAAM,yBAANA,EAAM,gBAGV,OAFAA,EAAKwO,QAAQ,WACbrP,KAAKY,KAAKR,MAAMJ,KAAMa,GACfb,O,kBASX,SAAKsP,GACD,GAAInB,EAAgBxQ,eAAe2R,GAC/B,MAAM,IAAI7M,MAAM,IAAM6M,EAAK,8BAFjB,2BAANzO,EAAM,iCAANA,EAAM,kBAIdA,EAAKwO,QAAQC,GACb,IAAMvQ,EAAS,CACXS,KAAMyO,EAAmBrK,WAAWG,MACpCf,KAAMnC,EAEV9B,QAAiB,IACjBA,EAAOyL,QAAQ+E,UAAmC,IAAxBvP,KAAKiP,MAAMM,SAEjC,mBAAsB1O,EAAKA,EAAKnC,OAAS,KAGzCsB,KAAKgP,KAAKhP,KAAK+O,KAAOlO,EAAK2O,MAC3BzQ,EAAOoG,GAAKnF,KAAK+O,OAErB,IAAMU,EAAsBzP,KAAK0O,GAAGvF,QAChCnJ,KAAK0O,GAAGvF,OAAOuG,WACf1P,KAAK0O,GAAGvF,OAAOuG,UAAU3M,SACvB4M,EAAgB3P,KAAKiP,MAAL,YAAyBQ,IAAwBzP,KAAK2O,WAY5E,OAXIgB,IAIK3P,KAAK2O,UACV3O,KAAKjB,OAAOA,GAGZiB,KAAK8O,WAAWvP,KAAKR,IAEzBiB,KAAKiP,MAAQ,GACNjP,O,oBAQX,SAAOjB,GACHA,EAAOgG,IAAM/E,KAAK+E,IAClB/E,KAAK0O,GAAGkB,QAAQ7Q,K,oBAOpB,WAAS,WAGmB,mBAAbiB,KAAKkP,KACZlP,KAAKkP,MAAK,SAAClM,GACP,EAAKjE,OAAO,CAAES,KAAMyO,EAAmBrK,WAAWC,QAASb,YAI/DhD,KAAKjB,OAAO,CAAES,KAAMyO,EAAmBrK,WAAWC,QAASb,KAAMhD,KAAKkP,S,qBAS9E,SAAQ1M,GACCxC,KAAK2O,WACN3O,KAAKyJ,aAAa,gBAAiBjH,K,qBAS3C,SAAQmI,GAGJ3K,KAAK2O,WAAY,EACjB3O,KAAK4O,cAAe,SACb5O,KAAKmF,GACZnF,KAAKyJ,aAAa,aAAckB,K,sBAQpC,SAAS5L,GAEL,GADsBA,EAAOgG,MAAQ/E,KAAK+E,IAG1C,OAAQhG,EAAOS,MACX,KAAKyO,EAAmBrK,WAAWC,QAC/B,GAAI9E,EAAOiE,MAAQjE,EAAOiE,KAAKmK,IAAK,CAChC,IAAMhI,EAAKpG,EAAOiE,KAAKmK,IACvBnN,KAAK6P,UAAU1K,QAGfnF,KAAKyJ,aAAa,gBAAiB,IAAIhH,MAAM,8LAEjD,MACJ,KAAKwL,EAAmBrK,WAAWG,MAGnC,KAAKkK,EAAmBrK,WAAWkM,aAC/B9P,KAAK+P,QAAQhR,GACb,MACJ,KAAKkP,EAAmBrK,WAAWI,IAGnC,KAAKiK,EAAmBrK,WAAWoM,WAC/BhQ,KAAKiQ,MAAMlR,GACX,MACJ,KAAKkP,EAAmBrK,WAAWE,WAC/B9D,KAAKkQ,eACL,MACJ,KAAKjC,EAAmBrK,WAAWK,cAC/B,IAAMzB,EAAM,IAAIC,MAAM1D,EAAOiE,KAAKmN,SAElC3N,EAAIQ,KAAOjE,EAAOiE,KAAKA,KACvBhD,KAAKyJ,aAAa,gBAAiBjH,M,qBAU/C,SAAQzD,GACJ,IAAM8B,EAAO9B,EAAOiE,MAAQ,GAGxB,MAAQjE,EAAOoG,IAGftE,EAAKtB,KAAKS,KAAKoQ,IAAIrR,EAAOoG,KAE1BnF,KAAK2O,UACL3O,KAAKqQ,UAAUxP,GAGfb,KAAK6O,cAActP,KAAK/C,OAAO4R,OAAOvN,M,uBAG9C,SAAUA,GACN,GAAIb,KAAKsQ,eAAiBtQ,KAAKsQ,cAAc5R,OAAQ,CACjD,IADiD,MAC/BsB,KAAKsQ,cAAcvP,SADY,IAEjD,2BAAkC,QACrBX,MAAMJ,KAAMa,GAHwB,+BAMrD,8BAAWT,MAAMJ,KAAMa,K,iBAO3B,SAAIsE,GACA,IAAMzJ,EAAOsE,KACTuQ,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,2BAAN1P,EAAM,yBAANA,EAAM,gBAOtBnF,EAAKqD,OAAO,CACRS,KAAMyO,EAAmBrK,WAAWI,IACpCmB,GAAIA,EACJnC,KAAMnC,Q,mBAUlB,SAAM9B,GACF,IAAMqR,EAAMpQ,KAAKgP,KAAKjQ,EAAOoG,IACzB,mBAAsBiL,IAGtBA,EAAIhQ,MAAMJ,KAAMjB,EAAOiE,aAChBhD,KAAKgP,KAAKjQ,EAAOoG,O,uBAYhC,SAAUA,GAGNnF,KAAKmF,GAAKA,EACVnF,KAAK2O,WAAY,EACjB3O,KAAK4O,cAAe,EACpB5O,KAAKwQ,eACLxQ,KAAKyJ,aAAa,a,0BAOtB,WAAe,WACXzJ,KAAK6O,cAAc/P,SAAQ,SAAC+B,GAAD,OAAU,EAAKwP,UAAUxP,MACpDb,KAAK6O,cAAgB,GACrB7O,KAAK8O,WAAWhQ,SAAQ,SAACC,GAAD,OAAY,EAAKA,OAAOA,MAChDiB,KAAK8O,WAAa,K,0BAOtB,WAGI9O,KAAKoF,UACLpF,KAAKkK,QAAQ,0B,qBASjB,WACQlK,KAAKmH,OAELnH,KAAKmH,KAAKrI,SAAQ,SAAC2L,GAAD,OAAgBA,OAClCzK,KAAKmH,UAAOnC,GAEhBhF,KAAK0O,GAAL,SAAoB1O,Q,wBAQxB,WAYI,OAXIA,KAAK2O,WAGL3O,KAAKjB,OAAO,CAAES,KAAMyO,EAAmBrK,WAAWE,aAGtD9D,KAAKoF,UACDpF,KAAK2O,WAEL3O,KAAKkK,QAAQ,wBAEVlK,O,mBAQX,WACI,OAAOA,KAAKuO,e,sBAShB,SAASgB,GAEL,OADAvP,KAAKiP,MAAMM,SAAWA,EACfvP,O,oBASX,WAEI,OADAA,KAAKiP,MAAL,UAAsB,EACfjP,O,mBASX,SAAMyQ,GAGF,OAFAzQ,KAAKsQ,cAAgBtQ,KAAKsQ,eAAiB,GAC3CtQ,KAAKsQ,cAAc/Q,KAAKkR,GACjBzQ,O,wBASX,SAAWyQ,GAGP,OAFAzQ,KAAKsQ,cAAgBtQ,KAAKsQ,eAAiB,GAC3CtQ,KAAKsQ,cAAcjB,QAAQoB,GACpBzQ,O,oBAQX,SAAOyQ,GACH,IAAKzQ,KAAKsQ,cACN,OAAOtQ,KAEX,GAAIyQ,GAEA,IADA,IAAMzP,EAAYhB,KAAKsQ,cACdxU,EAAI,EAAGA,EAAIkF,EAAUtC,OAAQ5C,IAClC,GAAI2U,IAAazP,EAAUlF,GAEvB,OADAkF,EAAUL,OAAO7E,EAAG,GACbkE,UAKfA,KAAKsQ,cAAgB,GAEzB,OAAOtQ,O,0BAQX,WACI,OAAOA,KAAKsQ,eAAiB,Q,8BA3bhBpC,EAAelD,oBA8bpC1P,EAAQ8O,OAASA,G,6BCldjB5N,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQsE,QAAK,EAObtE,EAAQsE,GANR,SAAYF,EAAK4P,EAAIvP,GAEjB,OADAL,EAAIE,GAAG0P,EAAIvP,GACJ,WACHL,EAAIS,IAAImP,EAAIvP,M,0tDCLpBvD,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ0P,wBAAqB,EAC7B,IAcMA,E,2VAOF,SAAGsE,EAAImB,GAEH,OADA,sCAASnB,EAAImB,GACNzQ,O,kBAQX,SAAKsP,EAAImB,GAEL,OADA,wCAAWnB,EAAImB,GACRzQ,O,kBAQX,SAAKsP,GAAa,6BAANzO,EAAM,iCAANA,EAAM,kBAEd,OADA,oDAAWyO,GAAX,OAAkBzO,IACXb,O,0BAWX,SAAasP,GAAa,6BAANzO,EAAM,iCAANA,EAAM,kBAEtB,OADA,oDAAWyO,GAAX,OAAkBzO,IACXb,O,uBAQX,SAAUF,GACN,oDAAuBA,Q,8BAjEf7B,EAAQ,IAoExB3C,EAAQ0P,mBAAqBA,G,kQCtE7BxO,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQoT,GAAKpT,EAAQ8O,OAAS9O,EAAQqL,QAAUrL,EAAQgD,cAAW,EACnE,IAAMoS,EAAQzS,EAAQ,IAChB0S,EAAY1S,EAAQ,GAM1B1C,EAAOD,QAAUA,EAAUsV,EAI3B,IAAMC,EAASvV,EAAQwV,SAAW,GAClC,SAASF,EAAO9K,EAAKjE,GACE,WAAf,EAAOiE,KACPjE,EAAOiE,EACPA,OAAMd,GAEVnD,EAAOA,GAAQ,GACf,IASI6M,EATEqC,GAAS,EAAIL,EAAMM,KAAKlL,EAAKjE,EAAKuE,MAAQ,cAC1CL,EAASgL,EAAOhL,OAChBZ,EAAK4L,EAAO5L,GACZiB,EAAO2K,EAAO3K,KACd6K,EAAgBJ,EAAM1L,IAAOiB,KAAQyK,EAAM1L,GAAN,KAsB3C,OArBsBtD,EAAKqP,UACvBrP,EAAK,0BACL,IAAUA,EAAKsP,WACfF,EAKAvC,EAAK,IAAIiC,EAAUhK,QAAQZ,EAAQlE,IAG9BgP,EAAM1L,KAGP0L,EAAM1L,GAAM,IAAIwL,EAAUhK,QAAQZ,EAAQlE,IAE9C6M,EAAKmC,EAAM1L,IAEX4L,EAAO5O,QAAUN,EAAKM,QACtBN,EAAKM,MAAQ4O,EAAOxK,UAEjBmI,EAAGrM,OAAO0O,EAAO3K,KAAMvE,GAElCvG,EAAQoT,GAAKkC,EAMb,IAAI3C,EAAqBhQ,EAAQ,GACjCzB,OAAOC,eAAenB,EAAS,WAAY,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOsR,EAAmB3P,YAO5GhD,EAAQ+S,QAAUuC,EAMlB,IAAIQ,EAAYnT,EAAQ,GACxBzB,OAAOC,eAAenB,EAAS,UAAW,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOyU,EAAUzK,WAClG,IAAIG,EAAW7I,EAAQ,IACvBzB,OAAOC,eAAenB,EAAS,SAAU,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOmK,EAASsD,UAChG9O,EAAO,QAAWsV,G,6BCxElBpU,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ0V,SAAM,EACd,IAAMK,EAAWpT,EAAQ,GAiEzB3C,EAAQ0V,IArDR,SAAalL,GAAqB,IAAhBM,EAAgB,uDAAT,GAAIkL,EAAK,uCAC1B5R,EAAMoG,EAEVwL,EAAMA,GAA4B,oBAAb7F,UAA4BA,SAC7C,MAAQ3F,IACRA,EAAMwL,EAAIhT,SAAW,KAAOgT,EAAItL,MAEjB,iBAARF,IACH,MAAQA,EAAIkI,OAAO,KAEflI,EADA,MAAQA,EAAIkI,OAAO,GACbsD,EAAIhT,SAAWwH,EAGfwL,EAAItL,KAAOF,GAGpB,sBAAsByL,KAAKzL,KAIxBA,OADA,IAAuBwL,EACjBA,EAAIhT,SAAW,KAAOwH,EAGtB,WAAaA,GAM3BpG,EAAM2R,EAASvL,IAGdpG,EAAIiM,OACD,cAAc4F,KAAK7R,EAAIpB,UACvBoB,EAAIiM,KAAO,KAEN,eAAe4F,KAAK7R,EAAIpB,YAC7BoB,EAAIiM,KAAO,QAGnBjM,EAAI0G,KAAO1G,EAAI0G,MAAQ,IACvB,IAAMoL,GAAkC,IAA3B9R,EAAIsG,KAAKP,QAAQ,KACxBO,EAAOwL,EAAO,IAAM9R,EAAIsG,KAAO,IAAMtG,EAAIsG,KAS/C,OAPAtG,EAAIyF,GAAKzF,EAAIpB,SAAW,MAAQ0H,EAAO,IAAMtG,EAAIiM,KAAOvF,EAExD1G,EAAI+R,KACA/R,EAAIpB,SACA,MACA0H,GACCsL,GAAOA,EAAI3F,OAASjM,EAAIiM,KAAO,GAAK,IAAMjM,EAAIiM,MAChDjM,I,gBClEX,IAAM0K,EAASnM,EAAQ,IAEvB1C,EAAOD,QAAU,SAACwK,EAAKjE,GAAN,OAAe,IAAIuI,EAAOtE,EAAKjE,IAOhDtG,EAAOD,QAAQ8O,OAASA,EACxB7O,EAAOD,QAAQgD,SAAW8L,EAAO9L,SACjC/C,EAAOD,QAAQ4G,UAAYjE,EAAQ,GACnC1C,EAAOD,QAAQoW,WAAazT,EAAQ,GACpC1C,EAAOD,QAAQ2G,OAAShE,EAAQ,I,wnDCbhC,IAAMyT,EAAazT,EAAQ,GACrBwB,EAAUxB,EAAQ,GAGlBgE,EAAShE,EAAQ,GACjBoT,EAAWpT,EAAQ,GACnBoO,EAAUpO,EAAQ,GAChB2D,EAA0B3D,EAAQ,GAAlC2D,sBAEFwI,E,sQAQJ,WAAYtE,GAAgB,MAAXjE,EAAW,uDAAJ,GAAI,iBAC1B,eAEIiE,GAAO,WAAa,EAAOA,KAC7BjE,EAAOiE,EACPA,EAAM,MAGJA,GACFA,EAAMuL,EAASvL,GACfjE,EAAK+J,SAAW9F,EAAIE,KACpBnE,EAAKgK,OAA0B,UAAjB/F,EAAIxH,UAAyC,QAAjBwH,EAAIxH,SAC9CuD,EAAK8J,KAAO7F,EAAI6F,KACZ7F,EAAI3D,QAAON,EAAKM,MAAQ2D,EAAI3D,QACvBN,EAAKmE,OACdnE,EAAK+J,SAAWyF,EAASxP,EAAKmE,MAAMA,MAGtCpE,EAAsB,EAAD,GAAOC,GAE5B,EAAKgK,OACH,MAAQhK,EAAKgK,OACThK,EAAKgK,OACe,oBAAbJ,UAA4B,WAAaA,SAASnN,SAE3DuD,EAAK+J,WAAa/J,EAAK8J,OAEzB9J,EAAK8J,KAAO,EAAKE,OAAS,MAAQ,MAGpC,EAAKD,SACH/J,EAAK+J,WACgB,oBAAbH,SAA2BA,SAASG,SAAW,aACzD,EAAKD,KACH9J,EAAK8J,OACgB,oBAAbF,UAA4BA,SAASE,KACzCF,SAASE,KACT,EAAKE,OACL,IACA,IAEN,EAAK6F,WAAa7P,EAAK6P,YAAc,CAAC,UAAW,aACjD,EAAKtP,WAAa,GAClB,EAAKuP,YAAc,GACnB,EAAKC,cAAgB,EAErB,EAAK/P,KAAO,EACV,CACEuE,KAAM,aACNyL,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACTvG,OAAO,EACPyB,eAAgB,IAChB+E,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACjBC,UAAW,MAEbC,iBAAkB,GAClBC,qBAAqB,GAEvBxQ,GAGF,EAAKA,KAAKuE,KAAO,EAAKvE,KAAKuE,KAAKR,QAAQ,MAAO,IAAM,IAEtB,iBAApB,EAAK/D,KAAKM,QACnB,EAAKN,KAAKM,MAAQkK,EAAQhJ,OAAO,EAAKxB,KAAKM,QAI7C,EAAKgD,GAAK,KACV,EAAKmN,SAAW,KAChB,EAAKC,aAAe,KACpB,EAAKC,YAAc,KAGnB,EAAKC,iBAAmB,KAEQ,mBAArB5S,mBACL,EAAKgC,KAAKwQ,qBAIZxS,iBACE,gBACA,WACM,EAAK6P,YAEP,EAAKA,UAAUnP,qBACf,EAAKmP,UAAU9F,YAGnB,GAGkB,cAAlB,EAAKgC,WACP,EAAK8G,qBAAuB,WAC1B,EAAK7P,QAAQ,oBAEfhD,iBAAiB,UAAW,EAAK6S,sBAAsB,KAI3D,EAAKrK,OAzGqB,E,4CAmH5B,SAAgBhM,GAGd,IAAM8F,EAkjBV,SAAezC,GACb,IAAMnD,EAAI,GACV,IAAK,IAAIT,KAAK4D,EACRA,EAAI/B,eAAe7B,KACrBS,EAAET,GAAK4D,EAAI5D,IAGf,OAAOS,EAzjBSoW,CAAM3S,KAAK6B,KAAKM,OAG9BA,EAAMyQ,IAAM3Q,EAAO3D,SAGnB6D,EAAMuN,UAAYrT,EAGd2D,KAAKmF,KAAIhD,EAAMgL,IAAMnN,KAAKmF,IAE9B,IAAMtD,EAAO,EACX,GACA7B,KAAK6B,KAAKuQ,iBAAiB/V,GAC3B2D,KAAK6B,KACL,CACEM,QACAE,OAAQrC,KACR4L,SAAU5L,KAAK4L,SACfC,OAAQ7L,KAAK6L,OACbF,KAAM3L,KAAK2L,OAOf,OAAO,IAAI+F,EAAWrV,GAAMwF,K,kBAQ9B,WAAO,IACD6N,EADC,OAEL,GACE1P,KAAK6B,KAAKmQ,iBACV5H,EAAOyI,wBACmC,IAA1C7S,KAAK0R,WAAWjM,QAAQ,aAExBiK,EAAY,gBACP,IAAI,IAAM1P,KAAK0R,WAAWhT,OAK/B,YAHAsB,KAAK+B,cAAa,WAChB,EAAKnB,KAAK,QAAS,6BAClB,GAGH8O,EAAY1P,KAAK0R,WAAW,GAE9B1R,KAAKoC,WAAa,UAGlB,IACEsN,EAAY1P,KAAK8S,gBAAgBpD,GACjC,MAAOhK,GAKP,OAFA1F,KAAK0R,WAAWqB,aAChB/S,KAAKqI,OAIPqH,EAAUrH,OACVrI,KAAKgT,aAAatD,K,0BAQpB,SAAaA,GAAW,WAIlB1P,KAAK0P,WAGP1P,KAAK0P,UAAUnP,qBAIjBP,KAAK0P,UAAYA,EAGjBA,EACG9P,GAAG,QAASI,KAAKiT,QAAQ3V,KAAK0C,OAC9BJ,GAAG,SAAUI,KAAKiD,SAAS3F,KAAK0C,OAChCJ,GAAG,QAASI,KAAKkT,QAAQ5V,KAAK0C,OAC9BJ,GAAG,SAAS,WACX,EAAKiD,QAAQ,wB,mBAUnB,SAAMxG,GAAM,WAGNqT,EAAY1P,KAAK8S,gBAAgBzW,EAAM,CAAE8W,MAAO,IAChDC,GAAS,EAEbhJ,EAAOyI,uBAAwB,EAE/B,IAAMQ,EAAkB,WAClBD,IAIJ1D,EAAU4D,KAAK,CAAC,CAAE9T,KAAM,OAAQwD,KAAM,WACtC0M,EAAUxP,KAAK,UAAU,SAAAoC,GACvB,IAAI8Q,EACJ,GAAI,SAAW9Q,EAAI9C,MAAQ,UAAY8C,EAAIU,KAAM,CAK/C,GAFA,EAAKuQ,WAAY,EACjB,EAAK3S,KAAK,YAAa8O,IAClBA,EAAW,OAChBtF,EAAOyI,sBAAwB,cAAgBnD,EAAUrT,KAIzD,EAAKqT,UAAUhD,OAAM,WACf0G,GACA,WAAa,EAAKhR,aAItBoH,IAEA,EAAKwJ,aAAatD,GAClBA,EAAU4D,KAAK,CAAC,CAAE9T,KAAM,aACxB,EAAKoB,KAAK,UAAW8O,GACrBA,EAAY,KACZ,EAAK6D,WAAY,EACjB,EAAKC,gBAEF,CAGL,IAAMhR,EAAM,IAAIC,MAAM,eACtBD,EAAIkN,UAAYA,EAAUrT,KAC1B,EAAKuE,KAAK,eAAgB4B,SAKhC,SAASiR,IACHL,IAGJA,GAAS,EAET5J,IAEAkG,EAAU9F,QACV8F,EAAY,MAId,IAAMzF,EAAU,SAAAzH,GACd,IAAMkR,EAAQ,IAAIjR,MAAM,gBAAkBD,GAC1CkR,EAAMhE,UAAYA,EAAUrT,KAE5BoX,IAKA,EAAK7S,KAAK,eAAgB8S,IAG5B,SAASC,IACP1J,EAAQ,oBAIV,SAASC,IACPD,EAAQ,iBAIV,SAAS2J,EAAUC,GACbnE,GAAamE,EAAGxX,OAASqT,EAAUrT,MAGrCoX,IAKJ,IAAMjK,EAAU,WACdkG,EAAUpP,eAAe,OAAQ+S,GACjC3D,EAAUpP,eAAe,QAAS2J,GAClCyF,EAAUpP,eAAe,QAASqT,GAClC,EAAKrT,eAAe,QAAS4J,GAC7B,EAAK5J,eAAe,YAAasT,IAGnClE,EAAUxP,KAAK,OAAQmT,GACvB3D,EAAUxP,KAAK,QAAS+J,GACxByF,EAAUxP,KAAK,QAASyT,GAExB3T,KAAKE,KAAK,QAASgK,GACnBlK,KAAKE,KAAK,YAAa0T,GAEvBlE,EAAUrH,S,oBAQZ,WAUE,GAPArI,KAAKoC,WAAa,OAClBgI,EAAOyI,sBAAwB,cAAgB7S,KAAK0P,UAAUrT,KAC9D2D,KAAKY,KAAK,QACVZ,KAAKwT,QAKH,SAAWxT,KAAKoC,YAChBpC,KAAK6B,KAAKkQ,SACV/R,KAAK0P,UAAUhD,MAMf,IAFA,IAAI5Q,EAAI,EACFC,EAAIiE,KAAKsS,SAAS5T,OACjB5C,EAAIC,EAAGD,IACZkE,KAAKmT,MAAMnT,KAAKsS,SAASxW,M,sBAU/B,SAASiD,GACP,GACE,YAAciB,KAAKoC,YACnB,SAAWpC,KAAKoC,YAChB,YAAcpC,KAAKoC,WAUnB,OALApC,KAAKY,KAAK,SAAU7B,GAGpBiB,KAAKY,KAAK,aAEF7B,EAAOS,MACb,IAAK,OACHQ,KAAK8T,YAAYC,KAAKC,MAAMjV,EAAOiE,OACnC,MAEF,IAAK,OACHhD,KAAKiU,mBACLjU,KAAKkU,WAAW,QAChBlU,KAAKY,KAAK,QACVZ,KAAKY,KAAK,QACV,MAEF,IAAK,QACH,IAAM4B,EAAM,IAAIC,MAAM,gBACtBD,EAAI2R,KAAOpV,EAAOiE,KAClBhD,KAAKkT,QAAQ1Q,GACb,MAEF,IAAK,UACHxC,KAAKY,KAAK,OAAQ7B,EAAOiE,MACzBhD,KAAKY,KAAK,UAAW7B,EAAOiE,S,yBAepC,SAAYA,GACVhD,KAAKY,KAAK,YAAaoC,GACvBhD,KAAKmF,GAAKnC,EAAKmK,IACfnN,KAAK0P,UAAUvN,MAAMgL,IAAMnK,EAAKmK,IAChCnN,KAAKsS,SAAWtS,KAAKoU,eAAepR,EAAKsP,UACzCtS,KAAKuS,aAAevP,EAAKuP,aACzBvS,KAAKwS,YAAcxP,EAAKwP,YACxBxS,KAAK6M,SAED,WAAa7M,KAAKoC,YACtBpC,KAAKiU,qB,8BAQP,WAAmB,WACjBjU,KAAKgC,eAAehC,KAAKyS,kBACzBzS,KAAKyS,iBAAmBzS,KAAK+B,cAAa,WACxC,EAAKc,QAAQ,kBACZ7C,KAAKuS,aAAevS,KAAKwS,aACxBxS,KAAK6B,KAAKgI,WACZ7J,KAAKyS,iBAAiB3I,U,qBAS1B,WACE9J,KAAK2R,YAAYhR,OAAO,EAAGX,KAAK4R,eAKhC5R,KAAK4R,cAAgB,EAEjB,IAAM5R,KAAK2R,YAAYjT,OACzBsB,KAAKY,KAAK,SAEVZ,KAAKwT,U,mBAST,WAEI,WAAaxT,KAAKoC,YAClBpC,KAAK0P,UAAU3M,WACd/C,KAAKuT,WACNvT,KAAK2R,YAAYjT,SAIjBsB,KAAK0P,UAAU4D,KAAKtT,KAAK2R,aAGzB3R,KAAK4R,cAAgB5R,KAAK2R,YAAYjT,OACtCsB,KAAKY,KAAK,Y,mBAad,SAAM0B,EAAKkI,EAASzK,GAElB,OADAC,KAAKkU,WAAW,UAAW5R,EAAKkI,EAASzK,GAClCC,O,kBAGT,SAAKsC,EAAKkI,EAASzK,GAEjB,OADAC,KAAKkU,WAAW,UAAW5R,EAAKkI,EAASzK,GAClCC,O,wBAYT,SAAWR,EAAMwD,EAAMwH,EAASzK,GAW9B,GAVI,mBAAsBiD,IACxBjD,EAAKiD,EACLA,OAAOgC,GAGL,mBAAsBwF,IACxBzK,EAAKyK,EACLA,EAAU,MAGR,YAAcxK,KAAKoC,YAAc,WAAapC,KAAKoC,WAAvD,EAIAoI,EAAUA,GAAW,IACb+E,UAAW,IAAU/E,EAAQ+E,SAErC,IAAMxQ,EAAS,CACbS,KAAMA,EACNwD,KAAMA,EACNwH,QAASA,GAEXxK,KAAKY,KAAK,eAAgB7B,GAC1BiB,KAAK2R,YAAYpS,KAAKR,GAClBgB,GAAIC,KAAKE,KAAK,QAASH,GAC3BC,KAAKwT,W,mBAQP,WAAQ,WACA5J,EAAQ,WACZ,EAAK/G,QAAQ,gBAGb,EAAK6M,UAAU9F,SAGXyK,EAAkB,SAAlBA,IACJ,EAAK/T,eAAe,UAAW+T,GAC/B,EAAK/T,eAAe,eAAgB+T,GACpCzK,KAGI0K,EAAiB,WAErB,EAAKpU,KAAK,UAAWmU,GACrB,EAAKnU,KAAK,eAAgBmU,IAqB5B,MAlBI,YAAcrU,KAAKoC,YAAc,SAAWpC,KAAKoC,aACnDpC,KAAKoC,WAAa,UAEdpC,KAAK2R,YAAYjT,OACnBsB,KAAKE,KAAK,SAAS,WACb,EAAKqT,UACPe,IAEA1K,OAGK5J,KAAKuT,UACde,IAEA1K,KAIG5J,O,qBAQT,SAAQwC,GAGN4H,EAAOyI,uBAAwB,EAC/B7S,KAAKY,KAAK,QAAS4B,GACnBxC,KAAK6C,QAAQ,kBAAmBL,K,qBAQlC,SAAQmI,EAAQpI,GAEZ,YAAcvC,KAAKoC,YACnB,SAAWpC,KAAKoC,YAChB,YAAcpC,KAAKoC,aAMnBpC,KAAKgC,eAAehC,KAAKuU,mBACzBvU,KAAKgC,eAAehC,KAAKyS,kBAGzBzS,KAAK0P,UAAUnP,mBAAmB,SAGlCP,KAAK0P,UAAU9F,QAGf5J,KAAK0P,UAAUnP,qBAEoB,mBAAxBC,qBACTA,oBAAoB,UAAWR,KAAK0S,sBAAsB,GAI5D1S,KAAKoC,WAAa,SAGlBpC,KAAKmF,GAAK,KAGVnF,KAAKY,KAAK,QAAS+J,EAAQpI,GAI3BvC,KAAK2R,YAAc,GACnB3R,KAAK4R,cAAgB,K,4BAWzB,SAAeU,GAIb,IAHA,IAAMkC,EAAmB,GACrB1Y,EAAI,EACF2Y,EAAInC,EAAS5T,OACZ5C,EAAI2Y,EAAG3Y,KACPkE,KAAK0R,WAAWjM,QAAQ6M,EAASxW,KACpC0Y,EAAiBjV,KAAK+S,EAASxW,IAEnC,OAAO0Y,O,8BAlqBU/U,GAsqBrB2K,EAAOyI,uBAAwB,EAQ/BzI,EAAO9L,SAAW2D,EAAO3D,SAYzB/C,EAAOD,QAAU8O,G,cC1rBjB,IACE7O,EAAOD,QAAoC,oBAAnB2P,gBACtB,oBAAqB,IAAIA,eAC3B,MAAOzI,GAGPjH,EAAOD,SAAU,I,q5DCbnB,IAAM2P,EAAiBhN,EAAQ,IACzBsO,EAAUtO,EAAQ,IAClBwB,EAAUxB,EAAQ,GACxB,EAAwCA,EAAQ,GAAxCkD,EAAR,EAAQA,KAAMS,EAAd,EAAcA,sBACRV,EAAajD,EAAQ,GAS3B,SAASyW,KAET,IAAMC,EAEG,MADK,IAAI1J,EAAe,CAAEa,SAAS,IACvB8I,aAGf1J,E,8BAOJ,WAAYrJ,GAAM,MAGhB,GAHgB,UAChB,cAAMA,GAEkB,oBAAb4J,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASnN,SAChCqN,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvB,EAAKJ,GACkB,oBAAbG,UACN5J,EAAK+J,WAAaH,SAASG,UAC7BD,IAAS9J,EAAK8J,KAChB,EAAKJ,GAAK1J,EAAKgK,SAAWH,EAK5B,IAAMmJ,EAAchT,GAAQA,EAAKgT,YArBjB,OAsBhB,EAAK3H,eAAiByH,IAAYE,EAtBlB,E,iCA+BlB,WAAmB,IAAXhT,EAAW,uDAAJ,GAEb,OADA,EAAcA,EAAM,CAAEyJ,GAAItL,KAAKsL,GAAIC,GAAIvL,KAAKuL,IAAMvL,KAAK6B,MAChD,IAAIiT,EAAQ9U,KAAK8F,MAAOjE,K,qBAUjC,SAAQmB,EAAMjD,GAAI,WACVgV,EAAM/U,KAAKgV,QAAQ,CACvBC,OAAQ,OACRjS,KAAMA,IAER+R,EAAInV,GAAG,UAAWG,GAClBgV,EAAInV,GAAG,SAAS,SAAA4C,GACd,EAAK0Q,QAAQ,iBAAkB1Q,Q,oBASnC,WAAS,WAGDuS,EAAM/U,KAAKgV,UACjBD,EAAInV,GAAG,OAAQI,KAAKkV,OAAO5X,KAAK0C,OAChC+U,EAAInV,GAAG,SAAS,SAAA4C,GACd,EAAK0Q,QAAQ,iBAAkB1Q,MAEjCxC,KAAKmV,QAAUJ,M,GA1EDxI,GA8EZuI,E,8BAOJ,WAAYhP,EAAKjE,GAAM,uBACrB,eACAD,EAAsB,EAAD,GAAOC,GAC5B,EAAKA,KAAOA,EAEZ,EAAKoT,OAASpT,EAAKoT,QAAU,MAC7B,EAAKnP,IAAMA,EACX,EAAKsP,OAAQ,IAAUvT,EAAKuT,MAC5B,EAAKpS,UAAOgC,IAAcnD,EAAKmB,KAAOnB,EAAKmB,KAAO,KAElD,EAAK5F,SAVgB,E,gCAkBvB,WAAS,WACDyE,EAAOV,EACXnB,KAAK6B,KACL,QACA,aACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,aAEFA,EAAKiK,UAAY9L,KAAK6B,KAAKyJ,GAC3BzJ,EAAKkK,UAAY/L,KAAK6B,KAAK0J,GAE3B,IAAM8J,EAAOrV,KAAKqV,IAAM,IAAIpK,EAAepJ,GAE3C,IAGEwT,EAAIhN,KAAKrI,KAAKiV,OAAQjV,KAAK8F,IAAK9F,KAAKoV,OACrC,IACE,GAAIpV,KAAK6B,KAAKyT,aAEZ,IAAK,IAAIxZ,KADTuZ,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACzCvV,KAAK6B,KAAKyT,aAClBtV,KAAK6B,KAAKyT,aAAa3X,eAAe7B,IACxCuZ,EAAIG,iBAAiB1Z,EAAGkE,KAAK6B,KAAKyT,aAAaxZ,IAIrD,MAAO4J,IAET,GAAI,SAAW1F,KAAKiV,OAClB,IACEI,EAAIG,iBAAiB,eAAgB,4BACrC,MAAO9P,IAGX,IACE2P,EAAIG,iBAAiB,SAAU,OAC/B,MAAO9P,IAGL,oBAAqB2P,IACvBA,EAAIvD,gBAAkB9R,KAAK6B,KAAKiQ,iBAG9B9R,KAAK6B,KAAK4T,iBACZJ,EAAIvN,QAAU9H,KAAK6B,KAAK4T,gBAGtBzV,KAAK0V,UACPL,EAAIM,OAAS,WACX,EAAKC,UAEPP,EAAIpL,QAAU,WACZ,EAAKiJ,QAAQmC,EAAIQ,gBAGnBR,EAAIS,mBAAqB,WACnB,IAAMT,EAAIjT,aACV,MAAQiT,EAAIU,QAAU,OAASV,EAAIU,OACrC,EAAKH,SAIL,EAAK7T,cAAa,WAChB,EAAKmR,QAA8B,iBAAfmC,EAAIU,OAAsBV,EAAIU,OAAS,KAC1D,KAOTV,EAAI/B,KAAKtT,KAAKgD,MACd,MAAO0C,GAOP,YAHA1F,KAAK+B,cAAa,WAChB,EAAKmR,QAAQxN,KACZ,GAImB,oBAAbsQ,WACThW,KAAKiW,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAASnW,KAAKiW,OAASjW,Q,uBASnC,WACEA,KAAKY,KAAK,WACVZ,KAAKwJ,Y,oBAQP,SAAOxG,GACLhD,KAAKY,KAAK,OAAQoC,GAClBhD,KAAKoW,c,qBAQP,SAAQ5T,GACNxC,KAAKY,KAAK,QAAS4B,GACnBxC,KAAKwJ,SAAQ,K,qBAQf,SAAQ6M,GACN,QAAI,IAAuBrW,KAAKqV,KAAO,OAASrV,KAAKqV,IAArD,CAUA,GANIrV,KAAK0V,SACP1V,KAAKqV,IAAIM,OAAS3V,KAAKqV,IAAIpL,QAAUyK,EAErC1U,KAAKqV,IAAIS,mBAAqBpB,EAG5B2B,EACF,IACErW,KAAKqV,IAAIiB,QACT,MAAO5Q,IAGa,oBAAbsQ,iBACFlB,EAAQqB,SAASnW,KAAKiW,OAG/BjW,KAAKqV,IAAM,Q,oBAQb,WACE,IAAMrS,EAAOhD,KAAKqV,IAAIQ,aACT,OAAT7S,GACFhD,KAAKkV,OAAOlS,K,oBAShB,WACE,MAAiC,oBAAnBmJ,iBAAmCnM,KAAKuL,IAAMvL,KAAKkM,a,mBAQnE,WACElM,KAAKwJ,c,GA7Ma/J,GA0NtB,GAHAqV,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,GAEK,oBAAbH,SACT,GAA2B,mBAAhBO,YACTA,YAAY,WAAYC,QACnB,GAAgC,mBAArB3W,iBAAiC,CAEjDA,iBADyB,eAAgBqB,EAAa,WAAa,SAChCsV,GAAe,GAItD,SAASA,IACP,IAAK,IAAI1a,KAAKgZ,EAAQqB,SAChBrB,EAAQqB,SAASxY,eAAe7B,IAClCgZ,EAAQqB,SAASra,GAAGwa,QAK1B/a,EAAOD,QAAU4P,EACjB3P,EAAOD,QAAQwZ,QAAUA,G,gBChVzB,IAAQzH,EAAiBpP,EAAQ,IAAzBoP,aAEFoJ,EACY,mBAATC,MACU,oBAATA,MACmC,6BAAzCla,OAAOkB,UAAU+G,SAASxI,KAAKya,MAC7BC,EAA+C,mBAAhBC,YA8B/BC,EAAqB,SAAC7T,EAAMvE,GAChC,IAAMqY,EAAa,IAAIC,WAKvB,OAJAD,EAAWnB,OAAS,WAClB,IAAMqB,EAAUF,EAAWG,OAAO5X,MAAM,KAAK,GAC7CZ,EAAS,IAAMuY,IAEVF,EAAWI,cAAclU,IAGlCzH,EAAOD,QA9Bc,SAAC,EAAgB4R,EAAgBzO,GAAa,IANpDiB,EAMSF,EAA2C,EAA3CA,KAAMwD,EAAqC,EAArCA,KAC5B,OAAIyT,GAAkBzT,aAAgB0T,KAChCxJ,EACKzO,EAASuE,GAET6T,EAAmB7T,EAAMvE,GAGlCkY,IACC3T,aAAgB4T,cAfNlX,EAe4BsD,EAdJ,mBAAvB4T,YAAYO,OACtBP,YAAYO,OAAOzX,GACnBA,GAAOA,EAAI0X,kBAAkBR,cAc3B1J,EACKzO,EAASuE,aAAgB4T,YAAc5T,EAAOA,EAAKoU,QAEnDP,EAAmB,IAAIH,KAAK,CAAC1T,IAAQvE,GAIzCA,EAAS4O,EAAa7N,IAASwD,GAAQ,O,gBCjChD,IAIIqU,EAJJ,EAA+CpZ,EAAQ,IAA/CqP,EAAR,EAAQA,qBAAsBC,EAA9B,EAA8BA,aAEuB,mBAAhBqJ,cAInCS,EAAgBpZ,EAAQ,KAG1B,IA4BMqZ,EAAqB,SAACtU,EAAM5D,GAChC,GAAIiY,EAAe,CACjB,IAAMxS,EAAUwS,EAAchU,OAAOL,GACrC,OAAOuU,EAAU1S,EAASzF,GAE1B,MAAO,CAAEoY,QAAQ,EAAMxU,SAIrBuU,EAAY,SAACvU,EAAM5D,GACvB,OAAQA,GACN,IAAK,OACH,OAAO4D,aAAgB4T,YAAc,IAAIF,KAAK,CAAC1T,IAASA,EAC1D,IAAK,cACL,QACE,OAAOA,IAIbzH,EAAOD,QA/Cc,SAAC0D,EAAeI,GACnC,GAA6B,iBAAlBJ,EACT,MAAO,CACLQ,KAAM,UACNwD,KAAMuU,EAAUvY,EAAeI,IAGnC,IAAMI,EAAOR,EAAcgP,OAAO,GAClC,MAAa,MAATxO,EACK,CACLA,KAAM,UACNwD,KAAMsU,EAAmBtY,EAAc2G,UAAU,GAAIvG,IAGtCkO,EAAqB9N,GAIjCR,EAAcN,OAAS,EAC1B,CACEc,KAAM8N,EAAqB9N,GAC3BwD,KAAMhE,EAAc2G,UAAU,IAEhC,CACEnG,KAAM8N,EAAqB9N,IARxB+N,I,eClBX,SAAUkK,GACR,aAEAnc,EAAQ4H,OAAS,SAASwU,GACxB,IACA5b,EADI6b,EAAQ,IAAIC,WAAWF,GACxB5W,EAAM6W,EAAMjZ,OAAQ8Y,EAAS,GAEhC,IAAK1b,EAAI,EAAGA,EAAIgF,EAAKhF,GAAG,EACtB0b,GAAUC,EAAME,EAAM7b,IAAM,GAC5B0b,GAAUC,GAAmB,EAAXE,EAAM7b,KAAW,EAAM6b,EAAM7b,EAAI,IAAM,GACzD0b,GAAUC,GAAuB,GAAfE,EAAM7b,EAAI,KAAY,EAAM6b,EAAM7b,EAAI,IAAM,GAC9D0b,GAAUC,EAAqB,GAAfE,EAAM7b,EAAI,IAS5B,OANKgF,EAAM,GAAO,EAChB0W,EAASA,EAAO7R,UAAU,EAAG6R,EAAO9Y,OAAS,GAAK,IACzCoC,EAAM,GAAM,IACrB0W,EAASA,EAAO7R,UAAU,EAAG6R,EAAO9Y,OAAS,GAAK,MAG7C8Y,GAGTlc,EAAQ+H,OAAU,SAASmU,GACzB,IACqB1b,EACrB+b,EAAUC,EAAUC,EAAUC,EAF1BC,EAA+B,IAAhBT,EAAO9Y,OAC1BoC,EAAM0W,EAAO9Y,OAAWd,EAAI,EAGM,MAA9B4Z,EAAOA,EAAO9Y,OAAS,KACzBuZ,IACkC,MAA9BT,EAAOA,EAAO9Y,OAAS,IACzBuZ,KAIJ,IAAIP,EAAc,IAAId,YAAYqB,GAClCN,EAAQ,IAAIC,WAAWF,GAEvB,IAAK5b,EAAI,EAAGA,EAAIgF,EAAKhF,GAAG,EACtB+b,EAAWJ,EAAMhS,QAAQ+R,EAAO1b,IAChCgc,EAAWL,EAAMhS,QAAQ+R,EAAO1b,EAAE,IAClCic,EAAWN,EAAMhS,QAAQ+R,EAAO1b,EAAE,IAClCkc,EAAWP,EAAMhS,QAAQ+R,EAAO1b,EAAE,IAElC6b,EAAM/Z,KAAQia,GAAY,EAAMC,GAAY,EAC5CH,EAAM/Z,MAAoB,GAAXka,IAAkB,EAAMC,GAAY,EACnDJ,EAAM/Z,MAAoB,EAAXma,IAAiB,EAAiB,GAAXC,EAGxC,OAAON,GAjDX,CAmDG,qE,knDC1DH,IAUIhX,EAVE6L,EAAUtO,EAAQ,IAClBiD,EAAajD,EAAQ,GAErBia,EAAW,MACXC,EAAkB,OAQlBC,E,sQAOJ,WAAYvW,GAAM,a,4FAAA,UAChB,cAAMA,IAEDM,MAAQ,EAAKA,OAAS,GAItBzB,IAEHA,EAAYQ,EAAWmX,OAASnX,EAAWmX,QAAU,IAIvD,EAAKpC,MAAQvV,EAAUhC,OAGvBgC,EAAUnB,KAAK,EAAK2V,OAAO5X,KAAZ,OAGf,EAAK6E,MAAMsS,EAAI,EAAKwB,MAnBJ,E,yCAyBlB,WACE,OAAO,I,qBAQT,WACMjW,KAAKsY,SAEPtY,KAAKsY,OAAOrO,QAAU,aACtBjK,KAAKsY,OAAOC,WAAWC,YAAYxY,KAAKsY,QACxCtY,KAAKsY,OAAS,MAGZtY,KAAKyY,OACPzY,KAAKyY,KAAKF,WAAWC,YAAYxY,KAAKyY,MACtCzY,KAAKyY,KAAO,KACZzY,KAAK0Y,OAAS,MAGhB,8C,oBAQF,WAAS,WACDJ,EAAStC,SAAS2C,cAAc,UAElC3Y,KAAKsY,SACPtY,KAAKsY,OAAOC,WAAWC,YAAYxY,KAAKsY,QACxCtY,KAAKsY,OAAS,MAGhBA,EAAOlD,OAAQ,EACfkD,EAAO/S,IAAMvF,KAAK8F,MAClBwS,EAAOrO,QAAU,SAAAvE,GACf,EAAKwN,QAAQ,mBAAoBxN,IAGnC,IAAMkT,EAAW5C,SAAS6C,qBAAqB,UAAU,GACrDD,EACFA,EAASL,WAAWO,aAAaR,EAAQM,IAExC5C,SAAS+C,MAAQ/C,SAASgD,MAAMC,YAAYX,GAE/CtY,KAAKsY,OAASA,EAGZ,oBAAuBY,WAAa,SAAS3H,KAAK2H,UAAUC,YAG5DnZ,KAAK+B,cAAa,WAChB,IAAM2W,EAAS1C,SAAS2C,cAAc,UACtC3C,SAASgD,KAAKC,YAAYP,GAC1B1C,SAASgD,KAAKR,YAAYE,KACzB,O,qBAWP,SAAQ1V,EAAMjD,GAAI,IACZ2Y,EADY,OAGhB,IAAK1Y,KAAKyY,KAAM,CACd,IAAMA,EAAOzC,SAAS2C,cAAc,QAC9BS,EAAOpD,SAAS2C,cAAc,YAC9BxT,EAAMnF,KAAKqZ,SAAW,cAAgBrZ,KAAKiW,MAEjDwC,EAAKa,UAAY,WACjBb,EAAKc,MAAMC,SAAW,WACtBf,EAAKc,MAAME,IAAM,UACjBhB,EAAKc,MAAMG,KAAO,UAClBjB,EAAKkB,OAASxU,EACdsT,EAAKxD,OAAS,OACdwD,EAAKmB,aAAa,iBAAkB,SACpCR,EAAK/c,KAAO,IACZoc,EAAKQ,YAAYG,GACjBpD,SAASgD,KAAKC,YAAYR,GAE1BzY,KAAKyY,KAAOA,EACZzY,KAAKoZ,KAAOA,EAKd,SAASS,IACPC,IACA/Z,IAJFC,KAAKyY,KAAKsB,OAAS/Z,KAAK8F,MAOxB,IAAMgU,EAAa,WACjB,GAAI,EAAKpB,OACP,IACE,EAAKD,KAAKD,YAAY,EAAKE,QAC3B,MAAOhT,GACP,EAAKwN,QAAQ,qCAAsCxN,GAIvD,IAEE,IAAMsU,EAAO,oCAAsC,EAAKX,SAAW,KACnEX,EAAS1C,SAAS2C,cAAcqB,GAChC,MAAOtU,IACPgT,EAAS1C,SAAS2C,cAAc,WACzBtc,KAAO,EAAKgd,SACnBX,EAAOnT,IAAM,eAGfmT,EAAOvT,GAAK,EAAKkU,SAEjB,EAAKZ,KAAKQ,YAAYP,GACtB,EAAKA,OAASA,GAGhBoB,IAIA9W,EAAOA,EAAK4C,QAAQuS,EAAiB,QACrCnY,KAAKoZ,KAAKrc,MAAQiG,EAAK4C,QAAQsS,EAAU,OAEzC,IACElY,KAAKyY,KAAKwB,SACV,MAAOvU,IAEL1F,KAAK0Y,OAAOnC,YACdvW,KAAK0Y,OAAO5C,mBAAqB,WACA,aAA3B,EAAK4C,OAAOtW,YACdyX,KAIJ7Z,KAAK0Y,OAAO/C,OAASkE,O,8BAjLAtN,GAsL3BhR,EAAOD,QAAU8c,G,w0CClMjB,IAAMlW,EAAYjE,EAAQ,GACpBgE,EAAShE,EAAQ,GACjBoO,EAAUpO,EAAQ,GAClBqO,EAAQrO,EAAQ,IACdkD,EAASlD,EAAQ,GAAjBkD,KACR,EAKIlD,EAAQ,IAJVic,EADF,EACEA,UACAC,EAFF,EAEEA,sBACAC,EAHF,EAGEA,kBACAC,EAJF,EAIEA,SAOIC,EACiB,oBAAdpB,WACsB,iBAAtBA,UAAUqB,SACmB,gBAApCrB,UAAUqB,QAAQC,cAEdC,E,sQAOJ,WAAY5Y,GAAM,a,4FAAA,UAChB,cAAMA,IAEDqL,gBAAkBrL,EAAKgT,YAHZ,E,+BAWlB,WACE,MAAO,c,oBAQT,WACE,GAAK7U,KAAK0a,QAAV,CAKA,IAAM5U,EAAM9F,KAAK8F,MACX6U,EAAY3a,KAAK6B,KAAK8Y,UAGtB9Y,EAAOyY,EACT,GACAnZ,EACEnB,KAAK6B,KACL,QACA,oBACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,eACA,kBACA,SACA,aACA,SACA,uBAGF7B,KAAK6B,KAAKyT,eACZzT,EAAK+Y,QAAU5a,KAAK6B,KAAKyT,cAG3B,IACEtV,KAAK6a,GACHV,IAA0BG,EACtBK,EACE,IAAIT,EAAUpU,EAAK6U,GACnB,IAAIT,EAAUpU,GAChB,IAAIoU,EAAUpU,EAAK6U,EAAW9Y,GACpC,MAAOW,GACP,OAAOxC,KAAKY,KAAK,QAAS4B,GAG5BxC,KAAK6a,GAAGzb,WAAaY,KAAKqC,OAAOjD,YAAcgb,EAE/Cpa,KAAK8a,uB,+BAQP,WAAoB,WAClB9a,KAAK6a,GAAGvR,OAAS,WACX,EAAKzH,KAAKgI,WACZ,EAAKgR,GAAGE,QAAQjR,QAElB,EAAK+C,UAEP7M,KAAK6a,GAAG3Q,QAAUlK,KAAK6C,QAAQvF,KAAK0C,MACpCA,KAAK6a,GAAGG,UAAY,SAAA1L,GAAE,OAAI,EAAK4F,OAAO5F,EAAGtM,OACzChD,KAAK6a,GAAG5Q,QAAU,SAAAvE,GAAC,OAAI,EAAKwN,QAAQ,kBAAmBxN,M,mBASzD,SAAMlH,GAAS,WACbwB,KAAK+C,UAAW,EAIhB,IALa,eAKJjH,GACP,IAAMiD,EAASP,EAAQ1C,GACjBmf,EAAanf,IAAM0C,EAAQE,OAAS,EAE1CuD,EAAOjE,aAAae,EAAQ,EAAKmO,gBAAgB,SAAAlK,GAE/C,IAAMnB,EAAO,GACRsY,IACCpb,EAAOyL,UACT3I,EAAK0N,SAAWxQ,EAAOyL,QAAQ+E,UAG7B,EAAK1N,KAAKqQ,oBAEV,iBAAoBlP,EAAOkY,OAAOC,WAAWnY,GAAQA,EAAKtE,QAClD,EAAKmD,KAAKqQ,kBAAkBC,YACpCtQ,EAAK0N,UAAW,IAQtB,IACM4K,EAEF,EAAKU,GAAGvH,KAAKtQ,GAEb,EAAK6X,GAAGvH,KAAKtQ,EAAMnB,GAErB,MAAO6D,IAKLuV,GAGFZ,GAAS,WACP,EAAKtX,UAAW,EAChB,EAAKnC,KAAK,WACT,EAAKmB,kBA1CLjG,EAAI,EAAGA,EAAI0C,EAAQE,OAAQ5C,IAAK,EAAhCA,K,qBAqDX,WACEoG,EAAUxE,UAAUmF,QAAQ5G,KAAK+D,Q,qBAQnC,gBACyB,IAAZA,KAAK6a,KACd7a,KAAK6a,GAAGjR,QACR5J,KAAK6a,GAAK,Q,iBASd,WACE,IAAI1Y,EAAQnC,KAAKmC,OAAS,GACpB4K,EAAS/M,KAAK6B,KAAKgK,OAAS,MAAQ,KACtCF,EAAO,GA6BX,OAzBE3L,KAAK6B,KAAK8J,OACR,QAAUoB,GAAqC,MAA3B5I,OAAOnE,KAAK6B,KAAK8J,OACpC,OAASoB,GAAqC,KAA3B5I,OAAOnE,KAAK6B,KAAK8J,SAEvCA,EAAO,IAAM3L,KAAK6B,KAAK8J,MAIrB3L,KAAK6B,KAAKmL,oBACZ7K,EAAMnC,KAAK6B,KAAKoL,gBAAkBX,KAI/BtM,KAAKkN,iBACR/K,EAAMiL,IAAM,IAGdjL,EAAQkK,EAAQnJ,OAAOf,IAGbzD,SACRyD,EAAQ,IAAMA,GAKd4K,EACA,QAHgD,IAArC/M,KAAK6B,KAAK+J,SAASnG,QAAQ,KAI9B,IAAMzF,KAAK6B,KAAK+J,SAAW,IAAM5L,KAAK6B,KAAK+J,UACnDD,EACA3L,KAAK6B,KAAKuE,KACVjE,I,mBAUJ,WACE,SACI+X,GACA,iBAAkBA,GAAala,KAAK3D,OAASoe,EAAG/c,UAAUrB,W,8BAvOjD6F,GA4OjB3G,EAAOD,QAAUmf,G,gBCjQjB,IAAMvZ,EAAajD,EAAQ,GACrBoc,EAEiB,mBAAZe,SAAqD,mBAApBA,QAAQC,QAEzC,SAAA5a,GAAE,OAAI2a,QAAQC,UAAUC,KAAK7a,IAE7B,SAACA,EAAIsB,GAAL,OAAsBA,EAAatB,EAAI,IAIlDlF,EAAOD,QAAU,CACf4e,UAAWhZ,EAAWgZ,WAAahZ,EAAWqa,aAC9CpB,uBAAuB,EACvBC,kBAAmB,cACnBC,a,gBCfF/e,EAAQ4H,OAASjF,EAAQ,IACzB3C,EAAQ+H,OAASpF,EAAQ,K,kQCCzB,SAASud,EAAUC,EAAMC,EAAQvY,GAE/B,IADA,IAAIhH,EAAI,EACCL,EAAI,EAAGC,EAAIoH,EAAIzE,OAAQ5C,EAAIC,EAAGD,KACrCK,EAAIgH,EAAIwY,WAAW7f,IACX,IACN2f,EAAKG,SAASF,IAAUvf,GAEjBA,EAAI,MACXsf,EAAKG,SAASF,IAAU,IAAQvf,GAAK,GACrCsf,EAAKG,SAASF,IAAU,IAAY,GAAJvf,IAEzBA,EAAI,OAAUA,GAAK,OAC1Bsf,EAAKG,SAASF,IAAU,IAAQvf,GAAK,IACrCsf,EAAKG,SAASF,IAAU,IAAQvf,GAAK,EAAK,IAC1Csf,EAAKG,SAASF,IAAU,IAAY,GAAJvf,KAGhCL,IACAK,EAAI,QAAiB,KAAJA,IAAc,GAA2B,KAApBgH,EAAIwY,WAAW7f,IACrD2f,EAAKG,SAASF,IAAU,IAAQvf,GAAK,IACrCsf,EAAKG,SAASF,IAAU,IAAQvf,GAAK,GAAM,IAC3Csf,EAAKG,SAASF,IAAU,IAAQvf,GAAK,EAAK,IAC1Csf,EAAKG,SAASF,IAAU,IAAY,GAAJvf,IAwRtCZ,EAAOD,QAxCP,SAAgByB,GACd,IAAI4a,EAAQ,GACRkE,EAAS,GACTC,EAzNN,SAASC,EAAQpE,EAAOkE,EAAQ9e,GAC9B,IAAIyC,EAAO,EAAOzC,GAAOjB,EAAI,EAAGC,EAAI,EAAGigB,EAAK,EAAGC,EAAK,EAAGvd,EAAS,EAAGod,EAAO,EAE1E,GAAa,WAATtc,EAAmB,CAIrB,IAHAd,EAzBJ,SAAoByE,GAElB,IADA,IAAIhH,EAAI,EAAGuC,EAAS,EACX5C,EAAI,EAAGC,EAAIoH,EAAIzE,OAAQ5C,EAAIC,EAAGD,KACrCK,EAAIgH,EAAIwY,WAAW7f,IACX,IACN4C,GAAU,EAEHvC,EAAI,KACXuC,GAAU,EAEHvC,EAAI,OAAUA,GAAK,MAC1BuC,GAAU,GAGV5C,IACA4C,GAAU,GAGd,OAAOA,EAOIwd,CAAWnf,IAGP,GACX4a,EAAMpY,KAAc,IAATb,GACXod,EAAO,OAGJ,GAAIpd,EAAS,IAChBiZ,EAAMpY,KAAK,IAAMb,GACjBod,EAAO,OAGJ,GAAIpd,EAAS,MAChBiZ,EAAMpY,KAAK,IAAMb,GAAU,EAAGA,GAC9Bod,EAAO,MAGJ,MAAIpd,EAAS,YAIhB,MAAM,IAAI+D,MAAM,mBAHhBkV,EAAMpY,KAAK,IAAMb,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1Dod,EAAO,EAKT,OADAD,EAAOtc,KAAK,CAAE4c,KAAMpf,EAAOqf,QAAS1d,EAAQ2d,QAAS1E,EAAMjZ,SACpDod,EAAOpd,EAEhB,GAAa,WAATc,EAIF,OAAI6E,KAAKC,MAAMvH,KAAWA,GAAUqH,SAASrH,GAMzCA,GAAS,EAEPA,EAAQ,KACV4a,EAAMpY,KAAKxC,GACJ,GAGLA,EAAQ,KACV4a,EAAMpY,KAAK,IAAMxC,GACV,GAGLA,EAAQ,OACV4a,EAAMpY,KAAK,IAAMxC,GAAS,EAAGA,GACtB,GAGLA,EAAQ,YACV4a,EAAMpY,KAAK,IAAMxC,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGTif,EAAMjf,EAAQsH,KAAKiY,IAAI,EAAG,KAAQ,EAClCL,EAAKlf,IAAU,EACf4a,EAAMpY,KAAK,IAAMyc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,GAGHlf,IAAU,IACZ4a,EAAMpY,KAAKxC,GACJ,GAGLA,IAAU,KACZ4a,EAAMpY,KAAK,IAAMxC,GACV,GAGLA,IAAU,OACZ4a,EAAMpY,KAAK,IAAMxC,GAAS,EAAGA,GACtB,GAGLA,IAAU,YACZ4a,EAAMpY,KAAK,IAAMxC,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGTif,EAAK3X,KAAKC,MAAMvH,EAAQsH,KAAKiY,IAAI,EAAG,KACpCL,EAAKlf,IAAU,EACf4a,EAAMpY,KAAK,IAAMyc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,IAxDPtE,EAAMpY,KAAK,KACXsc,EAAOtc,KAAK,CAAEgd,OAAQxf,EAAOqf,QAAS,EAAGC,QAAS1E,EAAMjZ,SACjD,GAyDX,GAAa,WAATc,EAAmB,CAErB,GAAc,OAAVzC,EAEF,OADA4a,EAAMpY,KAAK,KACJ,EAGT,GAAIX,MAAMqG,QAAQlI,GAAQ,CAIxB,IAHA2B,EAAS3B,EAAM2B,QAGF,GACXiZ,EAAMpY,KAAc,IAATb,GACXod,EAAO,OAGJ,GAAIpd,EAAS,MAChBiZ,EAAMpY,KAAK,IAAMb,GAAU,EAAGA,GAC9Bod,EAAO,MAGJ,MAAIpd,EAAS,YAIhB,MAAM,IAAI+D,MAAM,mBAHhBkV,EAAMpY,KAAK,IAAMb,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1Dod,EAAO,EAIT,IAAKhgB,EAAI,EAAGA,EAAI4C,EAAQ5C,IACtBggB,GAAQC,EAAQpE,EAAOkE,EAAQ9e,EAAMjB,IAEvC,OAAOggB,EAIT,GAAI/e,aAAiBgR,KAAM,CACzB,IAAIyO,EAAOzf,EAAM0f,UAIjB,OAHAT,EAAK3X,KAAKC,MAAMkY,EAAOnY,KAAKiY,IAAI,EAAG,KACnCL,EAAKO,IAAS,EACd7E,EAAMpY,KAAK,IAAM,EAAGyc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GAC3E,GAGT,GAAIlf,aAAiB6Z,YAAa,CAIhC,IAHAlY,EAAS3B,EAAMoe,YAGF,IACXxD,EAAMpY,KAAK,IAAMb,GACjBod,EAAO,OAGT,GAAIpd,EAAS,MACXiZ,EAAMpY,KAAK,IAAMb,GAAU,EAAGA,GAC9Bod,EAAO,MAGT,MAAIpd,EAAS,YAIX,MAAM,IAAI+D,MAAM,oBAHhBkV,EAAMpY,KAAK,IAAMb,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1Dod,EAAO,EAKT,OADAD,EAAOtc,KAAK,CAAEmd,KAAM3f,EAAOqf,QAAS1d,EAAQ2d,QAAS1E,EAAMjZ,SACpDod,EAAOpd,EAGhB,GAA4B,mBAAjB3B,EAAM4f,OACf,OAAOZ,EAAQpE,EAAOkE,EAAQ9e,EAAM4f,UAGtC,IAAItS,EAAO,GAAIhN,EAAM,GAEjBuf,EAAUpgB,OAAO6N,KAAKtN,GAC1B,IAAKjB,EAAI,EAAGC,EAAI6gB,EAAQle,OAAQ5C,EAAIC,EAAGD,IAEX,mBAAfiB,EADXM,EAAMuf,EAAQ9gB,KAEZuO,EAAK9K,KAAKlC,GAMd,IAHAqB,EAAS2L,EAAK3L,QAGD,GACXiZ,EAAMpY,KAAc,IAATb,GACXod,EAAO,OAGJ,GAAIpd,EAAS,MAChBiZ,EAAMpY,KAAK,IAAMb,GAAU,EAAGA,GAC9Bod,EAAO,MAGJ,MAAIpd,EAAS,YAIhB,MAAM,IAAI+D,MAAM,oBAHhBkV,EAAMpY,KAAK,IAAMb,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1Dod,EAAO,EAKT,IAAKhgB,EAAI,EAAGA,EAAI4C,EAAQ5C,IAEtBggB,GAAQC,EAAQpE,EAAOkE,EADvBxe,EAAMgN,EAAKvO,IAEXggB,GAAQC,EAAQpE,EAAOkE,EAAQ9e,EAAMM,IAEvC,OAAOye,EAGT,GAAa,YAATtc,EAEF,OADAmY,EAAMpY,KAAKxC,EAAQ,IAAO,KACnB,EAGT,GAAa,cAATyC,EAEF,OADAmY,EAAMpY,KAAK,IAAM,EAAG,GACb,EAET,MAAM,IAAIkD,MAAM,oBAMLsZ,CAAQpE,EAAOkE,EAAQ9e,GAC9B8f,EAAM,IAAIjG,YAAYkF,GACtBL,EAAO,IAAIqB,SAASD,GAEpBE,EAAa,EACbC,EAAe,EACfC,GAAc,EACdpB,EAAOnd,OAAS,IAClBue,EAAapB,EAAO,GAAGQ,SAIzB,IADA,IAAIa,EAAOC,EAAc,EAAGzB,EAAS,EAC5B5f,EAAI,EAAGC,EAAI4b,EAAMjZ,OAAQ5C,EAAIC,EAAGD,IAEvC,GADA2f,EAAKG,SAASoB,EAAelhB,EAAG6b,EAAM7b,IAClCA,EAAI,IAAMmhB,EAAd,CAIA,GAFAE,GADAD,EAAQrB,EAAOkB,IACKX,QACpBV,EAASsB,EAAeC,EACpBC,EAAMR,KAER,IADA,IAAIU,EAAM,IAAIxF,WAAWsF,EAAMR,MACtBjI,EAAI,EAAGA,EAAI0I,EAAa1I,IAC/BgH,EAAKG,SAASF,EAASjH,EAAG2I,EAAI3I,SAEvByI,EAAMf,KACfX,EAAUC,EAAMC,EAAQwB,EAAMf,WACJnX,IAAjBkY,EAAMX,QACfd,EAAK4B,WAAW3B,EAAQwB,EAAMX,QAGhCS,GAAgBG,EACZtB,IAFJkB,KAGEE,EAAapB,EAAOkB,GAAYV,SAGpC,OAAOQ,I,6BC3ST,SAASlY,EAAQyS,GAEf,GADApX,KAAKqc,QAAU,EACXjF,aAAkBR,YACpB5W,KAAKsd,QAAUlG,EACfpX,KAAKud,MAAQ,IAAIT,SAAS9c,KAAKsd,aAC1B,KAAI1G,YAAYO,OAAOC,GAI5B,MAAM,IAAI3U,MAAM,oBAHhBzC,KAAKsd,QAAUlG,EAAOA,OACtBpX,KAAKud,MAAQ,IAAIT,SAAS9c,KAAKsd,QAASlG,EAAOoG,WAAYpG,EAAO+D,aA+CtExW,EAAQjH,UAAU+f,OAAS,SAAU/e,GAEnC,IADA,IAAI3B,EAAQ,IAAI6B,MAAMF,GACb5C,EAAI,EAAGA,EAAI4C,EAAQ5C,IAC1BiB,EAAMjB,GAAKkE,KAAK0d,SAElB,OAAO3gB,GAGT4H,EAAQjH,UAAUigB,KAAO,SAAUjf,GAEjC,IADA,IAAc3B,EAAQ,GACbjB,EAAI,EAAGA,EAAI4C,EAAQ5C,IAE1BiB,EADMiD,KAAK0d,UACE1d,KAAK0d,SAEpB,OAAO3gB,GAGT4H,EAAQjH,UAAUye,KAAO,SAAUzd,GACjC,IAAI3B,EA3DN,SAAkB0e,EAAMC,EAAQhd,GAE9B,IADA,IAAIkf,EAAS,GAAIC,EAAM,EACd/hB,EAAI4f,EAAQoC,EAAMpC,EAAShd,EAAQ5C,EAAIgiB,EAAKhiB,IAAK,CACxD,IAAIiiB,EAAOtC,EAAKuC,SAASliB,GACzB,GAAsB,IAAV,IAAPiiB,GAIL,GAAsB,MAAV,IAAPA,GAOL,GAAsB,MAAV,IAAPA,GAAL,CAQA,GAAsB,MAAV,IAAPA,GAaL,MAAM,IAAItb,MAAM,gBAAkBsb,EAAKtZ,SAAS,MAZ9CoZ,GAAe,EAAPE,IAAgB,IACC,GAArBtC,EAAKuC,WAAWliB,KAAc,IACT,GAArB2f,EAAKuC,WAAWliB,KAAc,GACT,GAArB2f,EAAKuC,WAAWliB,KAAc,IACvB,OACT+hB,GAAO,MACPD,GAAUxf,OAAOC,aAA4B,OAAdwf,IAAQ,IAA8B,OAAT,KAANA,KAEtDD,GAAUxf,OAAOC,aAAawf,QAhBhCD,GAAUxf,OAAOC,cACN,GAAP0f,IAAgB,IACK,GAArBtC,EAAKuC,WAAWliB,KAAc,GACT,GAArB2f,EAAKuC,WAAWliB,KAAc,QAVlC8hB,GAAUxf,OAAOC,cACN,GAAP0f,IAAgB,EACI,GAArBtC,EAAKuC,WAAWliB,SANnB8hB,GAAUxf,OAAOC,aAAa0f,GAiClC,OAAOH,EAqBKK,CAASje,KAAKud,MAAOvd,KAAKqc,QAAS3d,GAE/C,OADAsB,KAAKqc,SAAW3d,EACT3B,GAGT4H,EAAQjH,UAAUgf,KAAO,SAAUhe,GACjC,IAAI3B,EAAQiD,KAAKsd,QAAQvc,MAAMf,KAAKqc,QAASrc,KAAKqc,QAAU3d,GAE5D,OADAsB,KAAKqc,SAAW3d,EACT3B,GAGT4H,EAAQjH,UAAUggB,OAAS,WACzB,IACI3gB,EADAmhB,EAASle,KAAKud,MAAMS,SAAShe,KAAKqc,WAC3B3d,EAAS,EAAGc,EAAO,EAAGwc,EAAK,EAAGC,EAAK,EAE9C,GAAIiC,EAAS,IAEX,OAAIA,EAAS,IACJA,EAGLA,EAAS,IACJle,KAAK2d,KAAc,GAATO,GAGfA,EAAS,IACJle,KAAKyd,OAAgB,GAATS,GAGdle,KAAKmc,KAAc,GAAT+B,GAInB,GAAIA,EAAS,IACX,OAA8B,GAAtB,IAAOA,EAAS,GAG1B,OAAQA,GAEN,KAAK,IACH,OAAO,KAET,KAAK,IACH,OAAO,EAET,KAAK,IACH,OAAO,EAGT,KAAK,IAGH,OAFAxf,EAASsB,KAAKud,MAAMS,SAAShe,KAAKqc,SAClCrc,KAAKqc,SAAW,EACTrc,KAAK0c,KAAKhe,GACnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMY,UAAUne,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAK0c,KAAKhe,GACnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMa,UAAUpe,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAK0c,KAAKhe,GAGnB,KAAK,IAIH,OAHAA,EAASsB,KAAKud,MAAMS,SAAShe,KAAKqc,SAClC7c,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAKhe,IAC1B,KAAK,IAIH,OAHAA,EAASsB,KAAKud,MAAMY,UAAUne,KAAKqc,SACnC7c,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAKhe,IAC1B,KAAK,IAIH,OAHAA,EAASsB,KAAKud,MAAMa,UAAUpe,KAAKqc,SACnC7c,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAKhe,IAG1B,KAAK,IAGH,OAFA3B,EAAQiD,KAAKud,MAAMe,WAAWte,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMgB,WAAWve,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTtf,EAGT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMS,SAAShe,KAAKqc,SACjCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMY,UAAUne,KAAKqc,SAClCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMa,UAAUpe,KAAKqc,SAClCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAIH,OAHAif,EAAKhc,KAAKud,MAAMa,UAAUpe,KAAKqc,SAAWhY,KAAKiY,IAAI,EAAG,IACtDL,EAAKjc,KAAKud,MAAMa,UAAUpe,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACTL,EAAKC,EAGd,KAAK,IAGH,OAFAlf,EAAQiD,KAAKud,MAAMc,QAAQre,KAAKqc,SAChCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMiB,SAASxe,KAAKqc,SACjCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAGH,OAFAA,EAAQiD,KAAKud,MAAMkB,SAASze,KAAKqc,SACjCrc,KAAKqc,SAAW,EACTtf,EACT,KAAK,IAIH,OAHAif,EAAKhc,KAAKud,MAAMkB,SAASze,KAAKqc,SAAWhY,KAAKiY,IAAI,EAAG,IACrDL,EAAKjc,KAAKud,MAAMa,UAAUpe,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACTL,EAAKC,EAGd,KAAK,IAGH,OAFAzc,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,SAC/Brc,KAAKqc,SAAW,EACH,IAAT7c,OACFQ,KAAKqc,SAAW,GAGX,CAAC7c,EAAMQ,KAAK0c,KAAK,IAC1B,KAAK,IAGH,OAFAld,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,SAC/Brc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAK,IAC1B,KAAK,IAGH,OAFAld,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,SAC/Brc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAK,IAC1B,KAAK,IAGH,OAFAld,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,SAC/Brc,KAAKqc,SAAW,EACH,IAAT7c,GACFwc,EAAKhc,KAAKud,MAAMkB,SAASze,KAAKqc,SAAWhY,KAAKiY,IAAI,EAAG,IACrDL,EAAKjc,KAAKud,MAAMa,UAAUpe,KAAKqc,QAAU,GACzCrc,KAAKqc,SAAW,EACT,IAAItO,KAAKiO,EAAKC,IAEhB,CAACzc,EAAMQ,KAAK0c,KAAK,IAC1B,KAAK,IAGH,OAFAld,EAAOQ,KAAKud,MAAMc,QAAQre,KAAKqc,SAC/Brc,KAAKqc,SAAW,EACT,CAAC7c,EAAMQ,KAAK0c,KAAK,KAG1B,KAAK,IAGH,OAFAhe,EAASsB,KAAKud,MAAMS,SAAShe,KAAKqc,SAClCrc,KAAKqc,SAAW,EACTrc,KAAKmc,KAAKzd,GACnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMY,UAAUne,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAKmc,KAAKzd,GACnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMa,UAAUpe,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAKmc,KAAKzd,GAGnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMY,UAAUne,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAKyd,OAAO/e,GACrB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMa,UAAUpe,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAKyd,OAAO/e,GAGrB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMY,UAAUne,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAK2d,KAAKjf,GACnB,KAAK,IAGH,OAFAA,EAASsB,KAAKud,MAAMa,UAAUpe,KAAKqc,SACnCrc,KAAKqc,SAAW,EACTrc,KAAK2d,KAAKjf,GAGrB,MAAM,IAAI+D,MAAM,oBAYlBlH,EAAOD,QATP,SAAgB8b,GACd,IAAIlP,EAAU,IAAIvD,EAAQyS,GACtBra,EAAQmL,EAAQwV,SACpB,GAAIxV,EAAQmU,UAAYjF,EAAO+D,WAC7B,MAAM,IAAI1Y,MAAO2U,EAAO+D,WAAajT,EAAQmU,QAAW,mBAE1D,OAAOtf,I,cClQT,SAASiK,EAAQnF,GACfA,EAAOA,GAAQ,GACf7B,KAAK0e,GAAK7c,EAAK8F,KAAO,IACtB3H,KAAK4H,IAAM/F,EAAK+F,KAAO,IACvB5H,KAAK2e,OAAS9c,EAAK8c,QAAU,EAC7B3e,KAAK6H,OAAShG,EAAKgG,OAAS,GAAKhG,EAAKgG,QAAU,EAAIhG,EAAKgG,OAAS,EAClE7H,KAAKiJ,SAAW,EApBlB1N,EAAOD,QAAU0L,EA8BjBA,EAAQtJ,UAAUmN,SAAW,WAC3B,IAAI6T,EAAK1e,KAAK0e,GAAKra,KAAKiY,IAAItc,KAAK2e,OAAQ3e,KAAKiJ,YAC9C,GAAIjJ,KAAK6H,OAAQ,CACf,IAAI+W,EAAQva,KAAKwa,SACbC,EAAYza,KAAKC,MAAMsa,EAAO5e,KAAK6H,OAAS6W,GAChDA,EAAoC,IAAN,EAAxBra,KAAKC,MAAa,GAAPsa,IAAwBF,EAAKI,EAAYJ,EAAKI,EAEjE,OAAgC,EAAzBza,KAAKsD,IAAI+W,EAAI1e,KAAK4H,MAS3BZ,EAAQtJ,UAAUgN,MAAQ,WACxB1K,KAAKiJ,SAAW,GASlBjC,EAAQtJ,UAAUgL,OAAS,SAASf,GAClC3H,KAAK0e,GAAK/W,GASZX,EAAQtJ,UAAUoL,OAAS,SAASlB,GAClC5H,KAAK4H,IAAMA,GASbZ,EAAQtJ,UAAUkL,UAAY,SAASf,GACrC7H,KAAK6H,OAASA", "file": "socket.io.msgpack.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(self, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 17);\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "const globalThis = require(\"./globalThis\");\n\nmodule.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\n\nmodule.exports.installTimerFunctions = (obj, opts) => {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n  }\n};\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst { installTimerFunctions } = require(\"./util\");\n\n\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n    installTimerFunctions(this, opts);\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      // this might happen if the transport was silently closed in the beforeunload event handler\n\n\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "var msgpack = require(\"notepack.io\");\nvar Emitter = require(\"component-emitter\");\n\nexports.protocol = 5;\n\n/**\n * Packet types (see https://github.com/socketio/socket.io-protocol)\n */\n\nvar PacketType = (exports.PacketType = {\n  CONNECT: 0,\n  DISCONNECT: 1,\n  EVENT: 2,\n  ACK: 3,\n  CONNECT_ERROR: 4,\n});\n\nvar isInteger =\n  Number.isInteger ||\n  function (value) {\n    return (\n      typeof value === \"number\" &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    );\n  };\n\nvar isString = function (value) {\n  return typeof value === \"string\";\n};\n\nvar isObject = function (value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n};\n\nfunction Encoder() {}\n\nEncoder.prototype.encode = function (packet) {\n  return [msgpack.encode(packet)];\n};\n\nfunction Decoder() {}\n\nEmitter(Decoder.prototype);\n\nDecoder.prototype.add = function (obj) {\n  var decoded = msgpack.decode(obj);\n  this.checkPacket(decoded);\n  this.emit(\"decoded\", decoded);\n};\n\nfunction isDataValid(decoded) {\n  switch (decoded.type) {\n    case PacketType.CONNECT:\n      return decoded.data === undefined || isObject(decoded.data);\n    case PacketType.DISCONNECT:\n      return decoded.data === undefined;\n    case PacketType.CONNECT_ERROR:\n      return isString(decoded.data) || isObject(decoded.data);\n    default:\n      return Array.isArray(decoded.data);\n  }\n}\n\nDecoder.prototype.checkPacket = function (decoded) {\n  var isTypeValid =\n    isInteger(decoded.type) &&\n    decoded.type >= PacketType.CONNECT &&\n    decoded.type <= PacketType.CONNECT_ERROR;\n  if (!isTypeValid) {\n    throw new Error(\"invalid packet type\");\n  }\n\n  if (!isString(decoded.nsp)) {\n    throw new Error(\"invalid namespace\");\n  }\n\n  if (!isDataValid(decoded)) {\n    throw new Error(\"invalid payload\");\n  }\n\n  var isAckValid = decoded.id === undefined || isInteger(decoded.id);\n  if (!isAckValid) {\n    throw new Error(\"invalid packet id\");\n  }\n};\n\nDecoder.prototype.destroy = function () {};\n\nexports.Encoder = Encoder;\nexports.Decoder = Decoder;\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst util_1 = require(\"engine.io-client/lib/util\");\nconst socket_1 = require(\"./socket\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\nconst typed_events_1 = require(\"./typed-events\");\n\n\nclass Manager extends typed_events_1.StrictEventEmitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0, util_1.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n\n\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n\n\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0, on_1.on)(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = (0, on_1.on)(socket, \"error\", (err) => {\n\n\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n\n\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n\n\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0, on_1.on)(socket, \"ping\", this.onping.bind(this)), (0, on_1.on)(socket, \"data\", this.ondata.bind(this)), (0, on_1.on)(socket, \"error\", this.onerror.bind(this)), (0, on_1.on)(socket, \"close\", this.onclose.bind(this)), (0, on_1.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n\n\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n\n\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n\n\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n\n\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n\n\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n\n\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n\n\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n\n\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n\n\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\n\n\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n\n    const pause = () => {\n\n\n      this.readyState = \"paused\";\n      onPause();\n    };\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n\n\n        total++;\n        this.once(\"pollComplete\", function() {\n\n\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n\n\n        total++;\n        this.once(\"drain\", function() {\n\n\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n\n\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n\n\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n\n\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const close = () => {\n\n\n      this.write([{ type: \"close\" }]);\n    };\n\n    if (\"open\" === this.readyState) {\n\n\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n\n\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst typed_events_1 = require(\"./typed-events\");\n\n\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends typed_events_1.StrictEventEmitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.disconnected = true;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            (0, on_1.on)(io, \"open\", this.onopen.bind(this)),\n            (0, on_1.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0, on_1.on)(io, \"error\", this.onerror.bind(this)),\n            (0, on_1.on)(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n\n\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n\n\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n\n\n        if (null != packet.id) {\n\n\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n\n\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n\n\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n\n\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n\n\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n\n\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n\n\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.StrictEventEmitter = void 0;\nconst Emitter = require(\"component-emitter\");\n/**\n * Strictly typed version of an `EventEmitter`. A `TypedEventEmitter` takes type\n * parameters for mappings of event names to event data types, and strictly\n * types method calls to the `EventEmitter` according to these event maps.\n *\n * @typeParam ListenEvents - `EventsMap` of user-defined events that can be\n * listened to with `on` or `once`\n * @typeParam EmitEvents - `EventsMap` of user-defined events that can be\n * emitted with `emit`\n * @typeParam ReservedEvents - `EventsMap` of reserved events, that can be\n * emitted by socket.io with `emitReserved`, and can be listened to with\n * `listen`.\n */\nclass StrictEventEmitter extends Emitter {\n    /**\n     * Adds the `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    on(ev, listener) {\n        super.on(ev, listener);\n        return this;\n    }\n    /**\n     * Adds a one-time `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    once(ev, listener) {\n        super.once(ev, listener);\n        return this;\n    }\n    /**\n     * Emits an event.\n     *\n     * @param ev Name of the event\n     * @param args Values to send to listeners of this event\n     */\n    emit(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Emits a reserved event.\n     *\n     * This method is `protected`, so that only a class extending\n     * `StrictEventEmitter` can emit its own reserved events.\n     *\n     * @param ev Reserved event name\n     * @param args Arguments to emit along with the event\n     */\n    emitReserved(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Returns the listeners listening to an event.\n     *\n     * @param event Event name\n     * @returns Array of listeners subscribed to `event`\n     */\n    listeners(event) {\n        return super.listeners(event);\n    }\n}\nexports.StrictEventEmitter = StrictEventEmitter;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.io = exports.Socket = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\n\n\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0, url_1.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n\n\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n\n\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\nvar socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\nexports.default = lookup;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\n\n\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n\n\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\n\n\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\nconst { installTimerFunctions } = require(\"./util\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    installTimerFunctions(this, opts);\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {},\n        closeOnBeforeunload: true\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      if (this.opts.closeOnBeforeunload) {\n        // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n        // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n        // closed/reloaded)\n        addEventListener(\n          \"beforeunload\",\n          () => {\n            if (this.transport) {\n              // silently close the transport\n              this.transport.removeAllListeners();\n              this.transport.close();\n            }\n          },\n          false\n        );\n      }\n      if (this.hostname !== \"localhost\") {\n        this.offlineEventListener = () => {\n          this.onClose(\"transport close\");\n        };\n        addEventListener(\"offline\", this.offlineEventListener, false);\n      }\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n\n\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n\n\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      this.setTimeoutFn(() => {\n        this.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n\n\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n\n\n\n    if (this.transport) {\n\n\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", this.onDrain.bind(this))\n      .on(\"packet\", this.onPacket.bind(this))\n      .on(\"error\", this.onError.bind(this))\n      .on(\"close\", () => {\n        this.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n\n\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n\n    Socket.priorWebsocketSuccess = false;\n\n    const onTransportOpen = () => {\n      if (failed) return;\n\n\n\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n\n\n          this.upgrading = true;\n          this.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n\n\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n\n\n\n            cleanup();\n\n            this.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            this.emit(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n\n\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          this.emit(\"upgradeError\", err);\n        }\n      });\n    };\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n\n\n\n      this.emit(\"upgradeError\", error);\n    };\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n\n\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.removeListener(\"close\", onclose);\n      this.removeListener(\"upgrading\", onupgrade);\n    };\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n\n\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n\n\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"ping\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n\n\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    this.clearTimeoutFn(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = this.setTimeoutFn(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n    if (this.opts.autoUnref) {\n      this.pingTimeoutTimer.unref();\n    }\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n\n\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const close = () => {\n      this.onClose(\"forced close\");\n\n\n      this.transport.close();\n    };\n\n    const cleanupAndClose = () => {\n      this.removeListener(\"upgrade\", cleanupAndClose);\n      this.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    };\n\n    const waitForUpgrade = () => {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      this.once(\"upgrade\", cleanupAndClose);\n      this.once(\"upgradeError\", cleanupAndClose);\n    };\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", () => {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n\n\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      // clear timers\n      this.clearTimeoutFn(this.pingIntervalTimer);\n      this.clearTimeoutFn(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      if (typeof removeEventListener === \"function\") {\n        removeEventListener(\"offline\", this.offlineEventListener, false);\n      }\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      this.writeBuffer = [];\n      this.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick, installTimerFunctions } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\n\n\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", err => {\n      this.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n\n\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", err => {\n      this.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\",\n      \"autoUnref\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n\n    try {\n\n\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = () => {\n          this.onLoad();\n        };\n        xhr.onerror = () => {\n          this.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = () => {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            this.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            this.setTimeoutFn(() => {\n              this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n\n\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    callbacks.push(this.onData.bind(this));\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = e => {\n      this.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      this.setTimeoutFn(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    const initIframe = () => {\n      if (this.iframe) {\n        try {\n          this.form.removeChild(this.iframe);\n        } catch (e) {\n          this.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + this.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = this.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = this.iframeId;\n\n      this.form.appendChild(iframe);\n      this.iframe = iframe;\n    };\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = () => {\n        if (this.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType,\n  nextTick\n} = require(\"./websocket-constructor\");\n\n\n\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n      this.onOpen();\n    };\n    this.ws.onclose = this.onClose.bind(this);\n    this.ws.onmessage = ev => this.onData(ev.data);\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n\n      parser.encodePacket(packet, this.supportsBinary, data => {\n        // always create a new object (GH-437)\n        const opts = {};\n        if (!usingBrowserWebSocket) {\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (this.opts.perMessageDeflate) {\n            const len =\n              \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            this.ws.send(data);\n          } else {\n            this.ws.send(data, opts);\n          }\n        } catch (e) {\n\n\n        }\n\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emit(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "const globalThis = require(\"../globalThis\");\nconst nextTick = (() => {\n  const isPromiseAvailable =\n    typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n  if (isPromiseAvailable) {\n    return cb => Promise.resolve().then(cb);\n  } else {\n    return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n  }\n})();\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\",\n  nextTick\n};\n", "exports.encode = require('./encode');\nexports.decode = require('./decode');\n", "'use strict';\n\nfunction utf8Write(view, offset, str) {\n  var c = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      view.setUint8(offset++, c);\n    }\n    else if (c < 0x800) {\n      view.setUint8(offset++, 0xc0 | (c >> 6));\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      view.setUint8(offset++, 0xe0 | (c >> 12));\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else {\n      i++;\n      c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n      view.setUint8(offset++, 0xf0 | (c >> 18));\n      view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n  }\n}\n\nfunction utf8Length(str) {\n  var c = 0, length = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    }\n    else if (c < 0x800) {\n      length += 2;\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    }\n    else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n\nfunction _encode(bytes, defers, value) {\n  var type = typeof value, i = 0, l = 0, hi = 0, lo = 0, length = 0, size = 0;\n\n  if (type === 'string') {\n    length = utf8Length(value);\n\n    // fixstr\n    if (length < 0x20) {\n      bytes.push(length | 0xa0);\n      size = 1;\n    }\n    // str 8\n    else if (length < 0x100) {\n      bytes.push(0xd9, length);\n      size = 2;\n    }\n    // str 16\n    else if (length < 0x10000) {\n      bytes.push(0xda, length >> 8, length);\n      size = 3;\n    }\n    // str 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdb, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('String too long');\n    }\n    defers.push({ _str: value, _length: length, _offset: bytes.length });\n    return size + length;\n  }\n  if (type === 'number') {\n    // TODO: encode to float 32?\n\n    // float 64\n    if (Math.floor(value) !== value || !isFinite(value)) {\n      bytes.push(0xcb);\n      defers.push({ _float: value, _length: 8, _offset: bytes.length });\n      return 9;\n    }\n\n    if (value >= 0) {\n      // positive fixnum\n      if (value < 0x80) {\n        bytes.push(value);\n        return 1;\n      }\n      // uint 8\n      if (value < 0x100) {\n        bytes.push(0xcc, value);\n        return 2;\n      }\n      // uint 16\n      if (value < 0x10000) {\n        bytes.push(0xcd, value >> 8, value);\n        return 3;\n      }\n      // uint 32\n      if (value < 0x100000000) {\n        bytes.push(0xce, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // uint 64\n      hi = (value / Math.pow(2, 32)) >> 0;\n      lo = value >>> 0;\n      bytes.push(0xcf, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    } else {\n      // negative fixnum\n      if (value >= -0x20) {\n        bytes.push(value);\n        return 1;\n      }\n      // int 8\n      if (value >= -0x80) {\n        bytes.push(0xd0, value);\n        return 2;\n      }\n      // int 16\n      if (value >= -0x8000) {\n        bytes.push(0xd1, value >> 8, value);\n        return 3;\n      }\n      // int 32\n      if (value >= -0x80000000) {\n        bytes.push(0xd2, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // int 64\n      hi = Math.floor(value / Math.pow(2, 32));\n      lo = value >>> 0;\n      bytes.push(0xd3, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    }\n  }\n  if (type === 'object') {\n    // nil\n    if (value === null) {\n      bytes.push(0xc0);\n      return 1;\n    }\n\n    if (Array.isArray(value)) {\n      length = value.length;\n\n      // fixarray\n      if (length < 0x10) {\n        bytes.push(length | 0x90);\n        size = 1;\n      }\n      // array 16\n      else if (length < 0x10000) {\n        bytes.push(0xdc, length >> 8, length);\n        size = 3;\n      }\n      // array 32\n      else if (length < 0x100000000) {\n        bytes.push(0xdd, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Array too large');\n      }\n      for (i = 0; i < length; i++) {\n        size += _encode(bytes, defers, value[i]);\n      }\n      return size;\n    }\n\n    // fixext 8 / Date\n    if (value instanceof Date) {\n      var time = value.getTime();\n      hi = Math.floor(time / Math.pow(2, 32));\n      lo = time >>> 0;\n      bytes.push(0xd7, 0, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 10;\n    }\n\n    if (value instanceof ArrayBuffer) {\n      length = value.byteLength;\n\n      // bin 8\n      if (length < 0x100) {\n        bytes.push(0xc4, length);\n        size = 2;\n      } else\n      // bin 16\n      if (length < 0x10000) {\n        bytes.push(0xc5, length >> 8, length);\n        size = 3;\n      } else\n      // bin 32\n      if (length < 0x100000000) {\n        bytes.push(0xc6, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Buffer too large');\n      }\n      defers.push({ _bin: value, _length: length, _offset: bytes.length });\n      return size + length;\n    }\n\n    if (typeof value.toJSON === 'function') {\n      return _encode(bytes, defers, value.toJSON());\n    }\n\n    var keys = [], key = '';\n\n    var allKeys = Object.keys(value);\n    for (i = 0, l = allKeys.length; i < l; i++) {\n      key = allKeys[i];\n      if (typeof value[key] !== 'function') {\n        keys.push(key);\n      }\n    }\n    length = keys.length;\n\n    // fixmap\n    if (length < 0x10) {\n      bytes.push(length | 0x80);\n      size = 1;\n    }\n    // map 16\n    else if (length < 0x10000) {\n      bytes.push(0xde, length >> 8, length);\n      size = 3;\n    }\n    // map 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdf, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('Object too large');\n    }\n\n    for (i = 0; i < length; i++) {\n      key = keys[i];\n      size += _encode(bytes, defers, key);\n      size += _encode(bytes, defers, value[key]);\n    }\n    return size;\n  }\n  // false/true\n  if (type === 'boolean') {\n    bytes.push(value ? 0xc3 : 0xc2);\n    return 1;\n  }\n  // fixext 1 / undefined\n  if (type === 'undefined') {\n    bytes.push(0xd4, 0, 0);\n    return 3;\n  }\n  throw new Error('Could not encode');\n}\n\nfunction encode(value) {\n  var bytes = [];\n  var defers = [];\n  var size = _encode(bytes, defers, value);\n  var buf = new ArrayBuffer(size);\n  var view = new DataView(buf);\n\n  var deferIndex = 0;\n  var deferWritten = 0;\n  var nextOffset = -1;\n  if (defers.length > 0) {\n    nextOffset = defers[0]._offset;\n  }\n\n  var defer, deferLength = 0, offset = 0;\n  for (var i = 0, l = bytes.length; i < l; i++) {\n    view.setUint8(deferWritten + i, bytes[i]);\n    if (i + 1 !== nextOffset) { continue; }\n    defer = defers[deferIndex];\n    deferLength = defer._length;\n    offset = deferWritten + nextOffset;\n    if (defer._bin) {\n      var bin = new Uint8Array(defer._bin);\n      for (var j = 0; j < deferLength; j++) {\n        view.setUint8(offset + j, bin[j]);\n      }\n    } else if (defer._str) {\n      utf8Write(view, offset, defer._str);\n    } else if (defer._float !== undefined) {\n      view.setFloat64(offset, defer._float);\n    }\n    deferIndex++;\n    deferWritten += deferLength;\n    if (defers[deferIndex]) {\n      nextOffset = defers[deferIndex]._offset;\n    }\n  }\n  return buf;\n}\n\nmodule.exports = encode;\n", "'use strict';\n\nfunction Decoder(buffer) {\n  this._offset = 0;\n  if (buffer instanceof ArrayBuffer) {\n    this._buffer = buffer;\n    this._view = new DataView(this._buffer);\n  } else if (ArrayBuffer.isView(buffer)) {\n    this._buffer = buffer.buffer;\n    this._view = new DataView(this._buffer, buffer.byteOffset, buffer.byteLength);\n  } else {\n    throw new Error('Invalid argument');\n  }\n}\n\nfunction utf8Read(view, offset, length) {\n  var string = '', chr = 0;\n  for (var i = offset, end = offset + length; i < end; i++) {\n    var byte = view.getUint8(i);\n    if ((byte & 0x80) === 0x00) {\n      string += String.fromCharCode(byte);\n      continue;\n    }\n    if ((byte & 0xe0) === 0xc0) {\n      string += String.fromCharCode(\n        ((byte & 0x1f) << 6) |\n        (view.getUint8(++i) & 0x3f)\n      );\n      continue;\n    }\n    if ((byte & 0xf0) === 0xe0) {\n      string += String.fromCharCode(\n        ((byte & 0x0f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0)\n      );\n      continue;\n    }\n    if ((byte & 0xf8) === 0xf0) {\n      chr = ((byte & 0x07) << 18) |\n        ((view.getUint8(++i) & 0x3f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0);\n      if (chr >= 0x010000) { // surrogate pair\n        chr -= 0x010000;\n        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n      } else {\n        string += String.fromCharCode(chr);\n      }\n      continue;\n    }\n    throw new Error('Invalid byte ' + byte.toString(16));\n  }\n  return string;\n}\n\nDecoder.prototype._array = function (length) {\n  var value = new Array(length);\n  for (var i = 0; i < length; i++) {\n    value[i] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._map = function (length) {\n  var key = '', value = {};\n  for (var i = 0; i < length; i++) {\n    key = this._parse();\n    value[key] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._str = function (length) {\n  var value = utf8Read(this._view, this._offset, length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._bin = function (length) {\n  var value = this._buffer.slice(this._offset, this._offset + length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._parse = function () {\n  var prefix = this._view.getUint8(this._offset++);\n  var value, length = 0, type = 0, hi = 0, lo = 0;\n\n  if (prefix < 0xc0) {\n    // positive fixint\n    if (prefix < 0x80) {\n      return prefix;\n    }\n    // fixmap\n    if (prefix < 0x90) {\n      return this._map(prefix & 0x0f);\n    }\n    // fixarray\n    if (prefix < 0xa0) {\n      return this._array(prefix & 0x0f);\n    }\n    // fixstr\n    return this._str(prefix & 0x1f);\n  }\n\n  // negative fixint\n  if (prefix > 0xdf) {\n    return (0xff - prefix + 1) * -1;\n  }\n\n  switch (prefix) {\n    // nil\n    case 0xc0:\n      return null;\n    // false\n    case 0xc2:\n      return false;\n    // true\n    case 0xc3:\n      return true;\n\n    // bin\n    case 0xc4:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._bin(length);\n    case 0xc5:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._bin(length);\n    case 0xc6:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._bin(length);\n\n    // ext\n    case 0xc7:\n      length = this._view.getUint8(this._offset);\n      type = this._view.getInt8(this._offset + 1);\n      this._offset += 2;\n      return [type, this._bin(length)];\n    case 0xc8:\n      length = this._view.getUint16(this._offset);\n      type = this._view.getInt8(this._offset + 2);\n      this._offset += 3;\n      return [type, this._bin(length)];\n    case 0xc9:\n      length = this._view.getUint32(this._offset);\n      type = this._view.getInt8(this._offset + 4);\n      this._offset += 5;\n      return [type, this._bin(length)];\n\n    // float\n    case 0xca:\n      value = this._view.getFloat32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcb:\n      value = this._view.getFloat64(this._offset);\n      this._offset += 8;\n      return value;\n\n    // uint\n    case 0xcc:\n      value = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xcd:\n      value = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xce:\n      value = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcf:\n      hi = this._view.getUint32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // int\n    case 0xd0:\n      value = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xd1:\n      value = this._view.getInt16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xd2:\n      value = this._view.getInt32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xd3:\n      hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // fixext\n    case 0xd4:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        this._offset += 1;\n        return void 0;\n      }\n      return [type, this._bin(1)];\n    case 0xd5:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(2)];\n    case 0xd6:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(4)];\n    case 0xd7:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n        lo = this._view.getUint32(this._offset + 4);\n        this._offset += 8;\n        return new Date(hi + lo);\n      }\n      return [type, this._bin(8)];\n    case 0xd8:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(16)];\n\n    // str\n    case 0xd9:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._str(length);\n    case 0xda:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._str(length);\n    case 0xdb:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._str(length);\n\n    // array\n    case 0xdc:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._array(length);\n    case 0xdd:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._array(length);\n\n    // map\n    case 0xde:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._map(length);\n    case 0xdf:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._map(length);\n  }\n\n  throw new Error('Could not parse');\n};\n\nfunction decode(buffer) {\n  var decoder = new Decoder(buffer);\n  var value = decoder._parse();\n  if (decoder._offset !== buffer.byteLength) {\n    throw new Error((buffer.byteLength - decoder._offset) + ' trailing bytes');\n  }\n  return value;\n}\n\nmodule.exports = decode;\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n"], "sourceRoot": ""}