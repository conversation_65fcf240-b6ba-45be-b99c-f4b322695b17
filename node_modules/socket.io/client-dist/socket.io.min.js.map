{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/socket.io-parser/dist/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./build/manager.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/yeast/index.js", "webpack://io/./build/socket.js", "webpack://io/./node_modules/socket.io-parser/dist/is-binary.js", "webpack://io/./build/on.js", "webpack://io/./build/typed-events.js", "webpack://io/./build/index.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/socket.io-parser/dist/binary.js", "webpack://io/./node_modules/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "window", "Function", "encodePacket", "require", "decodePacket", "SEPARATOR", "String", "fromCharCode", "protocol", "encodePayload", "packets", "callback", "length", "encodedPackets", "Array", "count", "for<PERSON>ach", "packet", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "push", "type", "Emitter", "obj", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "len", "slice", "listeners", "hasListeners", "globalThis", "pick", "attr", "reduce", "acc", "k", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "clearTimeoutFn", "parser", "Transport", "query", "readyState", "socket", "msg", "desc", "err", "Error", "description", "doOpen", "doClose", "onClose", "write", "writable", "data", "onPacket", "encode", "str", "encodeURIComponent", "decode", "qs", "qry", "pairs", "pair", "decodeURIComponent", "Decoder", "Encoder", "PacketType", "binary_1", "is_binary_1", "EVENT", "ACK", "hasBinary", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "encodeAsBinary", "attachments", "nsp", "id", "JSON", "stringify", "deconstruction", "deconstructPacket", "pack", "buffers", "unshift", "decodeString", "reconstructor", "BinaryReconstructor", "isBinary", "base64", "takeBinaryData", "Number", "char<PERSON>t", "undefined", "start", "buf", "substring", "next", "payload", "parse", "e", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "isArray", "reconPack", "binData", "reconstructPacket", "re", "parts", "src", "b", "indexOf", "replace", "exec", "uri", "source", "host", "authority", "ipv6uri", "pathNames", "path", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Manager", "eio", "util_1", "socket_1", "on_1", "Backoff", "_a", "nsps", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "decoder", "_autoConnect", "autoConnect", "open", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "engine", "skipReconnect", "openSubDestroy", "onopen", "errorSub", "cleanup", "emit<PERSON><PERSON><PERSON><PERSON>", "maybeReconnectOnOpen", "timer", "close", "autoUnref", "unref", "onping", "ondata", "onerror", "onclose", "ondecoded", "add", "Socket", "keys", "active", "_close", "options", "subDestroy", "destroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "StrictEventEmitter", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xd", "xs", "jsonp", "location", "isSSL", "port", "hostname", "secure", "xdomain", "xscheme", "forceJSONP", "hasCORS", "enablesXDR", "XDomainRequest", "concat", "parseqs", "yeast", "Polling", "poll", "onPause", "pause", "total", "doPoll", "onOpen", "doWrite", "schema", "timestampRequests", "timestampParam", "supportsBinary", "sid", "b64", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "prev", "alphabet", "map", "seed", "num", "encoded", "Math", "floor", "now", "Date", "decoded", "socket_io_parser_1", "typed_events_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "disconnected", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "auth", "onpacket", "subEvents", "ev", "compress", "pop", "isTransportWritable", "transport", "discardPacket", "_packet", "onconnect", "onevent", "onack", "ondisconnect", "message", "ack", "emitEvent", "_anyListeners", "sent", "emitBuffered", "listener", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toString", "withNativeBlob", "Blob", "withNativeFile", "File", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "toJSON", "url_1", "manager_1", "lookup", "cache", "managers", "parsed", "url", "sameNamespace", "forceNew", "multiplex", "manager_2", "parseuri", "loc", "test", "ipv6", "href", "transports", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "offlineEventListener", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "onError", "probe", "failed", "onTransportOpen", "send", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "cleanupAndClose", "waitForUpgrade", "pingIntervalTimer", "filteredUpgrades", "j", "empty", "hasXHR2", "responseType", "forceBase64", "Request", "req", "request", "method", "onData", "pollXhr", "async", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onload", "onLoad", "responseText", "onreadystatechange", "status", "document", "index", "requestsCount", "requests", "onSuccess", "fromError", "abort", "attachEvent", "unload<PERSON><PERSON><PERSON>", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "base64decoder", "decodeBase64Packet", "mapBinary", "chars", "arraybuffer", "bytes", "Uint8Array", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "navigator", "userAgent", "area", "iframeId", "className", "style", "position", "top", "left", "target", "setAttribute", "complete", "initIframe", "action", "html", "submit", "WebSocket", "usingBrowserWebSocket", "defaultBinaryType", "nextTick", "isReactNative", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "_socket", "onmessage", "lastPacket", "<PERSON><PERSON><PERSON>", "byteLength", "Promise", "resolve", "then", "MozWebSocket", "packetData", "_deconstructPacket", "placeholder", "_placeholder", "newData", "_reconstructPacket", "ms", "factor", "pow", "rand", "random", "deviation"], "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAY,GAAID,IAEhBD,EAAS,GAAIC,IARf,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,I,gBClFrDtC,EAAOD,QACe,oBAATI,KACFA,KACoB,oBAAXoC,OACTA,OAEAC,SAAS,cAATA,I,gBCNX,IAAMC,EAAeC,EAAQ,IACvBC,EAAeD,EAAQ,IAEvBE,EAAYC,OAAOC,aAAa,IAgCtC9C,EAAOD,QAAU,CACfgD,SAAU,EACVN,eACAO,cAjCoB,SAACC,EAASC,GAE9B,IAAMC,EAASF,EAAQE,OACjBC,EAAiB,IAAIC,MAAMF,GAC7BG,EAAQ,EAEZL,EAAQM,SAAQ,SAACC,EAAQjD,GAEvBkC,EAAae,GAAQ,GAAO,SAAAC,GAC1BL,EAAe7C,GAAKkD,IACdH,IAAUH,GACdD,EAASE,EAAeM,KAAKd,WAuBnCD,eACAgB,cAlBoB,SAACC,EAAgBC,GAGrC,IAFA,IAAMT,EAAiBQ,EAAeE,MAAMlB,GACtCK,EAAU,GACP1C,EAAI,EAAGA,EAAI6C,EAAeD,OAAQ5C,IAAK,CAC9C,IAAMwD,EAAgBpB,EAAaS,EAAe7C,GAAIsD,GAEtD,GADAZ,EAAQe,KAAKD,GACc,UAAvBA,EAAcE,KAChB,MAGJ,OAAOhB,K,gBCjBT,SAASiB,EAAQC,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIrC,KAAOoC,EAAQ/B,UACtBgC,EAAIrC,GAAOoC,EAAQ/B,UAAUL,GAE/B,OAAOqC,EAfSC,CAAMD,GAVtBnE,EAAOD,QAAUmE,EAqCnBA,EAAQ/B,UAAUkC,GAClBH,EAAQ/B,UAAUmC,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DP,KAAKQ,GACDC,MAaTP,EAAQ/B,UAAUwC,KAAO,SAASJ,EAAOC,GACvC,SAASH,IACPI,KAAKG,IAAIL,EAAOF,GAChBG,EAAGK,MAAMJ,KAAMK,WAKjB,OAFAT,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTP,EAAQ/B,UAAUyC,IAClBV,EAAQ/B,UAAU4C,eAClBb,EAAQ/B,UAAU6C,mBAClBd,EAAQ/B,UAAU8C,oBAAsB,SAASV,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKI,UAAU3B,OAEjB,OADAsB,KAAKC,WAAa,GACXD,KAIT,IAUIS,EAVAC,EAAYV,KAAKC,WAAW,IAAMH,GACtC,IAAKY,EAAW,OAAOV,KAGvB,GAAI,GAAKK,UAAU3B,OAEjB,cADOsB,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAIlE,EAAI,EAAGA,EAAI4E,EAAUhC,OAAQ5C,IAEpC,IADA2E,EAAKC,EAAU5E,MACJiE,GAAMU,EAAGV,KAAOA,EAAI,CAC7BW,EAAUC,OAAO7E,EAAG,GACpB,MAUJ,OAJyB,IAArB4E,EAAUhC,eACLsB,KAAKC,WAAW,IAAMH,GAGxBE,MAWTP,EAAQ/B,UAAUkD,KAAO,SAASd,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIY,EAAO,IAAIjC,MAAMyB,UAAU3B,OAAS,GACpCgC,EAAYV,KAAKC,WAAW,IAAMH,GAE7BhE,EAAI,EAAGA,EAAIuE,UAAU3B,OAAQ5C,IACpC+E,EAAK/E,EAAI,GAAKuE,UAAUvE,GAG1B,GAAI4E,EAEG,CAAI5E,EAAI,EAAb,IAAK,IAAWgF,GADhBJ,EAAYA,EAAUK,MAAM,IACIrC,OAAQ5C,EAAIgF,IAAOhF,EACjD4E,EAAU5E,GAAGsE,MAAMJ,KAAMa,GAI7B,OAAOb,MAWTP,EAAQ/B,UAAUsD,UAAY,SAASlB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,IAWzCL,EAAQ/B,UAAUuD,aAAe,SAASnB,GACxC,QAAUE,KAAKgB,UAAUlB,GAAOpB,S,gBC7KlC,IAAMwC,EAAajD,EAAQ,GAE3B1C,EAAOD,QAAQ6F,KAAO,SAACzB,GAAiB,2BAAT0B,EAAS,iCAATA,EAAS,kBACtC,OAAOA,EAAKC,QAAO,SAACC,EAAKC,GAIvB,OAHI7B,EAAI/B,eAAe4D,KACrBD,EAAIC,GAAK7B,EAAI6B,IAERD,IACN,KAIL,IAAME,EAAqBC,WACrBC,EAAuBC,aAE7BpG,EAAOD,QAAQsG,sBAAwB,SAAClC,EAAKmC,GACvCA,EAAKC,iBACPpC,EAAIqC,aAAeP,EAAmBlE,KAAK4D,GAC3CxB,EAAIsC,eAAiBN,EAAqBpE,KAAK4D,KAE/CxB,EAAIqC,aAAeN,WAAWnE,KAAK4D,GACnCxB,EAAIsC,eAAiBL,aAAarE,KAAK4D,M,20CCrB3C,IAAMe,EAAShE,EAAQ,GACjBwB,EAAUxB,EAAQ,GAChB2D,EAA0B3D,EAAQ,GAAlC2D,sBAIFM,E,sQAOJ,WAAYL,GAAM,a,4FAAA,SAChB,eACAD,EAAsB,EAAD,GAAOC,GAE5B,EAAKA,KAAOA,EACZ,EAAKM,MAAQN,EAAKM,MAClB,EAAKC,WAAa,GAClB,EAAKC,OAASR,EAAKQ,OAPH,E,oCAiBlB,SAAQC,EAAKC,GACX,IAAMC,EAAM,IAAIC,MAAMH,GAItB,OAHAE,EAAIhD,KAAO,iBACXgD,EAAIE,YAAcH,EAClBvC,KAAKY,KAAK,QAAS4B,GACZxC,O,kBAQT,WAME,MALI,WAAaA,KAAKoC,YAAc,KAAOpC,KAAKoC,aAC9CpC,KAAKoC,WAAa,UAClBpC,KAAK2C,UAGA3C,O,mBAQT,WAME,MALI,YAAcA,KAAKoC,YAAc,SAAWpC,KAAKoC,aACnDpC,KAAK4C,UACL5C,KAAK6C,WAGA7C,O,kBAST,SAAKxB,GACC,SAAWwB,KAAKoC,YAClBpC,KAAK8C,MAAMtE,K,oBAaf,WACEwB,KAAKoC,WAAa,OAClBpC,KAAK+C,UAAW,EAChB/C,KAAKY,KAAK,U,oBASZ,SAAOoC,GACL,IAAMjE,EAASkD,EAAO/D,aAAa8E,EAAMhD,KAAKqC,OAAOjD,YACrDY,KAAKiD,SAASlE,K,sBAMhB,SAASA,GACPiB,KAAKY,KAAK,SAAU7B,K,qBAQtB,WACEiB,KAAKoC,WAAa,SAClBpC,KAAKY,KAAK,c,8BAhHUnB,GAoHxBlE,EAAOD,QAAU4G,G,cClHjB5G,EAAQ4H,OAAS,SAAUxD,GACzB,IAAIyD,EAAM,GAEV,IAAK,IAAIrH,KAAK4D,EACRA,EAAI/B,eAAe7B,KACjBqH,EAAIzE,SAAQyE,GAAO,KACvBA,GAAOC,mBAAmBtH,GAAK,IAAMsH,mBAAmB1D,EAAI5D,KAIhE,OAAOqH,GAUT7H,EAAQ+H,OAAS,SAASC,GAGxB,IAFA,IAAIC,EAAM,GACNC,EAAQF,EAAGjE,MAAM,KACZvD,EAAI,EAAGC,EAAIyH,EAAM9E,OAAQ5C,EAAIC,EAAGD,IAAK,CAC5C,IAAI2H,EAAOD,EAAM1H,GAAGuD,MAAM,KAC1BkE,EAAIG,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE7D,OAAOF,I,mxDClCT/G,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQqI,QAAUrI,EAAQsI,QAAUtI,EAAQuI,WAAavI,EAAQgD,cAAW,EAC5E,IAWIuF,EAXEpE,EAAUxB,EAAQ,GAClB6F,EAAW7F,EAAQ,IACnB8F,EAAc9F,EAAQ,IAQ5B3C,EAAQgD,SAAW,EAEnB,SAAWuF,GACPA,EAAWA,EAAU,QAAc,GAAK,UACxCA,EAAWA,EAAU,WAAiB,GAAK,aAC3CA,EAAWA,EAAU,MAAY,GAAK,QACtCA,EAAWA,EAAU,IAAU,GAAK,MACpCA,EAAWA,EAAU,cAAoB,GAAK,gBAC9CA,EAAWA,EAAU,aAAmB,GAAK,eAC7CA,EAAWA,EAAU,WAAiB,GAAK,aAP/C,CAQGA,EAAavI,EAAQuI,aAAevI,EAAQuI,WAAa,K,IAItDD,E,kEAOF,SAAOlE,GAGH,OAAIA,EAAIF,OAASqE,EAAWG,OAAStE,EAAIF,OAASqE,EAAWI,MACrDF,EAAYG,UAAUxE,GAQvB,CAACM,KAAKmE,eAAezE,KAPpBA,EAAIF,KACAE,EAAIF,OAASqE,EAAWG,MAClBH,EAAWO,aACXP,EAAWQ,WACdrE,KAAKsE,eAAe5E,M,4BAQvC,SAAeA,GAEX,IAAIyD,EAAM,GAAKzD,EAAIF,KAqBnB,OAnBIE,EAAIF,OAASqE,EAAWO,cACxB1E,EAAIF,OAASqE,EAAWQ,aACxBlB,GAAOzD,EAAI6E,YAAc,KAIzB7E,EAAI8E,KAAO,MAAQ9E,EAAI8E,MACvBrB,GAAOzD,EAAI8E,IAAM,KAGjB,MAAQ9E,EAAI+E,KACZtB,GAAOzD,EAAI+E,IAGX,MAAQ/E,EAAIsD,OACZG,GAAOuB,KAAKC,UAAUjF,EAAIsD,OAIvBG,I,4BAOX,SAAezD,GACX,IAAMkF,EAAiBd,EAASe,kBAAkBnF,GAC5CoF,EAAO9E,KAAKmE,eAAeS,EAAe7F,QAC1CgG,EAAUH,EAAeG,QAE/B,OADAA,EAAQC,QAAQF,GACTC,M,KAGfzJ,EAAQsI,QAAUA,E,IAMZD,E,gQACF,aAAc,8B,6BAQd,SAAIjE,GACA,IAAIX,EACJ,GAAmB,iBAARW,GACPX,EAASiB,KAAKiF,aAAavF,IAChBF,OAASqE,EAAWO,cAC3BrF,EAAOS,OAASqE,EAAWQ,YAE3BrE,KAAKkF,cAAgB,IAAIC,EAAoBpG,GAElB,IAAvBA,EAAOwF,aACP,wCAAW,UAAWxF,IAK1B,wCAAW,UAAWA,OAGzB,KAAIgF,EAAYqB,SAAS1F,KAAQA,EAAI2F,OAetC,MAAM,IAAI5C,MAAM,iBAAmB/C,GAbnC,IAAKM,KAAKkF,cACN,MAAM,IAAIzC,MAAM,qDAGhB1D,EAASiB,KAAKkF,cAAcI,eAAe5F,MAGvCM,KAAKkF,cAAgB,KACrB,wCAAW,UAAWnG,O,0BActC,SAAaoE,GACT,IAAIrH,EAAI,EAEF8B,EAAI,CACN4B,KAAM+F,OAAOpC,EAAIqC,OAAO,KAE5B,QAA2BC,IAAvB5B,EAAWjG,EAAE4B,MACb,MAAM,IAAIiD,MAAM,uBAAyB7E,EAAE4B,MAG/C,GAAI5B,EAAE4B,OAASqE,EAAWO,cACtBxG,EAAE4B,OAASqE,EAAWQ,WAAY,CAElC,IADA,IAAMqB,EAAQ5J,EAAI,EACS,MAApBqH,EAAIqC,SAAS1J,IAAcA,GAAKqH,EAAIzE,SAC3C,IAAMiH,EAAMxC,EAAIyC,UAAUF,EAAO5J,GACjC,GAAI6J,GAAOJ,OAAOI,IAA0B,MAAlBxC,EAAIqC,OAAO1J,GACjC,MAAM,IAAI2G,MAAM,uBAEpB7E,EAAE2G,YAAcgB,OAAOI,GAG3B,GAAI,MAAQxC,EAAIqC,OAAO1J,EAAI,GAAI,CAE3B,IADA,IAAM4J,EAAQ5J,EAAI,IACTA,GAAG,CAER,GAAI,MADMqH,EAAIqC,OAAO1J,GAEjB,MACJ,GAAIA,IAAMqH,EAAIzE,OACV,MAERd,EAAE4G,IAAMrB,EAAIyC,UAAUF,EAAO5J,QAG7B8B,EAAE4G,IAAM,IAGZ,IAAMqB,EAAO1C,EAAIqC,OAAO1J,EAAI,GAC5B,GAAI,KAAO+J,GAAQN,OAAOM,IAASA,EAAM,CAErC,IADA,IAAMH,EAAQ5J,EAAI,IACTA,GAAG,CACR,IAAMK,EAAIgH,EAAIqC,OAAO1J,GACrB,GAAI,MAAQK,GAAKoJ,OAAOpJ,IAAMA,EAAG,GAC3BL,EACF,MAEJ,GAAIA,IAAMqH,EAAIzE,OACV,MAERd,EAAE6G,GAAKc,OAAOpC,EAAIyC,UAAUF,EAAO5J,EAAI,IAG3C,GAAIqH,EAAIqC,SAAS1J,GAAI,CACjB,IAAMgK,EAsClB,SAAkB3C,GACd,IACI,OAAOuB,KAAKqB,MAAM5C,GAEtB,MAAO6C,GACH,OAAO,GA3CaC,CAAS9C,EAAI+C,OAAOpK,IACpC,IAAI6H,EAAQwC,eAAevI,EAAE4B,KAAMsG,GAI/B,MAAM,IAAIrD,MAAM,mBAHhB7E,EAAEoF,KAAO8C,EAQjB,OAAOlI,I,qBAqBX,WACQoC,KAAKkF,eACLlF,KAAKkF,cAAckB,4B,6BArB3B,SAAsB5G,EAAMsG,GACxB,OAAQtG,GACJ,KAAKqE,EAAWwC,QACZ,MAA0B,WAAnB,EAAOP,GAClB,KAAKjC,EAAWyC,WACZ,YAAmBb,IAAZK,EACX,KAAKjC,EAAW0C,cACZ,MAA0B,iBAAZT,GAA2C,WAAnB,EAAOA,GACjD,KAAKjC,EAAWG,MAChB,KAAKH,EAAWO,aACZ,OAAOxF,MAAM4H,QAAQV,IAAYA,EAAQpH,OAAS,EACtD,KAAKmF,EAAWI,IAChB,KAAKJ,EAAWQ,WACZ,OAAOzF,MAAM4H,QAAQV,Q,GAhIfrG,GA4ItBnE,EAAQqI,QAAUA,E,IAiBZwB,E,WACF,WAAYpG,GAAQ,UAChBiB,KAAKjB,OAASA,EACdiB,KAAK+E,QAAU,GACf/E,KAAKyG,UAAY1H,E,wCAUrB,SAAe2H,GAEX,GADA1G,KAAK+E,QAAQxF,KAAKmH,GACd1G,KAAK+E,QAAQrG,SAAWsB,KAAKyG,UAAUlC,YAAa,CAEpD,IAAMxF,EAAS+E,EAAS6C,kBAAkB3G,KAAKyG,UAAWzG,KAAK+E,SAE/D,OADA/E,KAAKoG,yBACErH,EAEX,OAAO,O,oCAKX,WACIiB,KAAKyG,UAAY,KACjBzG,KAAK+E,QAAU,O,oBClRvB,IAAI6B,EAAK,0OAELC,EAAQ,CACR,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAGzItL,EAAOD,QAAU,SAAkB6H,GAC/B,IAAI2D,EAAM3D,EACN4D,EAAI5D,EAAI6D,QAAQ,KAChBhB,EAAI7C,EAAI6D,QAAQ,MAEV,GAAND,IAAiB,GAANf,IACX7C,EAAMA,EAAIyC,UAAU,EAAGmB,GAAK5D,EAAIyC,UAAUmB,EAAGf,GAAGiB,QAAQ,KAAM,KAAO9D,EAAIyC,UAAUI,EAAG7C,EAAIzE,SAO9F,IAJA,IAmCmByD,EACfa,EApCA9G,EAAI0K,EAAGM,KAAK/D,GAAO,IACnBgE,EAAM,GACNrL,EAAI,GAEDA,KACHqL,EAAIN,EAAM/K,IAAMI,EAAEJ,IAAM,GAa5B,OAVU,GAANiL,IAAiB,GAANf,IACXmB,EAAIC,OAASN,EACbK,EAAIE,KAAOF,EAAIE,KAAKzB,UAAU,EAAGuB,EAAIE,KAAK3I,OAAS,GAAGuI,QAAQ,KAAM,KACpEE,EAAIG,UAAYH,EAAIG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EE,EAAII,SAAU,GAGlBJ,EAAIK,UAMR,SAAmB9H,EAAK+H,GACpB,IACIC,EAAQD,EAAKR,QADN,WACoB,KAAK5H,MAAM,KAEjB,KAArBoI,EAAKvB,OAAO,EAAG,IAA6B,IAAhBuB,EAAK/I,QACjCgJ,EAAM/G,OAAO,EAAG,GAEmB,KAAnC8G,EAAKvB,OAAOuB,EAAK/I,OAAS,EAAG,IAC7BgJ,EAAM/G,OAAO+G,EAAMhJ,OAAS,EAAG,GAGnC,OAAOgJ,EAjBSF,CAAUL,EAAKA,EAAG,MAClCA,EAAIQ,UAmBexF,EAnBUgF,EAAG,MAoB5BnE,EAAO,GAEXb,EAAM8E,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA7E,EAAK6E,GAAMC,MAIZ9E,GA1BAmE,I,w1CCvCX3K,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQyM,aAAU,EAClB,IAAMC,EAAM/J,EAAQ,IACdgK,EAAShK,EAAQ,GACjBiK,EAAWjK,EAAQ,IACnBgE,EAAShE,EAAQ,GACjBkK,EAAOlK,EAAQ,IACfmK,EAAUnK,EAAQ,IAIlB8J,E,sQACF,WAAYZ,EAAKtF,GAAM,MACfwG,G,4FADe,UAEnB,gBACKC,KAAO,GACZ,EAAKC,KAAO,GACRpB,GAAO,WAAa,EAAOA,KAC3BtF,EAAOsF,EACPA,OAAM1B,IAEV5D,EAAOA,GAAQ,IACV4F,KAAO5F,EAAK4F,MAAQ,aACzB,EAAK5F,KAAOA,GACZ,EAAIoG,EAAOrG,uBAAX,KAAwCC,GACxC,EAAK2G,cAAmC,IAAtB3G,EAAK2G,cACvB,EAAKC,qBAAqB5G,EAAK4G,sBAAwBC,KACvD,EAAKC,kBAAkB9G,EAAK8G,mBAAqB,KACjD,EAAKC,qBAAqB/G,EAAK+G,sBAAwB,KACvD,EAAKC,oBAAwD,QAAnCR,EAAKxG,EAAKgH,2BAAwC,IAAPR,EAAgBA,EAAK,IAC1F,EAAKS,QAAU,IAAIV,EAAQ,CACvBW,IAAK,EAAKJ,oBACVK,IAAK,EAAKJ,uBACVK,OAAQ,EAAKJ,wBAEjB,EAAKK,QAAQ,MAAQrH,EAAKqH,QAAU,IAAQrH,EAAKqH,SACjD,EAAKC,YAAc,SACnB,EAAKhC,IAAMA,EACX,IAAMiC,EAAUvH,EAAKI,QAAUA,EA1BZ,OA2BnB,EAAKoH,QAAU,IAAID,EAAQxF,QAC3B,EAAK0F,QAAU,IAAIF,EAAQzF,QAC3B,EAAK4F,cAAoC,IAArB1H,EAAK2H,YACrB,EAAKD,cACL,EAAKE,OA/BU,E,yCAiCvB,SAAaC,GACT,OAAKrJ,UAAU3B,QAEfsB,KAAK2J,gBAAkBD,EAChB1J,MAFIA,KAAK2J,gB,kCAIpB,SAAqBD,GACjB,YAAUjE,IAANiE,EACO1J,KAAK4J,uBAChB5J,KAAK4J,sBAAwBF,EACtB1J,Q,+BAEX,SAAkB0J,GACd,IAAIrB,EACJ,YAAU5C,IAANiE,EACO1J,KAAK6J,oBAChB7J,KAAK6J,mBAAqBH,EACF,QAAvBrB,EAAKrI,KAAK8I,eAA4B,IAAPT,GAAyBA,EAAGyB,OAAOJ,GAC5D1J,Q,iCAEX,SAAoB0J,GAChB,IAAIrB,EACJ,YAAU5C,IAANiE,EACO1J,KAAK+J,sBAChB/J,KAAK+J,qBAAuBL,EACJ,QAAvBrB,EAAKrI,KAAK8I,eAA4B,IAAPT,GAAyBA,EAAG2B,UAAUN,GAC/D1J,Q,kCAEX,SAAqB0J,GACjB,IAAIrB,EACJ,YAAU5C,IAANiE,EACO1J,KAAKiK,uBAChBjK,KAAKiK,sBAAwBP,EACL,QAAvBrB,EAAKrI,KAAK8I,eAA4B,IAAPT,GAAyBA,EAAG6B,OAAOR,GAC5D1J,Q,qBAEX,SAAQ0J,GACJ,OAAKrJ,UAAU3B,QAEfsB,KAAKmK,SAAWT,EACT1J,MAFIA,KAAKmK,W,kCAUpB,YAESnK,KAAKoK,eACNpK,KAAK2J,eACqB,IAA1B3J,KAAK8I,QAAQuB,UAEbrK,KAAKsK,c,kBAUb,SAAKvK,GAAI,WAGL,IAAKC,KAAKmJ,YAAYnC,QAAQ,QAC1B,OAAOhH,KAGXA,KAAKuK,OAASvC,EAAIhI,KAAKmH,IAAKnH,KAAK6B,MACjC,IAAMQ,EAASrC,KAAKuK,OACd7O,EAAOsE,KACbA,KAAKmJ,YAAc,UACnBnJ,KAAKwK,eAAgB,EAErB,IAAMC,GAAiB,EAAItC,EAAKvI,IAAIyC,EAAQ,QAAQ,WAChD3G,EAAKgP,SACL3K,GAAMA,OAGJ4K,GAAW,EAAIxC,EAAKvI,IAAIyC,EAAQ,SAAS,SAACG,GAG5C9G,EAAKkP,UACLlP,EAAKyN,YAAc,SACnB,EAAK0B,aAAa,QAASrI,GACvBzC,EACAA,EAAGyC,GAIH9G,EAAKoP,0BAGb,IAAI,IAAU9K,KAAKmK,SAAU,CACzB,IAAMjB,EAAUlJ,KAAKmK,SAGL,IAAZjB,GACAuB,IAGJ,IAAMM,EAAQ/K,KAAK+B,cAAa,WAG5B0I,IACApI,EAAO2I,QACP3I,EAAOzB,KAAK,QAAS,IAAI6B,MAAM,cAChCyG,GACClJ,KAAK6B,KAAKoJ,WACVF,EAAMG,QAEVlL,KAAKuI,KAAKhJ,MAAK,WACXoC,aAAaoJ,MAKrB,OAFA/K,KAAKuI,KAAKhJ,KAAKkL,GACfzK,KAAKuI,KAAKhJ,KAAKoL,GACR3K,O,qBAQX,SAAQD,GACJ,OAAOC,KAAKyJ,KAAK1J,K,oBAOrB,WAIIC,KAAK4K,UAEL5K,KAAKmJ,YAAc,OACnBnJ,KAAK6K,aAAa,QAElB,IAAMxI,EAASrC,KAAKuK,OACpBvK,KAAKuI,KAAKhJ,MAAK,EAAI4I,EAAKvI,IAAIyC,EAAQ,OAAQrC,KAAKmL,OAAO7N,KAAK0C,QAAQ,EAAImI,EAAKvI,IAAIyC,EAAQ,OAAQrC,KAAKoL,OAAO9N,KAAK0C,QAAQ,EAAImI,EAAKvI,IAAIyC,EAAQ,QAASrC,KAAKqL,QAAQ/N,KAAK0C,QAAQ,EAAImI,EAAKvI,IAAIyC,EAAQ,QAASrC,KAAKsL,QAAQhO,KAAK0C,QAAQ,EAAImI,EAAKvI,IAAII,KAAKsJ,QAAS,UAAWtJ,KAAKuL,UAAUjO,KAAK0C,U,oBAOzS,WACIA,KAAK6K,aAAa,U,oBAOtB,SAAO7H,GACHhD,KAAKsJ,QAAQkC,IAAIxI,K,uBAOrB,SAAUjE,GACNiB,KAAK6K,aAAa,SAAU9L,K,qBAOhC,SAAQyD,GAGJxC,KAAK6K,aAAa,QAASrI,K,oBAQ/B,SAAOgC,EAAK3C,GACR,IAAIQ,EAASrC,KAAKsI,KAAK9D,GAKvB,OAJKnC,IACDA,EAAS,IAAI6F,EAASuD,OAAOzL,KAAMwE,EAAK3C,GACxC7B,KAAKsI,KAAK9D,GAAOnC,GAEdA,I,sBAQX,SAASA,GAEL,IADA,IACA,MADa7F,OAAOkP,KAAK1L,KAAKsI,MAC9B,eAAwB,CAAnB,IAAM9D,EAAG,KAEV,GADexE,KAAKsI,KAAK9D,GACdmH,OAGP,OAGR3L,KAAK4L,W,qBAQT,SAAQ7M,GAIJ,IADA,IAAMJ,EAAiBqB,KAAKqJ,QAAQnG,OAAOnE,GAClCjD,EAAI,EAAGA,EAAI6C,EAAeD,OAAQ5C,IACvCkE,KAAKuK,OAAOzH,MAAMnE,EAAe7C,GAAIiD,EAAO8M,W,qBAQpD,WAGI7L,KAAKuI,KAAKzJ,SAAQ,SAACgN,GAAD,OAAgBA,OAClC9L,KAAKuI,KAAK7J,OAAS,EACnBsB,KAAKsJ,QAAQyC,Y,oBAOjB,WAGI/L,KAAKwK,eAAgB,EACrBxK,KAAKoK,eAAgB,EACjB,YAAcpK,KAAKmJ,aAGnBnJ,KAAK4K,UAET5K,KAAK8I,QAAQkD,QACbhM,KAAKmJ,YAAc,SACfnJ,KAAKuK,QACLvK,KAAKuK,OAAOS,U,wBAOpB,WACI,OAAOhL,KAAK4L,W,qBAOhB,SAAQK,GAGJjM,KAAK4K,UACL5K,KAAK8I,QAAQkD,QACbhM,KAAKmJ,YAAc,SACnBnJ,KAAK6K,aAAa,QAASoB,GACvBjM,KAAK2J,gBAAkB3J,KAAKwK,eAC5BxK,KAAKsK,c,uBAQb,WAAY,WACR,GAAItK,KAAKoK,eAAiBpK,KAAKwK,cAC3B,OAAOxK,KACX,IAAMtE,EAAOsE,KACb,GAAIA,KAAK8I,QAAQuB,UAAYrK,KAAK4J,sBAG9B5J,KAAK8I,QAAQkD,QACbhM,KAAK6K,aAAa,oBAClB7K,KAAKoK,eAAgB,MAEpB,CACD,IAAM8B,EAAQlM,KAAK8I,QAAQqD,WAG3BnM,KAAKoK,eAAgB,EACrB,IAAMW,EAAQ/K,KAAK+B,cAAa,WACxBrG,EAAK8O,gBAIT,EAAKK,aAAa,oBAAqBnP,EAAKoN,QAAQuB,UAEhD3O,EAAK8O,eAET9O,EAAK+N,MAAK,SAACjH,GACHA,GAGA9G,EAAK0O,eAAgB,EACrB1O,EAAK4O,YACL,EAAKO,aAAa,kBAAmBrI,IAKrC9G,EAAK0Q,oBAGdF,GACClM,KAAK6B,KAAKoJ,WACVF,EAAMG,QAEVlL,KAAKuI,KAAKhJ,MAAK,WACXoC,aAAaoJ,S,yBASzB,WACI,IAAMsB,EAAUrM,KAAK8I,QAAQuB,SAC7BrK,KAAKoK,eAAgB,EACrBpK,KAAK8I,QAAQkD,QACbhM,KAAK6K,aAAa,YAAawB,Q,8BA/XhBpO,EAAQ,IAGMqO,oBA+XrChR,EAAQyM,QAAUA,G,gBC3YlB,IAAMwE,EAAiBtO,EAAQ,IACzBuO,EAAMvO,EAAQ,IACdwO,EAAQxO,EAAQ,IAChByO,EAAYzO,EAAQ,IAE1B3C,EAAQqR,QAUR,SAAiB9K,GACf,IACI+K,GAAK,EACLC,GAAK,EACHC,GAAQ,IAAUjL,EAAKiL,MAE7B,GAAwB,oBAAbC,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASzO,SAChC2O,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvBJ,EAAK/K,EAAKqL,WAAaH,SAASG,UAAYD,IAASpL,EAAKoL,KAC1DJ,EAAKhL,EAAKsL,SAAWH,EAOvB,GAJAnL,EAAKuL,QAAUR,EACf/K,EAAKwL,QAAUR,EAGX,SAFE,IAAIN,EAAe1K,KAEHA,EAAKyL,WACzB,OAAO,IAAId,EAAI3K,GAEf,IAAKiL,EAAO,MAAM,IAAIrK,MAAM,kBAC5B,OAAO,IAAIgK,EAAM5K,IApCrBvG,EAAQoR,UAAYA,G,gBCJpB,IAAMa,EAAUtP,EAAQ,IAClBiD,EAAajD,EAAQ,GAE3B1C,EAAOD,QAAU,SAASuG,GACxB,IAAMuL,EAAUvL,EAAKuL,QAIfC,EAAUxL,EAAKwL,QAIfG,EAAa3L,EAAK2L,WAGxB,IACE,GAAI,oBAAuBjB,kBAAoBa,GAAWG,GACxD,OAAO,IAAIhB,eAEb,MAAOvG,IAKT,IACE,GAAI,oBAAuByH,iBAAmBJ,GAAWG,EACvD,OAAO,IAAIC,eAEb,MAAOzH,IAET,IAAKoH,EACH,IACE,OAAO,IAAIlM,EAAW,CAAC,UAAUwM,OAAO,UAAUzO,KAAK,OACrD,qBAEF,MAAO+G,O,s6CCrCb,IAAM9D,EAAYjE,EAAQ,GACpB0P,EAAU1P,EAAQ,GAClBgE,EAAShE,EAAQ,GACjB2P,EAAQ3P,EAAQ,IAKhB4P,E,2VAIJ,WACE,MAAO,Y,oBAST,WACE7N,KAAK8N,S,mBASP,SAAMC,GAAS,WACb/N,KAAKoC,WAAa,UAElB,IAAM4L,EAAQ,WAGZ,EAAK5L,WAAa,SAClB2L,KAGF,GAAI/N,KAAK2M,UAAY3M,KAAK+C,SAAU,CAClC,IAAIkL,EAAQ,EAERjO,KAAK2M,UAGPsB,IACAjO,KAAKE,KAAK,gBAAgB,aAGtB+N,GAASD,QAIVhO,KAAK+C,WAGRkL,IACAjO,KAAKE,KAAK,SAAS,aAGf+N,GAASD,aAIfA,M,kBASJ,WAGEhO,KAAK2M,SAAU,EACf3M,KAAKkO,SACLlO,KAAKY,KAAK,U,oBAQZ,SAAOoC,GAAM,WAoBXf,EAAO/C,cAAc8D,EAAMhD,KAAKqC,OAAOjD,YAAYN,SAjBlC,SAAAC,GAOf,GALI,YAAc,EAAKqD,YAA8B,SAAhBrD,EAAOS,MAC1C,EAAK2O,SAIH,UAAYpP,EAAOS,KAErB,OADA,EAAKqD,WACE,EAIT,EAAKI,SAASlE,MAOZ,WAAaiB,KAAKoC,aAEpBpC,KAAK2M,SAAU,EACf3M,KAAKY,KAAK,gBAEN,SAAWZ,KAAKoC,YAClBpC,KAAK8N,U,qBAaX,WAAU,WACF9C,EAAQ,WAGZ,EAAKlI,MAAM,CAAC,CAAEtD,KAAM,YAGlB,SAAWQ,KAAKoC,WAGlB4I,IAMAhL,KAAKE,KAAK,OAAQ8K,K,mBAWtB,SAAMxM,GAAS,WACbwB,KAAK+C,UAAW,EAEhBd,EAAO1D,cAAcC,GAAS,SAAAwE,GAC5B,EAAKoL,QAAQpL,GAAM,WACjB,EAAKD,UAAW,EAChB,EAAKnC,KAAK,iB,iBAUhB,WACE,IAAIuB,EAAQnC,KAAKmC,OAAS,GACpBkM,EAASrO,KAAK6B,KAAKsL,OAAS,QAAU,OACxCF,EAAO,GA4BX,OAzBI,IAAUjN,KAAK6B,KAAKyM,oBACtBnM,EAAMnC,KAAK6B,KAAK0M,gBAAkBX,KAG/B5N,KAAKwO,gBAAmBrM,EAAMsM,MACjCtM,EAAMuM,IAAM,GAGdvM,EAAQwL,EAAQzK,OAAOf,GAIrBnC,KAAK6B,KAAKoL,OACR,UAAYoB,GAAqC,MAA3B9I,OAAOvF,KAAK6B,KAAKoL,OACtC,SAAWoB,GAAqC,KAA3B9I,OAAOvF,KAAK6B,KAAKoL,SAEzCA,EAAO,IAAMjN,KAAK6B,KAAKoL,MAIrB9K,EAAMzD,SACRyD,EAAQ,IAAMA,GAKdkM,EACA,QAHgD,IAArCrO,KAAK6B,KAAKqL,SAASlG,QAAQ,KAI9B,IAAMhH,KAAK6B,KAAKqL,SAAW,IAAMlN,KAAK6B,KAAKqL,UACnDD,EACAjN,KAAK6B,KAAK4F,KACVtF,O,8BA3MgBD,GAgNtB3G,EAAOD,QAAUuS,G,cCxNjB,IAAMc,EAAenS,OAAOY,OAAO,MACnCuR,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IAEvB,IAAMC,EAAuBpS,OAAOY,OAAO,MAC3CZ,OAAOkP,KAAKiD,GAAc7P,SAAQ,SAAAzB,GAChCuR,EAAqBD,EAAatR,IAAQA,KAK5C9B,EAAOD,QAAU,CACfqT,eACAC,uBACAC,aALmB,CAAErP,KAAM,QAASwD,KAAM,kB,6BCZ5C,IAKI8L,EALAC,EAAW,mEAAmE1P,MAAM,IAEpF2P,EAAM,GACNC,EAAO,EACPnT,EAAI,EAUR,SAASoH,EAAOgM,GACd,IAAIC,EAAU,GAEd,GACEA,EAAUJ,EAASG,EAjBV,IAiB0BC,EACnCD,EAAME,KAAKC,MAAMH,EAlBR,UAmBFA,EAAM,GAEf,OAAOC,EA0BT,SAASvB,IACP,IAAI0B,EAAMpM,GAAQ,IAAIqM,MAEtB,OAAID,IAAQR,GAAaG,EAAO,EAAGH,EAAOQ,GACnCA,EAAK,IAAKpM,EAAO+L,KAM1B,KAAOnT,EAzDM,GAyDMA,IAAKkT,EAAID,EAASjT,IAAMA,EAK3C8R,EAAM1K,OAASA,EACf0K,EAAMvK,OAhCN,SAAgBF,GACd,IAAIqM,EAAU,EAEd,IAAK1T,EAAI,EAAGA,EAAIqH,EAAIzE,OAAQ5C,IAC1B0T,EAnCS,GAmCCA,EAAmBR,EAAI7L,EAAIqC,OAAO1J,IAG9C,OAAO0T,GA0BTjU,EAAOD,QAAUsS,G,gpFClEjBpR,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQmQ,YAAS,EACjB,IAAMgE,EAAqBxR,EAAQ,GAC7BkK,EAAOlK,EAAQ,IACfyR,EAAiBzR,EAAQ,IAOzB0R,EAAkBnT,OAAOoT,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb3P,eAAgB,IAEdmL,E,sQAMF,WAAYyE,EAAI1L,EAAK3C,GAAM,a,4FAAA,UACvB,gBACKsO,WAAY,EACjB,EAAKC,cAAe,EACpB,EAAKC,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKC,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKC,MAAQ,GACb,EAAKP,GAAKA,EACV,EAAK1L,IAAMA,EACP3C,GAAQA,EAAK6O,OACb,EAAKA,KAAO7O,EAAK6O,MAEjB,EAAKR,GAAG3G,cACR,EAAKE,OAfc,E,sCAsB3B,WACI,IAAIzJ,KAAKuI,KAAT,CAEA,IAAM2H,EAAKlQ,KAAKkQ,GAChBlQ,KAAKuI,KAAO,EACR,EAAIJ,EAAKvI,IAAIsQ,EAAI,OAAQlQ,KAAK0K,OAAOpN,KAAK0C,QAC1C,EAAImI,EAAKvI,IAAIsQ,EAAI,SAAUlQ,KAAK2Q,SAASrT,KAAK0C,QAC9C,EAAImI,EAAKvI,IAAIsQ,EAAI,QAASlQ,KAAKqL,QAAQ/N,KAAK0C,QAC5C,EAAImI,EAAKvI,IAAIsQ,EAAI,QAASlQ,KAAKsL,QAAQhO,KAAK0C,W,kBAMpD,WACI,QAASA,KAAKuI,O,qBAOlB,WACI,OAAIvI,KAAKmQ,YAETnQ,KAAK4Q,YACA5Q,KAAKkQ,GAAL,eACDlQ,KAAKkQ,GAAGzG,OACR,SAAWzJ,KAAKkQ,GAAG/G,aACnBnJ,KAAK0K,UALE1K,O,kBAWf,WACI,OAAOA,KAAK6P,Y,kBAQhB,WAAc,2BAANhP,EAAM,yBAANA,EAAM,gBAGV,OAFAA,EAAKmE,QAAQ,WACbhF,KAAKY,KAAKR,MAAMJ,KAAMa,GACfb,O,kBASX,SAAK6Q,GACD,GAAIlB,EAAgBhS,eAAekT,GAC/B,MAAM,IAAIpO,MAAM,IAAMoO,EAAK,8BAFjB,2BAANhQ,EAAM,iCAANA,EAAM,kBAIdA,EAAKmE,QAAQ6L,GACb,IAAM9R,EAAS,CACXS,KAAMiQ,EAAmB5L,WAAWG,MACpChB,KAAMnC,EAEV9B,QAAiB,IACjBA,EAAO8M,QAAQiF,UAAmC,IAAxB9Q,KAAKyQ,MAAMK,SAEjC,mBAAsBjQ,EAAKA,EAAKnC,OAAS,KAGzCsB,KAAKwQ,KAAKxQ,KAAKuQ,KAAO1P,EAAKkQ,MAC3BhS,EAAO0F,GAAKzE,KAAKuQ,OAErB,IAAMS,EAAsBhR,KAAKkQ,GAAG3F,QAChCvK,KAAKkQ,GAAG3F,OAAO0G,WACfjR,KAAKkQ,GAAG3F,OAAO0G,UAAUlO,SACvBmO,EAAgBlR,KAAKyQ,MAAL,YAAyBO,IAAwBhR,KAAKmQ,WAY5E,OAXIe,IAIKlR,KAAKmQ,UACVnQ,KAAKjB,OAAOA,GAGZiB,KAAKsQ,WAAW/Q,KAAKR,IAEzBiB,KAAKyQ,MAAQ,GACNzQ,O,oBAQX,SAAOjB,GACHA,EAAOyF,IAAMxE,KAAKwE,IAClBxE,KAAKkQ,GAAGiB,QAAQpS,K,oBAOpB,WAAS,WAGmB,mBAAbiB,KAAK0Q,KACZ1Q,KAAK0Q,MAAK,SAAC1N,GACP,EAAKjE,OAAO,CAAES,KAAMiQ,EAAmB5L,WAAWwC,QAASrD,YAI/DhD,KAAKjB,OAAO,CAAES,KAAMiQ,EAAmB5L,WAAWwC,QAASrD,KAAMhD,KAAK0Q,S,qBAS9E,SAAQlO,GACCxC,KAAKmQ,WACNnQ,KAAK6K,aAAa,gBAAiBrI,K,qBAS3C,SAAQyJ,GAGJjM,KAAKmQ,WAAY,EACjBnQ,KAAKoQ,cAAe,SACbpQ,KAAKyE,GACZzE,KAAK6K,aAAa,aAAcoB,K,sBAQpC,SAASlN,GAEL,GADsBA,EAAOyF,MAAQxE,KAAKwE,IAG1C,OAAQzF,EAAOS,MACX,KAAKiQ,EAAmB5L,WAAWwC,QAC/B,GAAItH,EAAOiE,MAAQjE,EAAOiE,KAAKyL,IAAK,CAChC,IAAMhK,EAAK1F,EAAOiE,KAAKyL,IACvBzO,KAAKoR,UAAU3M,QAGfzE,KAAK6K,aAAa,gBAAiB,IAAIpI,MAAM,8LAEjD,MACJ,KAAKgN,EAAmB5L,WAAWG,MAGnC,KAAKyL,EAAmB5L,WAAWO,aAC/BpE,KAAKqR,QAAQtS,GACb,MACJ,KAAK0Q,EAAmB5L,WAAWI,IAGnC,KAAKwL,EAAmB5L,WAAWQ,WAC/BrE,KAAKsR,MAAMvS,GACX,MACJ,KAAK0Q,EAAmB5L,WAAWyC,WAC/BtG,KAAKuR,eACL,MACJ,KAAK9B,EAAmB5L,WAAW0C,cAC/B,IAAM/D,EAAM,IAAIC,MAAM1D,EAAOiE,KAAKwO,SAElChP,EAAIQ,KAAOjE,EAAOiE,KAAKA,KACvBhD,KAAK6K,aAAa,gBAAiBrI,M,qBAU/C,SAAQzD,GACJ,IAAM8B,EAAO9B,EAAOiE,MAAQ,GAGxB,MAAQjE,EAAO0F,IAGf5D,EAAKtB,KAAKS,KAAKyR,IAAI1S,EAAO0F,KAE1BzE,KAAKmQ,UACLnQ,KAAK0R,UAAU7Q,GAGfb,KAAKqQ,cAAc9Q,KAAK/C,OAAOoT,OAAO/O,M,uBAG9C,SAAUA,GACN,GAAIb,KAAK2R,eAAiB3R,KAAK2R,cAAcjT,OAAQ,CACjD,IADiD,MAC/BsB,KAAK2R,cAAc5Q,SADY,IAEjD,2BAAkC,QACrBX,MAAMJ,KAAMa,GAHwB,+BAMrD,8BAAWT,MAAMJ,KAAMa,K,iBAO3B,SAAI4D,GACA,IAAM/I,EAAOsE,KACT4R,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,2BAAN/Q,EAAM,yBAANA,EAAM,gBAOtBnF,EAAKqD,OAAO,CACRS,KAAMiQ,EAAmB5L,WAAWI,IACpCQ,GAAIA,EACJzB,KAAMnC,Q,mBAUlB,SAAM9B,GACF,IAAM0S,EAAMzR,KAAKwQ,KAAKzR,EAAO0F,IACzB,mBAAsBgN,IAGtBA,EAAIrR,MAAMJ,KAAMjB,EAAOiE,aAChBhD,KAAKwQ,KAAKzR,EAAO0F,O,uBAYhC,SAAUA,GAGNzE,KAAKyE,GAAKA,EACVzE,KAAKmQ,WAAY,EACjBnQ,KAAKoQ,cAAe,EACpBpQ,KAAK6R,eACL7R,KAAK6K,aAAa,a,0BAOtB,WAAe,WACX7K,KAAKqQ,cAAcvR,SAAQ,SAAC+B,GAAD,OAAU,EAAK6Q,UAAU7Q,MACpDb,KAAKqQ,cAAgB,GACrBrQ,KAAKsQ,WAAWxR,SAAQ,SAACC,GAAD,OAAY,EAAKA,OAAOA,MAChDiB,KAAKsQ,WAAa,K,0BAOtB,WAGItQ,KAAK+L,UACL/L,KAAKsL,QAAQ,0B,qBASjB,WACQtL,KAAKuI,OAELvI,KAAKuI,KAAKzJ,SAAQ,SAACgN,GAAD,OAAgBA,OAClC9L,KAAKuI,UAAO9C,GAEhBzF,KAAKkQ,GAAL,SAAoBlQ,Q,wBAQxB,WAYI,OAXIA,KAAKmQ,WAGLnQ,KAAKjB,OAAO,CAAES,KAAMiQ,EAAmB5L,WAAWyC,aAGtDtG,KAAK+L,UACD/L,KAAKmQ,WAELnQ,KAAKsL,QAAQ,wBAEVtL,O,mBAQX,WACI,OAAOA,KAAK+P,e,sBAShB,SAASe,GAEL,OADA9Q,KAAKyQ,MAAMK,SAAWA,EACf9Q,O,oBASX,WAEI,OADAA,KAAKyQ,MAAL,UAAsB,EACfzQ,O,mBASX,SAAM8R,GAGF,OAFA9R,KAAK2R,cAAgB3R,KAAK2R,eAAiB,GAC3C3R,KAAK2R,cAAcpS,KAAKuS,GACjB9R,O,wBASX,SAAW8R,GAGP,OAFA9R,KAAK2R,cAAgB3R,KAAK2R,eAAiB,GAC3C3R,KAAK2R,cAAc3M,QAAQ8M,GACpB9R,O,oBAQX,SAAO8R,GACH,IAAK9R,KAAK2R,cACN,OAAO3R,KAEX,GAAI8R,GAEA,IADA,IAAM9Q,EAAYhB,KAAK2R,cACd7V,EAAI,EAAGA,EAAIkF,EAAUtC,OAAQ5C,IAClC,GAAIgW,IAAa9Q,EAAUlF,GAEvB,OADAkF,EAAUL,OAAO7E,EAAG,GACbkE,UAKfA,KAAK2R,cAAgB,GAEzB,OAAO3R,O,0BAQX,WACI,OAAOA,KAAK2R,eAAiB,Q,8BA3bhBjC,EAAepD,oBA8bpChR,EAAQmQ,OAASA,G,kQCldjBjP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ4I,UAAY5I,EAAQ8J,cAAW,EACvC,IAAM2M,EAA+C,mBAAhBC,YAM/BC,EAAWzV,OAAOkB,UAAUuU,SAC5BC,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBF,EAAShW,KAAKkW,MAChBC,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBJ,EAAShW,KAAKoW,MAMtB,SAASjN,EAAS1F,GACd,OAASqS,IAA0BrS,aAAesS,aAlBvC,SAACtS,GACZ,MAAqC,mBAAvBsS,YAAYM,OACpBN,YAAYM,OAAO5S,GACnBA,EAAI6S,kBAAkBP,YAeqCM,CAAO5S,KACnEwS,GAAkBxS,aAAeyS,MACjCC,GAAkB1S,aAAe2S,KAE1C/W,EAAQ8J,SAAWA,EA4BnB9J,EAAQ4I,UA3BR,SAASA,EAAUxE,EAAK8S,GACpB,IAAK9S,GAAsB,WAAf,EAAOA,GACf,OAAO,EAEX,GAAId,MAAM4H,QAAQ9G,GAAM,CACpB,IAAK,IAAI5D,EAAI,EAAGC,EAAI2D,EAAIhB,OAAQ5C,EAAIC,EAAGD,IACnC,GAAIoI,EAAUxE,EAAI5D,IACd,OAAO,EAGf,OAAO,EAEX,GAAIsJ,EAAS1F,GACT,OAAO,EAEX,GAAIA,EAAI8S,QACkB,mBAAf9S,EAAI8S,QACU,IAArBnS,UAAU3B,OACV,OAAOwF,EAAUxE,EAAI8S,UAAU,GAEnC,IAAK,IAAMnV,KAAOqC,EACd,GAAIlD,OAAOkB,UAAUC,eAAe1B,KAAKyD,EAAKrC,IAAQ6G,EAAUxE,EAAIrC,IAChE,OAAO,EAGf,OAAO,I,6BCnDXb,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQsE,QAAK,EAObtE,EAAQsE,GANR,SAAYF,EAAKmR,EAAI9Q,GAEjB,OADAL,EAAIE,GAAGiR,EAAI9Q,GACJ,WACHL,EAAIS,IAAI0Q,EAAI9Q,M,0tDCLpBvD,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQgR,wBAAqB,EAC7B,IAcMA,E,2VAOF,SAAGuE,EAAIiB,GAEH,OADA,sCAASjB,EAAIiB,GACN9R,O,kBAQX,SAAK6Q,EAAIiB,GAEL,OADA,wCAAWjB,EAAIiB,GACR9R,O,kBAQX,SAAK6Q,GAAa,6BAANhQ,EAAM,iCAANA,EAAM,kBAEd,OADA,oDAAWgQ,GAAX,OAAkBhQ,IACXb,O,0BAWX,SAAa6Q,GAAa,6BAANhQ,EAAM,iCAANA,EAAM,kBAEtB,OADA,oDAAWgQ,GAAX,OAAkBhQ,IACXb,O,uBAQX,SAAUF,GACN,oDAAuBA,Q,8BAjEf7B,EAAQ,IAoExB3C,EAAQgR,mBAAqBA,G,kQCtE7B9P,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ4U,GAAK5U,EAAQmQ,OAASnQ,EAAQyM,QAAUzM,EAAQgD,cAAW,EACnE,IAAMmU,EAAQxU,EAAQ,IAChByU,EAAYzU,EAAQ,GAM1B1C,EAAOD,QAAUA,EAAUqX,EAI3B,IAAMC,EAAStX,EAAQuX,SAAW,GAClC,SAASF,EAAOxL,EAAKtF,GACE,WAAf,EAAOsF,KACPtF,EAAOsF,EACPA,OAAM1B,GAEV5D,EAAOA,GAAQ,GACf,IASIqO,EATE4C,GAAS,EAAIL,EAAMM,KAAK5L,EAAKtF,EAAK4F,MAAQ,cAC1CL,EAAS0L,EAAO1L,OAChB3C,EAAKqO,EAAOrO,GACZgD,EAAOqL,EAAOrL,KACduL,EAAgBJ,EAAMnO,IAAOgD,KAAQmL,EAAMnO,GAAN,KAsB3C,OArBsB5C,EAAKoR,UACvBpR,EAAK,0BACL,IAAUA,EAAKqR,WACfF,EAKA9C,EAAK,IAAIwC,EAAU3K,QAAQX,EAAQvF,IAG9B+Q,EAAMnO,KAGPmO,EAAMnO,GAAM,IAAIiO,EAAU3K,QAAQX,EAAQvF,IAE9CqO,EAAK0C,EAAMnO,IAEXqO,EAAO3Q,QAAUN,EAAKM,QACtBN,EAAKM,MAAQ2Q,EAAOnL,UAEjBuI,EAAG7N,OAAOyQ,EAAOrL,KAAM5F,GAElCvG,EAAQ4U,GAAKyC,EAMb,IAAIlD,EAAqBxR,EAAQ,GACjCzB,OAAOC,eAAenB,EAAS,WAAY,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAO8S,EAAmBnR,YAO5GhD,EAAQuU,QAAU8C,EAMlB,IAAIQ,EAAYlV,EAAQ,GACxBzB,OAAOC,eAAenB,EAAS,UAAW,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOwW,EAAUpL,WAClG,IAAIG,EAAWjK,EAAQ,IACvBzB,OAAOC,eAAenB,EAAS,SAAU,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOuL,EAASuD,UAChGnQ,EAAO,QAAWqX,G,6BCxElBnW,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQyX,SAAM,EACd,IAAMK,EAAWnV,EAAQ,GAiEzB3C,EAAQyX,IArDR,SAAa5L,GAAqB,IAAhBM,EAAgB,uDAAT,GAAI4L,EAAK,uCAC1B3T,EAAMyH,EAEVkM,EAAMA,GAA4B,oBAAbtG,UAA4BA,SAC7C,MAAQ5F,IACRA,EAAMkM,EAAI/U,SAAW,KAAO+U,EAAIhM,MAEjB,iBAARF,IACH,MAAQA,EAAI3B,OAAO,KAEf2B,EADA,MAAQA,EAAI3B,OAAO,GACb6N,EAAI/U,SAAW6I,EAGfkM,EAAIhM,KAAOF,GAGpB,sBAAsBmM,KAAKnM,KAIxBA,OADA,IAAuBkM,EACjBA,EAAI/U,SAAW,KAAO6I,EAGtB,WAAaA,GAM3BzH,EAAM0T,EAASjM,IAGdzH,EAAIuN,OACD,cAAcqG,KAAK5T,EAAIpB,UACvBoB,EAAIuN,KAAO,KAEN,eAAeqG,KAAK5T,EAAIpB,YAC7BoB,EAAIuN,KAAO,QAGnBvN,EAAI+H,KAAO/H,EAAI+H,MAAQ,IACvB,IAAM8L,GAAkC,IAA3B7T,EAAI2H,KAAKL,QAAQ,KACxBK,EAAOkM,EAAO,IAAM7T,EAAI2H,KAAO,IAAM3H,EAAI2H,KAS/C,OAPA3H,EAAI+E,GAAK/E,EAAIpB,SAAW,MAAQ+I,EAAO,IAAM3H,EAAIuN,KAAOxF,EAExD/H,EAAI8T,KACA9T,EAAIpB,SACA,MACA+I,GACCgM,GAAOA,EAAIpG,OAASvN,EAAIuN,KAAO,GAAK,IAAMvN,EAAIuN,MAChDvN,I,gBClEX,IAAM+L,EAASxN,EAAQ,IAEvB1C,EAAOD,QAAU,SAAC6L,EAAKtF,GAAN,OAAe,IAAI4J,EAAOtE,EAAKtF,IAOhDtG,EAAOD,QAAQmQ,OAASA,EACxBlQ,EAAOD,QAAQgD,SAAWmN,EAAOnN,SACjC/C,EAAOD,QAAQ4G,UAAYjE,EAAQ,GACnC1C,EAAOD,QAAQmY,WAAaxV,EAAQ,GACpC1C,EAAOD,QAAQ2G,OAAShE,EAAQ,I,wnDCbhC,IAAMwV,EAAaxV,EAAQ,GACrBwB,EAAUxB,EAAQ,GAGlBgE,EAAShE,EAAQ,GACjBmV,EAAWnV,EAAQ,GACnB0P,EAAU1P,EAAQ,GAChB2D,EAA0B3D,EAAQ,GAAlC2D,sBAEF6J,E,sQAQJ,WAAYtE,GAAgB,MAAXtF,EAAW,uDAAJ,GAAI,iBAC1B,eAEIsF,GAAO,WAAa,EAAOA,KAC7BtF,EAAOsF,EACPA,EAAM,MAGJA,GACFA,EAAMiM,EAASjM,GACftF,EAAKqL,SAAW/F,EAAIE,KACpBxF,EAAKsL,OAA0B,UAAjBhG,EAAI7I,UAAyC,QAAjB6I,EAAI7I,SAC9CuD,EAAKoL,KAAO9F,EAAI8F,KACZ9F,EAAIhF,QAAON,EAAKM,MAAQgF,EAAIhF,QACvBN,EAAKwF,OACdxF,EAAKqL,SAAWkG,EAASvR,EAAKwF,MAAMA,MAGtCzF,EAAsB,EAAD,GAAOC,GAE5B,EAAKsL,OACH,MAAQtL,EAAKsL,OACTtL,EAAKsL,OACe,oBAAbJ,UAA4B,WAAaA,SAASzO,SAE3DuD,EAAKqL,WAAarL,EAAKoL,OAEzBpL,EAAKoL,KAAO,EAAKE,OAAS,MAAQ,MAGpC,EAAKD,SACHrL,EAAKqL,WACgB,oBAAbH,SAA2BA,SAASG,SAAW,aACzD,EAAKD,KACHpL,EAAKoL,OACgB,oBAAbF,UAA4BA,SAASE,KACzCF,SAASE,KACT,EAAKE,OACL,IACA,IAEN,EAAKsG,WAAa5R,EAAK4R,YAAc,CAAC,UAAW,aACjD,EAAKrR,WAAa,GAClB,EAAKsR,YAAc,GACnB,EAAKC,cAAgB,EAErB,EAAK9R,KAAO,EACV,CACE4F,KAAM,aACNmM,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACThH,OAAO,EACPyB,eAAgB,IAChBwF,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACjBC,UAAW,MAEbC,iBAAkB,GAClBC,qBAAqB,GAEvBvS,GAGF,EAAKA,KAAK4F,KAAO,EAAK5F,KAAK4F,KAAKR,QAAQ,MAAO,IAAM,IAEtB,iBAApB,EAAKpF,KAAKM,QACnB,EAAKN,KAAKM,MAAQwL,EAAQtK,OAAO,EAAKxB,KAAKM,QAI7C,EAAKsC,GAAK,KACV,EAAK4P,SAAW,KAChB,EAAKC,aAAe,KACpB,EAAKC,YAAc,KAGnB,EAAKC,iBAAmB,KAEQ,mBAArB3U,mBACL,EAAKgC,KAAKuS,qBAIZvU,iBACE,gBACA,WACM,EAAKoR,YAEP,EAAKA,UAAU1Q,qBACf,EAAK0Q,UAAUjG,YAGnB,GAGkB,cAAlB,EAAKkC,WACP,EAAKuH,qBAAuB,WAC1B,EAAK5R,QAAQ,oBAEfhD,iBAAiB,UAAW,EAAK4U,sBAAsB,KAI3D,EAAKhL,OAzGqB,E,4CAmH5B,SAAgBpN,GAGd,IAAM8F,EAkjBV,SAAezC,GACb,IAAMnD,EAAI,GACV,IAAK,IAAIT,KAAK4D,EACRA,EAAI/B,eAAe7B,KACrBS,EAAET,GAAK4D,EAAI5D,IAGf,OAAOS,EAzjBSmY,CAAM1U,KAAK6B,KAAKM,OAG9BA,EAAMwS,IAAM1S,EAAO3D,SAGnB6D,EAAM8O,UAAY5U,EAGd2D,KAAKyE,KAAItC,EAAMsM,IAAMzO,KAAKyE,IAE9B,IAAM5C,EAAO,EACX,GACA7B,KAAK6B,KAAKsS,iBAAiB9X,GAC3B2D,KAAK6B,KACL,CACEM,QACAE,OAAQrC,KACRkN,SAAUlN,KAAKkN,SACfC,OAAQnN,KAAKmN,OACbF,KAAMjN,KAAKiN,OAOf,OAAO,IAAIwG,EAAWpX,GAAMwF,K,kBAQ9B,WAAO,IACDoP,EADC,OAEL,GACEjR,KAAK6B,KAAKkS,iBACVtI,EAAOmJ,wBACmC,IAA1C5U,KAAKyT,WAAWzM,QAAQ,aAExBiK,EAAY,gBACP,IAAI,IAAMjR,KAAKyT,WAAW/U,OAK/B,YAHAsB,KAAK+B,cAAa,WAChB,EAAKnB,KAAK,QAAS,6BAClB,GAGHqQ,EAAYjR,KAAKyT,WAAW,GAE9BzT,KAAKoC,WAAa,UAGlB,IACE6O,EAAYjR,KAAK6U,gBAAgB5D,GACjC,MAAOjL,GAKP,OAFAhG,KAAKyT,WAAWqB,aAChB9U,KAAKyJ,OAIPwH,EAAUxH,OACVzJ,KAAK+U,aAAa9D,K,0BAQpB,SAAaA,GAAW,WAIlBjR,KAAKiR,WAGPjR,KAAKiR,UAAU1Q,qBAIjBP,KAAKiR,UAAYA,EAGjBA,EACGrR,GAAG,QAASI,KAAKgV,QAAQ1X,KAAK0C,OAC9BJ,GAAG,SAAUI,KAAKiD,SAAS3F,KAAK0C,OAChCJ,GAAG,QAASI,KAAKiV,QAAQ3X,KAAK0C,OAC9BJ,GAAG,SAAS,WACX,EAAKiD,QAAQ,wB,mBAUnB,SAAMxG,GAAM,WAGN4U,EAAYjR,KAAK6U,gBAAgBxY,EAAM,CAAE6Y,MAAO,IAChDC,GAAS,EAEb1J,EAAOmJ,uBAAwB,EAE/B,IAAMQ,EAAkB,WAClBD,IAIJlE,EAAUoE,KAAK,CAAC,CAAE7V,KAAM,OAAQwD,KAAM,WACtCiO,EAAU/Q,KAAK,UAAU,SAAAoC,GACvB,IAAI6S,EACJ,GAAI,SAAW7S,EAAI9C,MAAQ,UAAY8C,EAAIU,KAAM,CAK/C,GAFA,EAAKsS,WAAY,EACjB,EAAK1U,KAAK,YAAaqQ,IAClBA,EAAW,OAChBxF,EAAOmJ,sBAAwB,cAAgB3D,EAAU5U,KAIzD,EAAK4U,UAAUjD,OAAM,WACfmH,GACA,WAAa,EAAK/S,aAItBwI,IAEA,EAAKmK,aAAa9D,GAClBA,EAAUoE,KAAK,CAAC,CAAE7V,KAAM,aACxB,EAAKoB,KAAK,UAAWqQ,GACrBA,EAAY,KACZ,EAAKqE,WAAY,EACjB,EAAKC,gBAEF,CAGL,IAAM/S,EAAM,IAAIC,MAAM,eACtBD,EAAIyO,UAAYA,EAAU5U,KAC1B,EAAKuE,KAAK,eAAgB4B,SAKhC,SAASgT,IACHL,IAGJA,GAAS,EAETvK,IAEAqG,EAAUjG,QACViG,EAAY,MAId,IAAM5F,EAAU,SAAA7I,GACd,IAAMiT,EAAQ,IAAIhT,MAAM,gBAAkBD,GAC1CiT,EAAMxE,UAAYA,EAAU5U,KAE5BmZ,IAKA,EAAK5U,KAAK,eAAgB6U,IAG5B,SAASC,IACPrK,EAAQ,oBAIV,SAASC,IACPD,EAAQ,iBAIV,SAASsK,EAAUC,GACb3E,GAAa2E,EAAGvZ,OAAS4U,EAAU5U,MAGrCmZ,IAKJ,IAAM5K,EAAU,WACdqG,EAAU3Q,eAAe,OAAQ8U,GACjCnE,EAAU3Q,eAAe,QAAS+K,GAClC4F,EAAU3Q,eAAe,QAASoV,GAClC,EAAKpV,eAAe,QAASgL,GAC7B,EAAKhL,eAAe,YAAaqV,IAGnC1E,EAAU/Q,KAAK,OAAQkV,GACvBnE,EAAU/Q,KAAK,QAASmL,GACxB4F,EAAU/Q,KAAK,QAASwV,GAExB1V,KAAKE,KAAK,QAASoL,GACnBtL,KAAKE,KAAK,YAAayV,GAEvB1E,EAAUxH,S,oBAQZ,WAUE,GAPAzJ,KAAKoC,WAAa,OAClBqJ,EAAOmJ,sBAAwB,cAAgB5U,KAAKiR,UAAU5U,KAC9D2D,KAAKY,KAAK,QACVZ,KAAKuV,QAKH,SAAWvV,KAAKoC,YAChBpC,KAAK6B,KAAKiS,SACV9T,KAAKiR,UAAUjD,MAMf,IAFA,IAAIlS,EAAI,EACFC,EAAIiE,KAAKqU,SAAS3V,OACjB5C,EAAIC,EAAGD,IACZkE,KAAKkV,MAAMlV,KAAKqU,SAASvY,M,sBAU/B,SAASiD,GACP,GACE,YAAciB,KAAKoC,YACnB,SAAWpC,KAAKoC,YAChB,YAAcpC,KAAKoC,WAUnB,OALApC,KAAKY,KAAK,SAAU7B,GAGpBiB,KAAKY,KAAK,aAEF7B,EAAOS,MACb,IAAK,OACHQ,KAAK6V,YAAYnR,KAAKqB,MAAMhH,EAAOiE,OACnC,MAEF,IAAK,OACHhD,KAAK8V,mBACL9V,KAAK+V,WAAW,QAChB/V,KAAKY,KAAK,QACVZ,KAAKY,KAAK,QACV,MAEF,IAAK,QACH,IAAM4B,EAAM,IAAIC,MAAM,gBACtBD,EAAIwT,KAAOjX,EAAOiE,KAClBhD,KAAKiV,QAAQzS,GACb,MAEF,IAAK,UACHxC,KAAKY,KAAK,OAAQ7B,EAAOiE,MACzBhD,KAAKY,KAAK,UAAW7B,EAAOiE,S,yBAepC,SAAYA,GACVhD,KAAKY,KAAK,YAAaoC,GACvBhD,KAAKyE,GAAKzB,EAAKyL,IACfzO,KAAKiR,UAAU9O,MAAMsM,IAAMzL,EAAKyL,IAChCzO,KAAKqU,SAAWrU,KAAKiW,eAAejT,EAAKqR,UACzCrU,KAAKsU,aAAetR,EAAKsR,aACzBtU,KAAKuU,YAAcvR,EAAKuR,YACxBvU,KAAKmO,SAED,WAAanO,KAAKoC,YACtBpC,KAAK8V,qB,8BAQP,WAAmB,WACjB9V,KAAKgC,eAAehC,KAAKwU,kBACzBxU,KAAKwU,iBAAmBxU,KAAK+B,cAAa,WACxC,EAAKc,QAAQ,kBACZ7C,KAAKsU,aAAetU,KAAKuU,aACxBvU,KAAK6B,KAAKoJ,WACZjL,KAAKwU,iBAAiBtJ,U,qBAS1B,WACElL,KAAK0T,YAAY/S,OAAO,EAAGX,KAAK2T,eAKhC3T,KAAK2T,cAAgB,EAEjB,IAAM3T,KAAK0T,YAAYhV,OACzBsB,KAAKY,KAAK,SAEVZ,KAAKuV,U,mBAST,WAEI,WAAavV,KAAKoC,YAClBpC,KAAKiR,UAAUlO,WACd/C,KAAKsV,WACNtV,KAAK0T,YAAYhV,SAIjBsB,KAAKiR,UAAUoE,KAAKrV,KAAK0T,aAGzB1T,KAAK2T,cAAgB3T,KAAK0T,YAAYhV,OACtCsB,KAAKY,KAAK,Y,mBAad,SAAM0B,EAAKuJ,EAAS9L,GAElB,OADAC,KAAK+V,WAAW,UAAWzT,EAAKuJ,EAAS9L,GAClCC,O,kBAGT,SAAKsC,EAAKuJ,EAAS9L,GAEjB,OADAC,KAAK+V,WAAW,UAAWzT,EAAKuJ,EAAS9L,GAClCC,O,wBAYT,SAAWR,EAAMwD,EAAM6I,EAAS9L,GAW9B,GAVI,mBAAsBiD,IACxBjD,EAAKiD,EACLA,OAAOyC,GAGL,mBAAsBoG,IACxB9L,EAAK8L,EACLA,EAAU,MAGR,YAAc7L,KAAKoC,YAAc,WAAapC,KAAKoC,WAAvD,EAIAyJ,EAAUA,GAAW,IACbiF,UAAW,IAAUjF,EAAQiF,SAErC,IAAM/R,EAAS,CACbS,KAAMA,EACNwD,KAAMA,EACN6I,QAASA,GAEX7L,KAAKY,KAAK,eAAgB7B,GAC1BiB,KAAK0T,YAAYnU,KAAKR,GAClBgB,GAAIC,KAAKE,KAAK,QAASH,GAC3BC,KAAKuV,W,mBAQP,WAAQ,WACAvK,EAAQ,WACZ,EAAKnI,QAAQ,gBAGb,EAAKoO,UAAUjG,SAGXkL,EAAkB,SAAlBA,IACJ,EAAK5V,eAAe,UAAW4V,GAC/B,EAAK5V,eAAe,eAAgB4V,GACpClL,KAGImL,EAAiB,WAErB,EAAKjW,KAAK,UAAWgW,GACrB,EAAKhW,KAAK,eAAgBgW,IAqB5B,MAlBI,YAAclW,KAAKoC,YAAc,SAAWpC,KAAKoC,aACnDpC,KAAKoC,WAAa,UAEdpC,KAAK0T,YAAYhV,OACnBsB,KAAKE,KAAK,SAAS,WACb,EAAKoV,UACPa,IAEAnL,OAGKhL,KAAKsV,UACda,IAEAnL,KAIGhL,O,qBAQT,SAAQwC,GAGNiJ,EAAOmJ,uBAAwB,EAC/B5U,KAAKY,KAAK,QAAS4B,GACnBxC,KAAK6C,QAAQ,kBAAmBL,K,qBAQlC,SAAQyJ,EAAQ1J,GAEZ,YAAcvC,KAAKoC,YACnB,SAAWpC,KAAKoC,YAChB,YAAcpC,KAAKoC,aAMnBpC,KAAKgC,eAAehC,KAAKoW,mBACzBpW,KAAKgC,eAAehC,KAAKwU,kBAGzBxU,KAAKiR,UAAU1Q,mBAAmB,SAGlCP,KAAKiR,UAAUjG,QAGfhL,KAAKiR,UAAU1Q,qBAEoB,mBAAxBC,qBACTA,oBAAoB,UAAWR,KAAKyU,sBAAsB,GAI5DzU,KAAKoC,WAAa,SAGlBpC,KAAKyE,GAAK,KAGVzE,KAAKY,KAAK,QAASqL,EAAQ1J,GAI3BvC,KAAK0T,YAAc,GACnB1T,KAAK2T,cAAgB,K,4BAWzB,SAAeU,GAIb,IAHA,IAAMgC,EAAmB,GACrBva,EAAI,EACFwa,EAAIjC,EAAS3V,OACZ5C,EAAIwa,EAAGxa,KACPkE,KAAKyT,WAAWzM,QAAQqN,EAASvY,KACpCua,EAAiB9W,KAAK8U,EAASvY,IAEnC,OAAOua,O,8BAlqBU5W,GAsqBrBgM,EAAOmJ,uBAAwB,EAQ/BnJ,EAAOnN,SAAW2D,EAAO3D,SAYzB/C,EAAOD,QAAUmQ,G,cC1rBjB,IACElQ,EAAOD,QAAoC,oBAAnBiR,gBACtB,oBAAqB,IAAIA,eAC3B,MAAO/J,GAGPjH,EAAOD,SAAU,I,q5DCbnB,IAAMiR,EAAiBtO,EAAQ,IACzB4P,EAAU5P,EAAQ,IAClBwB,EAAUxB,EAAQ,GACxB,EAAwCA,EAAQ,GAAxCkD,EAAR,EAAQA,KAAMS,EAAd,EAAcA,sBACRV,EAAajD,EAAQ,GAS3B,SAASsY,KAET,IAAMC,EAEG,MADK,IAAIjK,EAAe,CAAEa,SAAS,IACvBqJ,aAGfjK,E,8BAOJ,WAAY3K,GAAM,MAGhB,GAHgB,UAChB,cAAMA,GAEkB,oBAAbkL,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASzO,SAChC2O,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvB,EAAKJ,GACkB,oBAAbG,UACNlL,EAAKqL,WAAaH,SAASG,UAC7BD,IAASpL,EAAKoL,KAChB,EAAKJ,GAAKhL,EAAKsL,SAAWH,EAK5B,IAAM0J,EAAc7U,GAAQA,EAAK6U,YArBjB,OAsBhB,EAAKlI,eAAiBgI,IAAYE,EAtBlB,E,iCA+BlB,WAAmB,IAAX7U,EAAW,uDAAJ,GAEb,OADA,EAAcA,EAAM,CAAE+K,GAAI5M,KAAK4M,GAAIC,GAAI7M,KAAK6M,IAAM7M,KAAK6B,MAChD,IAAI8U,EAAQ3W,KAAKmH,MAAOtF,K,qBAUjC,SAAQmB,EAAMjD,GAAI,WACV6W,EAAM5W,KAAK6W,QAAQ,CACvBC,OAAQ,OACR9T,KAAMA,IAER4T,EAAIhX,GAAG,UAAWG,GAClB6W,EAAIhX,GAAG,SAAS,SAAA4C,GACd,EAAKyS,QAAQ,iBAAkBzS,Q,oBASnC,WAAS,WAGDoU,EAAM5W,KAAK6W,UACjBD,EAAIhX,GAAG,OAAQI,KAAK+W,OAAOzZ,KAAK0C,OAChC4W,EAAIhX,GAAG,SAAS,SAAA4C,GACd,EAAKyS,QAAQ,iBAAkBzS,MAEjCxC,KAAKgX,QAAUJ,M,GA1ED/I,GA8EZ8I,E,8BAOJ,WAAYxP,EAAKtF,GAAM,uBACrB,eACAD,EAAsB,EAAD,GAAOC,GAC5B,EAAKA,KAAOA,EAEZ,EAAKiV,OAASjV,EAAKiV,QAAU,MAC7B,EAAK3P,IAAMA,EACX,EAAK8P,OAAQ,IAAUpV,EAAKoV,MAC5B,EAAKjU,UAAOyC,IAAc5D,EAAKmB,KAAOnB,EAAKmB,KAAO,KAElD,EAAK5F,SAVgB,E,gCAkBvB,WAAS,WACDyE,EAAOV,EACXnB,KAAK6B,KACL,QACA,aACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,aAEFA,EAAKuL,UAAYpN,KAAK6B,KAAK+K,GAC3B/K,EAAKwL,UAAYrN,KAAK6B,KAAKgL,GAE3B,IAAMqK,EAAOlX,KAAKkX,IAAM,IAAI3K,EAAe1K,GAE3C,IAGEqV,EAAIzN,KAAKzJ,KAAK8W,OAAQ9W,KAAKmH,IAAKnH,KAAKiX,OACrC,IACE,GAAIjX,KAAK6B,KAAKsV,aAEZ,IAAK,IAAIrb,KADTob,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACzCpX,KAAK6B,KAAKsV,aAClBnX,KAAK6B,KAAKsV,aAAaxZ,eAAe7B,IACxCob,EAAIG,iBAAiBvb,EAAGkE,KAAK6B,KAAKsV,aAAarb,IAIrD,MAAOkK,IAET,GAAI,SAAWhG,KAAK8W,OAClB,IACEI,EAAIG,iBAAiB,eAAgB,4BACrC,MAAOrR,IAGX,IACEkR,EAAIG,iBAAiB,SAAU,OAC/B,MAAOrR,IAGL,oBAAqBkR,IACvBA,EAAIrD,gBAAkB7T,KAAK6B,KAAKgS,iBAG9B7T,KAAK6B,KAAKyV,iBACZJ,EAAIhO,QAAUlJ,KAAK6B,KAAKyV,gBAGtBtX,KAAKuX,UACPL,EAAIM,OAAS,WACX,EAAKC,UAEPP,EAAI7L,QAAU,WACZ,EAAK4J,QAAQiC,EAAIQ,gBAGnBR,EAAIS,mBAAqB,WACnB,IAAMT,EAAI9U,aACV,MAAQ8U,EAAIU,QAAU,OAASV,EAAIU,OACrC,EAAKH,SAIL,EAAK1V,cAAa,WAChB,EAAKkT,QAA8B,iBAAfiC,EAAIU,OAAsBV,EAAIU,OAAS,KAC1D,KAOTV,EAAI7B,KAAKrV,KAAKgD,MACd,MAAOgD,GAOP,YAHAhG,KAAK+B,cAAa,WAChB,EAAKkT,QAAQjP,KACZ,GAImB,oBAAb6R,WACT7X,KAAK8X,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAAShY,KAAK8X,OAAS9X,Q,uBASnC,WACEA,KAAKY,KAAK,WACVZ,KAAK4K,Y,oBAQP,SAAO5H,GACLhD,KAAKY,KAAK,OAAQoC,GAClBhD,KAAKiY,c,qBAQP,SAAQzV,GACNxC,KAAKY,KAAK,QAAS4B,GACnBxC,KAAK4K,SAAQ,K,qBAQf,SAAQsN,GACN,QAAI,IAAuBlY,KAAKkX,KAAO,OAASlX,KAAKkX,IAArD,CAUA,GANIlX,KAAKuX,SACPvX,KAAKkX,IAAIM,OAASxX,KAAKkX,IAAI7L,QAAUkL,EAErCvW,KAAKkX,IAAIS,mBAAqBpB,EAG5B2B,EACF,IACElY,KAAKkX,IAAIiB,QACT,MAAOnS,IAGa,oBAAb6R,iBACFlB,EAAQqB,SAAShY,KAAK8X,OAG/B9X,KAAKkX,IAAM,Q,oBAQb,WACE,IAAMlU,EAAOhD,KAAKkX,IAAIQ,aACT,OAAT1U,GACFhD,KAAK+W,OAAO/T,K,oBAShB,WACE,MAAiC,oBAAnByK,iBAAmCzN,KAAK6M,IAAM7M,KAAKwN,a,mBAQnE,WACExN,KAAK4K,c,GA7ManL,GA0NtB,GAHAkX,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,GAEK,oBAAbH,SACT,GAA2B,mBAAhBO,YACTA,YAAY,WAAYC,QACnB,GAAgC,mBAArBxY,iBAAiC,CAEjDA,iBADyB,eAAgBqB,EAAa,WAAa,SAChCmX,GAAe,GAItD,SAASA,IACP,IAAK,IAAIvc,KAAK6a,EAAQqB,SAChBrB,EAAQqB,SAASra,eAAe7B,IAClC6a,EAAQqB,SAASlc,GAAGqc,QAK1B5c,EAAOD,QAAUkR,EACjBjR,EAAOD,QAAQqb,QAAUA,G,gBChVzB,IAAQhI,EAAiB1Q,EAAQ,IAAzB0Q,aAEFuD,EACY,mBAATC,MACU,oBAATA,MACmC,6BAAzC3V,OAAOkB,UAAUuU,SAAShW,KAAKkW,MAC7BJ,EAA+C,mBAAhBC,YA8B/BsG,EAAqB,SAACtV,EAAMvE,GAChC,IAAM8Z,EAAa,IAAIC,WAKvB,OAJAD,EAAWf,OAAS,WAClB,IAAMiB,EAAUF,EAAWG,OAAOrZ,MAAM,KAAK,GAC7CZ,EAAS,IAAMga,IAEVF,EAAWI,cAAc3V,IAGlCzH,EAAOD,QA9Bc,SAAC,EAAgBkT,EAAgB/P,GAAa,IANpDiB,EAMSF,EAA2C,EAA3CA,KAAMwD,EAAqC,EAArCA,KAC5B,OAAIkP,GAAkBlP,aAAgBmP,KAChC3D,EACK/P,EAASuE,GAETsV,EAAmBtV,EAAMvE,GAGlCsT,IACC/O,aAAgBgP,cAfNtS,EAe4BsD,EAdJ,mBAAvBgP,YAAYM,OACtBN,YAAYM,OAAO5S,GACnBA,GAAOA,EAAI6S,kBAAkBP,cAc3BxD,EACK/P,EAASuE,aAAgBgP,YAAchP,EAAOA,EAAKuP,QAEnD+F,EAAmB,IAAInG,KAAK,CAACnP,IAAQvE,GAIzCA,EAASkQ,EAAanP,IAASwD,GAAQ,O,gBCjChD,IAII4V,EAJJ,EAA+C3a,EAAQ,IAA/C2Q,EAAR,EAAQA,qBAAsBC,EAA9B,EAA8BA,aAEuB,mBAAhBmD,cAInC4G,EAAgB3a,EAAQ,KAG1B,IA4BM4a,EAAqB,SAAC7V,EAAM5D,GAChC,GAAIwZ,EAAe,CACjB,IAAMpJ,EAAUoJ,EAAcvV,OAAOL,GACrC,OAAO8V,EAAUtJ,EAASpQ,GAE1B,MAAO,CAAEiG,QAAQ,EAAMrC,SAIrB8V,EAAY,SAAC9V,EAAM5D,GACvB,OAAQA,GACN,IAAK,OACH,OAAO4D,aAAgBgP,YAAc,IAAIG,KAAK,CAACnP,IAASA,EAC1D,IAAK,cACL,QACE,OAAOA,IAIbzH,EAAOD,QA/Cc,SAAC0D,EAAeI,GACnC,GAA6B,iBAAlBJ,EACT,MAAO,CACLQ,KAAM,UACNwD,KAAM8V,EAAU9Z,EAAeI,IAGnC,IAAMI,EAAOR,EAAcwG,OAAO,GAClC,MAAa,MAAThG,EACK,CACLA,KAAM,UACNwD,KAAM6V,EAAmB7Z,EAAc4G,UAAU,GAAIxG,IAGtCwP,EAAqBpP,GAIjCR,EAAcN,OAAS,EAC1B,CACEc,KAAMoP,EAAqBpP,GAC3BwD,KAAMhE,EAAc4G,UAAU,IAEhC,CACEpG,KAAMoP,EAAqBpP,IARxBqP,I,eClBX,SAAUkK,GACR,aAEAzd,EAAQ4H,OAAS,SAAS8V,GACxB,IACAld,EADImd,EAAQ,IAAIC,WAAWF,GACxBlY,EAAMmY,EAAMva,OAAQ2G,EAAS,GAEhC,IAAKvJ,EAAI,EAAGA,EAAIgF,EAAKhF,GAAG,EACtBuJ,GAAU0T,EAAME,EAAMnd,IAAM,GAC5BuJ,GAAU0T,GAAmB,EAAXE,EAAMnd,KAAW,EAAMmd,EAAMnd,EAAI,IAAM,GACzDuJ,GAAU0T,GAAuB,GAAfE,EAAMnd,EAAI,KAAY,EAAMmd,EAAMnd,EAAI,IAAM,GAC9DuJ,GAAU0T,EAAqB,GAAfE,EAAMnd,EAAI,IAS5B,OANKgF,EAAM,GAAO,EAChBuE,EAASA,EAAOO,UAAU,EAAGP,EAAO3G,OAAS,GAAK,IACzCoC,EAAM,GAAM,IACrBuE,EAASA,EAAOO,UAAU,EAAGP,EAAO3G,OAAS,GAAK,MAG7C2G,GAGT/J,EAAQ+H,OAAU,SAASgC,GACzB,IACqBvJ,EACrBqd,EAAUC,EAAUC,EAAUC,EAF1BC,EAA+B,IAAhBlU,EAAO3G,OAC1BoC,EAAMuE,EAAO3G,OAAWd,EAAI,EAGM,MAA9ByH,EAAOA,EAAO3G,OAAS,KACzB6a,IACkC,MAA9BlU,EAAOA,EAAO3G,OAAS,IACzB6a,KAIJ,IAAIP,EAAc,IAAIhH,YAAYuH,GAClCN,EAAQ,IAAIC,WAAWF,GAEvB,IAAKld,EAAI,EAAGA,EAAIgF,EAAKhF,GAAG,EACtBqd,EAAWJ,EAAM/R,QAAQ3B,EAAOvJ,IAChCsd,EAAWL,EAAM/R,QAAQ3B,EAAOvJ,EAAE,IAClCud,EAAWN,EAAM/R,QAAQ3B,EAAOvJ,EAAE,IAClCwd,EAAWP,EAAM/R,QAAQ3B,EAAOvJ,EAAE,IAElCmd,EAAMrb,KAAQub,GAAY,EAAMC,GAAY,EAC5CH,EAAMrb,MAAoB,GAAXwb,IAAkB,EAAMC,GAAY,EACnDJ,EAAMrb,MAAoB,EAAXyb,IAAiB,EAAiB,GAAXC,EAGxC,OAAON,GAjDX,CAmDG,qE,knDC1DH,IAUItY,EAVEmN,EAAU5P,EAAQ,IAClBiD,EAAajD,EAAQ,GAErBub,EAAW,MACXC,EAAkB,OAQlBC,E,sQAOJ,WAAY7X,GAAM,a,4FAAA,UAChB,cAAMA,IAEDM,MAAQ,EAAKA,OAAS,GAItBzB,IAEHA,EAAYQ,EAAWyY,OAASzY,EAAWyY,QAAU,IAIvD,EAAK7B,MAAQpX,EAAUhC,OAGvBgC,EAAUnB,KAAK,EAAKwX,OAAOzZ,KAAZ,OAGf,EAAK6E,MAAMmU,EAAI,EAAKwB,MAnBJ,E,yCAyBlB,WACE,OAAO,I,qBAQT,WACM9X,KAAK4Z,SAEP5Z,KAAK4Z,OAAOvO,QAAU,aACtBrL,KAAK4Z,OAAOC,WAAWC,YAAY9Z,KAAK4Z,QACxC5Z,KAAK4Z,OAAS,MAGZ5Z,KAAK+Z,OACP/Z,KAAK+Z,KAAKF,WAAWC,YAAY9Z,KAAK+Z,MACtC/Z,KAAK+Z,KAAO,KACZ/Z,KAAKga,OAAS,MAGhB,8C,oBAQF,WAAS,WACDJ,EAAS/B,SAASoC,cAAc,UAElCja,KAAK4Z,SACP5Z,KAAK4Z,OAAOC,WAAWC,YAAY9Z,KAAK4Z,QACxC5Z,KAAK4Z,OAAS,MAGhBA,EAAO3C,OAAQ,EACf2C,EAAO9S,IAAM9G,KAAKmH,MAClByS,EAAOvO,QAAU,SAAArF,GACf,EAAKiP,QAAQ,mBAAoBjP,IAGnC,IAAMkU,EAAWrC,SAASsC,qBAAqB,UAAU,GACrDD,EACFA,EAASL,WAAWO,aAAaR,EAAQM,IAExCrC,SAASwC,MAAQxC,SAASyC,MAAMC,YAAYX,GAE/C5Z,KAAK4Z,OAASA,EAGZ,oBAAuBY,WAAa,SAASlH,KAAKkH,UAAUC,YAG5Dza,KAAK+B,cAAa,WAChB,IAAMiY,EAASnC,SAASoC,cAAc,UACtCpC,SAASyC,KAAKC,YAAYP,GAC1BnC,SAASyC,KAAKR,YAAYE,KACzB,O,qBAWP,SAAQhX,EAAMjD,GAAI,IACZia,EADY,OAGhB,IAAKha,KAAK+Z,KAAM,CACd,IAAMA,EAAOlC,SAASoC,cAAc,QAC9BS,EAAO7C,SAASoC,cAAc,YAC9BxV,EAAMzE,KAAK2a,SAAW,cAAgB3a,KAAK8X,MAEjDiC,EAAKa,UAAY,WACjBb,EAAKc,MAAMC,SAAW,WACtBf,EAAKc,MAAME,IAAM,UACjBhB,EAAKc,MAAMG,KAAO,UAClBjB,EAAKkB,OAASxW,EACdsV,EAAKjD,OAAS,OACdiD,EAAKmB,aAAa,iBAAkB,SACpCR,EAAKre,KAAO,IACZ0d,EAAKQ,YAAYG,GACjB7C,SAASyC,KAAKC,YAAYR,GAE1B/Z,KAAK+Z,KAAOA,EACZ/Z,KAAK0a,KAAOA,EAKd,SAASS,IACPC,IACArb,IAJFC,KAAK+Z,KAAKsB,OAASrb,KAAKmH,MAOxB,IAAMiU,EAAa,WACjB,GAAI,EAAKpB,OACP,IACE,EAAKD,KAAKD,YAAY,EAAKE,QAC3B,MAAOhU,GACP,EAAKiP,QAAQ,qCAAsCjP,GAIvD,IAEE,IAAMsV,EAAO,oCAAsC,EAAKX,SAAW,KACnEX,EAASnC,SAASoC,cAAcqB,GAChC,MAAOtV,IACPgU,EAASnC,SAASoC,cAAc,WACzB5d,KAAO,EAAKse,SACnBX,EAAOlT,IAAM,eAGfkT,EAAOvV,GAAK,EAAKkW,SAEjB,EAAKZ,KAAKQ,YAAYP,GACtB,EAAKA,OAASA,GAGhBoB,IAIApY,EAAOA,EAAKiE,QAAQwS,EAAiB,QACrCzZ,KAAK0a,KAAK3d,MAAQiG,EAAKiE,QAAQuS,EAAU,OAEzC,IACExZ,KAAK+Z,KAAKwB,SACV,MAAOvV,IAELhG,KAAKga,OAAO5B,YACdpY,KAAKga,OAAOrC,mBAAqB,WACA,aAA3B,EAAKqC,OAAO5X,YACd+Y,KAIJnb,KAAKga,OAAOxC,OAAS2D,O,8BAjLAtN,GAsL3BtS,EAAOD,QAAUoe,G,w0CClMjB,IAAMxX,EAAYjE,EAAQ,GACpBgE,EAAShE,EAAQ,GACjB0P,EAAU1P,EAAQ,GAClB2P,EAAQ3P,EAAQ,IACdkD,EAASlD,EAAQ,GAAjBkD,KACR,EAKIlD,EAAQ,IAJVud,EADF,EACEA,UACAC,EAFF,EAEEA,sBACAC,EAHF,EAGEA,kBACAC,EAJF,EAIEA,SAOIC,EACiB,oBAAdpB,WACsB,iBAAtBA,UAAUqB,SACmB,gBAApCrB,UAAUqB,QAAQC,cAEdC,E,sQAOJ,WAAYla,GAAM,a,4FAAA,UAChB,cAAMA,IAED2M,gBAAkB3M,EAAK6U,YAHZ,E,+BAWlB,WACE,MAAO,c,oBAQT,WACE,GAAK1W,KAAKgc,QAAV,CAKA,IAAM7U,EAAMnH,KAAKmH,MACX8U,EAAYjc,KAAK6B,KAAKoa,UAGtBpa,EAAO+Z,EACT,GACAza,EACEnB,KAAK6B,KACL,QACA,oBACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,eACA,kBACA,SACA,aACA,SACA,uBAGF7B,KAAK6B,KAAKsV,eACZtV,EAAKqa,QAAUlc,KAAK6B,KAAKsV,cAG3B,IACEnX,KAAKmc,GACHV,IAA0BG,EACtBK,EACE,IAAIT,EAAUrU,EAAK8U,GACnB,IAAIT,EAAUrU,GAChB,IAAIqU,EAAUrU,EAAK8U,EAAWpa,GACpC,MAAOW,GACP,OAAOxC,KAAKY,KAAK,QAAS4B,GAG5BxC,KAAKmc,GAAG/c,WAAaY,KAAKqC,OAAOjD,YAAcsc,EAE/C1b,KAAKoc,uB,+BAQP,WAAoB,WAClBpc,KAAKmc,GAAGzR,OAAS,WACX,EAAK7I,KAAKoJ,WACZ,EAAKkR,GAAGE,QAAQnR,QAElB,EAAKiD,UAEPnO,KAAKmc,GAAG7Q,QAAUtL,KAAK6C,QAAQvF,KAAK0C,MACpCA,KAAKmc,GAAGG,UAAY,SAAAzL,GAAE,OAAI,EAAKkG,OAAOlG,EAAG7N,OACzChD,KAAKmc,GAAG9Q,QAAU,SAAArF,GAAC,OAAI,EAAKiP,QAAQ,kBAAmBjP,M,mBASzD,SAAMxH,GAAS,WACbwB,KAAK+C,UAAW,EAIhB,IALa,eAKJjH,GACP,IAAMiD,EAASP,EAAQ1C,GACjBygB,EAAazgB,IAAM0C,EAAQE,OAAS,EAE1CuD,EAAOjE,aAAae,EAAQ,EAAKyP,gBAAgB,SAAAxL,GAE/C,IAAMnB,EAAO,GACR4Z,IACC1c,EAAO8M,UACThK,EAAKiP,SAAW/R,EAAO8M,QAAQiF,UAG7B,EAAKjP,KAAKoS,oBAEV,iBAAoBjR,EAAOwZ,OAAOC,WAAWzZ,GAAQA,EAAKtE,QAClD,EAAKmD,KAAKoS,kBAAkBC,YACpCrS,EAAKiP,UAAW,IAQtB,IACM2K,EAEF,EAAKU,GAAG9G,KAAKrS,GAEb,EAAKmZ,GAAG9G,KAAKrS,EAAMnB,GAErB,MAAOmE,IAKLuW,GAGFZ,GAAS,WACP,EAAK5Y,UAAW,EAChB,EAAKnC,KAAK,WACT,EAAKmB,kBA1CLjG,EAAI,EAAGA,EAAI0C,EAAQE,OAAQ5C,IAAK,EAAhCA,K,qBAqDX,WACEoG,EAAUxE,UAAUmF,QAAQ5G,KAAK+D,Q,qBAQnC,gBACyB,IAAZA,KAAKmc,KACdnc,KAAKmc,GAAGnR,QACRhL,KAAKmc,GAAK,Q,iBASd,WACE,IAAIha,EAAQnC,KAAKmC,OAAS,GACpBkM,EAASrO,KAAK6B,KAAKsL,OAAS,MAAQ,KACtCF,EAAO,GA6BX,OAzBEjN,KAAK6B,KAAKoL,OACR,QAAUoB,GAAqC,MAA3B9I,OAAOvF,KAAK6B,KAAKoL,OACpC,OAASoB,GAAqC,KAA3B9I,OAAOvF,KAAK6B,KAAKoL,SAEvCA,EAAO,IAAMjN,KAAK6B,KAAKoL,MAIrBjN,KAAK6B,KAAKyM,oBACZnM,EAAMnC,KAAK6B,KAAK0M,gBAAkBX,KAI/B5N,KAAKwO,iBACRrM,EAAMuM,IAAM,IAGdvM,EAAQwL,EAAQzK,OAAOf,IAGbzD,SACRyD,EAAQ,IAAMA,GAKdkM,EACA,QAHgD,IAArCrO,KAAK6B,KAAKqL,SAASlG,QAAQ,KAI9B,IAAMhH,KAAK6B,KAAKqL,SAAW,IAAMlN,KAAK6B,KAAKqL,UACnDD,EACAjN,KAAK6B,KAAK4F,KACVtF,I,mBAUJ,WACE,SACIqZ,GACA,iBAAkBA,GAAaxb,KAAK3D,OAAS0f,EAAGre,UAAUrB,W,8BAvOjD6F,GA4OjB3G,EAAOD,QAAUygB,G,gBCjQjB,IAAM7a,EAAajD,EAAQ,GACrB0d,EAEiB,mBAAZe,SAAqD,mBAApBA,QAAQC,QAEzC,SAAAlc,GAAE,OAAIic,QAAQC,UAAUC,KAAKnc,IAE7B,SAACA,EAAIsB,GAAL,OAAsBA,EAAatB,EAAI,IAIlDlF,EAAOD,QAAU,CACfkgB,UAAWta,EAAWsa,WAAata,EAAW2b,aAC9CpB,uBAAuB,EACvBC,kBAAmB,cACnBC,a,kQCdFnf,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQqL,kBAAoBrL,EAAQuJ,uBAAoB,EACxD,IAAMd,EAAc9F,EAAQ,IAgB5B3C,EAAQuJ,kBARR,SAA2B9F,GACvB,IAAMgG,EAAU,GACV+X,EAAa/d,EAAOiE,KACpB8B,EAAO/F,EAGb,OAFA+F,EAAK9B,KAKT,SAAS+Z,EAAmB/Z,EAAM+B,GAC9B,IAAK/B,EACD,OAAOA,EACX,GAAIe,EAAYqB,SAASpC,GAAO,CAC5B,IAAMga,EAAc,CAAEC,cAAc,EAAM/N,IAAKnK,EAAQrG,QAEvD,OADAqG,EAAQxF,KAAKyD,GACNga,EAEN,GAAIpe,MAAM4H,QAAQxD,GAAO,CAE1B,IADA,IAAMka,EAAU,IAAIte,MAAMoE,EAAKtE,QACtB5C,EAAI,EAAGA,EAAIkH,EAAKtE,OAAQ5C,IAC7BohB,EAAQphB,GAAKihB,EAAmB/Z,EAAKlH,GAAIiJ,GAE7C,OAAOmY,EAEN,GAAoB,WAAhB,EAAOla,MAAuBA,aAAgBuM,MAAO,CAC1D,IAAM2N,EAAU,GAChB,IAAK,IAAM7f,KAAO2F,EACVA,EAAKrF,eAAeN,KACpB6f,EAAQ7f,GAAO0f,EAAmB/Z,EAAK3F,GAAM0H,IAGrD,OAAOmY,EAEX,OAAOla,EA7BK+Z,CAAmBD,EAAY/X,GAC3CD,EAAKP,YAAcQ,EAAQrG,OACpB,CAAEK,OAAQ+F,EAAMC,QAASA,IA0CpCzJ,EAAQqL,kBALR,SAA2B5H,EAAQgG,GAG/B,OAFAhG,EAAOiE,KAKX,SAASma,EAAmBna,EAAM+B,GAC9B,IAAK/B,EACD,OAAOA,EACX,GAAIA,GAAQA,EAAKia,aACb,OAAOlY,EAAQ/B,EAAKkM,KAEnB,GAAItQ,MAAM4H,QAAQxD,GACnB,IAAK,IAAIlH,EAAI,EAAGA,EAAIkH,EAAKtE,OAAQ5C,IAC7BkH,EAAKlH,GAAKqhB,EAAmBna,EAAKlH,GAAIiJ,QAGzC,GAAoB,WAAhB,EAAO/B,GACZ,IAAK,IAAM3F,KAAO2F,EACVA,EAAKrF,eAAeN,KACpB2F,EAAK3F,GAAO8f,EAAmBna,EAAK3F,GAAM0H,IAItD,OAAO/B,EAvBOma,CAAmBpe,EAAOiE,KAAM+B,GAC9ChG,EAAOwF,iBAAckB,EACd1G,I,cCtCX,SAASqJ,EAAQvG,GACfA,EAAOA,GAAQ,GACf7B,KAAKod,GAAKvb,EAAKkH,KAAO,IACtB/I,KAAKgJ,IAAMnH,EAAKmH,KAAO,IACvBhJ,KAAKqd,OAASxb,EAAKwb,QAAU,EAC7Brd,KAAKiJ,OAASpH,EAAKoH,OAAS,GAAKpH,EAAKoH,QAAU,EAAIpH,EAAKoH,OAAS,EAClEjJ,KAAKqK,SAAW,EApBlB9O,EAAOD,QAAU8M,EA8BjBA,EAAQ1K,UAAUyO,SAAW,WAC3B,IAAIiR,EAAKpd,KAAKod,GAAKhO,KAAKkO,IAAItd,KAAKqd,OAAQrd,KAAKqK,YAC9C,GAAIrK,KAAKiJ,OAAQ,CACf,IAAIsU,EAAQnO,KAAKoO,SACbC,EAAYrO,KAAKC,MAAMkO,EAAOvd,KAAKiJ,OAASmU,GAChDA,EAAoC,IAAN,EAAxBhO,KAAKC,MAAa,GAAPkO,IAAwBH,EAAKK,EAAYL,EAAKK,EAEjE,OAAgC,EAAzBrO,KAAKrG,IAAIqU,EAAIpd,KAAKgJ,MAS3BZ,EAAQ1K,UAAUsO,MAAQ,WACxBhM,KAAKqK,SAAW,GASlBjC,EAAQ1K,UAAUoM,OAAS,SAASf,GAClC/I,KAAKod,GAAKrU,GASZX,EAAQ1K,UAAUwM,OAAS,SAASlB,GAClChJ,KAAKgJ,IAAMA,GASbZ,EAAQ1K,UAAUsM,UAAY,SAASf,GACrCjJ,KAAKiJ,OAASA", "file": "socket.io.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(self, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 18);\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "const globalThis = require(\"./globalThis\");\n\nmodule.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = clearTimeout;\n\nmodule.exports.installTimerFunctions = (obj, opts) => {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = clearTimeout.bind(globalThis);\n  }\n};\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst { installTimerFunctions } = require(\"./util\");\n\n\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n    installTimerFunctions(this, opts);\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      // this might happen if the transport was silently closed in the beforeunload event handler\n\n\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Decoder = exports.Encoder = exports.PacketType = exports.protocol = void 0;\nconst Emitter = require(\"component-emitter\");\nconst binary_1 = require(\"./binary\");\nconst is_binary_1 = require(\"./is-binary\");\n\n\n/**\n * Protocol version.\n *\n * @public\n */\nexports.protocol = 5;\nvar PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType = exports.PacketType || (exports.PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nclass Encoder {\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n\n\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (is_binary_1.hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data);\n        }\n\n\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = binary_1.deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\nexports.Encoder = Encoder;\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nclass Decoder extends Emitter {\n    constructor() {\n        super();\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emit(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emit(\"decoded\", packet);\n            }\n        }\n        else if (is_binary_1.isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emit(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n\n\n        return p;\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\nexports.Decoder = Decoder;\nfunction tryParse(str) {\n    try {\n        return JSON.parse(str);\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = binary_1.reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst util_1 = require(\"engine.io-client/lib/util\");\nconst socket_1 = require(\"./socket\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\nconst typed_events_1 = require(\"./typed-events\");\n\n\nclass Manager extends typed_events_1.StrictEventEmitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0, util_1.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n\n\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n\n\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0, on_1.on)(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = (0, on_1.on)(socket, \"error\", (err) => {\n\n\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n\n\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n\n\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0, on_1.on)(socket, \"ping\", this.onping.bind(this)), (0, on_1.on)(socket, \"data\", this.ondata.bind(this)), (0, on_1.on)(socket, \"error\", this.onerror.bind(this)), (0, on_1.on)(socket, \"close\", this.onclose.bind(this)), (0, on_1.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        this.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n\n\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n\n\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n\n\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n\n\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n\n\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n\n\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n\n\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n\n\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n\n\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\n\n\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n\n    const pause = () => {\n\n\n      this.readyState = \"paused\";\n      onPause();\n    };\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n\n\n        total++;\n        this.once(\"pollComplete\", function() {\n\n\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n\n\n        total++;\n        this.once(\"drain\", function() {\n\n\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n\n\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n\n\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n\n\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const close = () => {\n\n\n      this.write([{ type: \"close\" }]);\n    };\n\n    if (\"open\" === this.readyState) {\n\n\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n\n\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst typed_events_1 = require(\"./typed-events\");\n\n\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends typed_events_1.StrictEventEmitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.connected = false;\n        this.disconnected = true;\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            (0, on_1.on)(io, \"open\", this.onopen.bind(this)),\n            (0, on_1.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0, on_1.on)(io, \"error\", this.onerror.bind(this)),\n            (0, on_1.on)(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n\n\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n\n\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n\n\n        if (null != packet.id) {\n\n\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n\n\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n\n\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n\n\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n\n\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n\n\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n\n\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hasBinary = exports.isBinary = void 0;\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nfunction isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexports.isBinary = isBinary;\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\nexports.hasBinary = hasBinary;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.StrictEventEmitter = void 0;\nconst Emitter = require(\"component-emitter\");\n/**\n * Strictly typed version of an `EventEmitter`. A `TypedEventEmitter` takes type\n * parameters for mappings of event names to event data types, and strictly\n * types method calls to the `EventEmitter` according to these event maps.\n *\n * @typeParam ListenEvents - `EventsMap` of user-defined events that can be\n * listened to with `on` or `once`\n * @typeParam EmitEvents - `EventsMap` of user-defined events that can be\n * emitted with `emit`\n * @typeParam ReservedEvents - `EventsMap` of reserved events, that can be\n * emitted by socket.io with `emitReserved`, and can be listened to with\n * `listen`.\n */\nclass StrictEventEmitter extends Emitter {\n    /**\n     * Adds the `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    on(ev, listener) {\n        super.on(ev, listener);\n        return this;\n    }\n    /**\n     * Adds a one-time `listener` function as an event listener for `ev`.\n     *\n     * @param ev Name of the event\n     * @param listener Callback function\n     */\n    once(ev, listener) {\n        super.once(ev, listener);\n        return this;\n    }\n    /**\n     * Emits an event.\n     *\n     * @param ev Name of the event\n     * @param args Values to send to listeners of this event\n     */\n    emit(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Emits a reserved event.\n     *\n     * This method is `protected`, so that only a class extending\n     * `StrictEventEmitter` can emit its own reserved events.\n     *\n     * @param ev Reserved event name\n     * @param args Arguments to emit along with the event\n     */\n    emitReserved(ev, ...args) {\n        super.emit(ev, ...args);\n        return this;\n    }\n    /**\n     * Returns the listeners listening to an event.\n     *\n     * @param event Event name\n     * @returns Array of listeners subscribed to `event`\n     */\n    listeners(event) {\n        return super.listeners(event);\n    }\n}\nexports.StrictEventEmitter = StrictEventEmitter;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.io = exports.Socket = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\n\n\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0, url_1.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n\n\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n\n\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\nvar socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\nexports.default = lookup;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\n\n\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n\n\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\n\n\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\nconst { installTimerFunctions } = require(\"./util\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    installTimerFunctions(this, opts);\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {},\n        closeOnBeforeunload: true\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      if (this.opts.closeOnBeforeunload) {\n        // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n        // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n        // closed/reloaded)\n        addEventListener(\n          \"beforeunload\",\n          () => {\n            if (this.transport) {\n              // silently close the transport\n              this.transport.removeAllListeners();\n              this.transport.close();\n            }\n          },\n          false\n        );\n      }\n      if (this.hostname !== \"localhost\") {\n        this.offlineEventListener = () => {\n          this.onClose(\"transport close\");\n        };\n        addEventListener(\"offline\", this.offlineEventListener, false);\n      }\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n\n\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n\n\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      this.setTimeoutFn(() => {\n        this.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n\n\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n\n\n\n    if (this.transport) {\n\n\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", this.onDrain.bind(this))\n      .on(\"packet\", this.onPacket.bind(this))\n      .on(\"error\", this.onError.bind(this))\n      .on(\"close\", () => {\n        this.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n\n\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n\n    Socket.priorWebsocketSuccess = false;\n\n    const onTransportOpen = () => {\n      if (failed) return;\n\n\n\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n\n\n          this.upgrading = true;\n          this.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n\n\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n\n\n\n            cleanup();\n\n            this.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            this.emit(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n\n\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          this.emit(\"upgradeError\", err);\n        }\n      });\n    };\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n\n\n\n      this.emit(\"upgradeError\", error);\n    };\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n\n\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.removeListener(\"close\", onclose);\n      this.removeListener(\"upgrading\", onupgrade);\n    };\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n\n\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n\n\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"ping\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n\n\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    this.clearTimeoutFn(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = this.setTimeoutFn(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n    if (this.opts.autoUnref) {\n      this.pingTimeoutTimer.unref();\n    }\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n\n\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const close = () => {\n      this.onClose(\"forced close\");\n\n\n      this.transport.close();\n    };\n\n    const cleanupAndClose = () => {\n      this.removeListener(\"upgrade\", cleanupAndClose);\n      this.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    };\n\n    const waitForUpgrade = () => {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      this.once(\"upgrade\", cleanupAndClose);\n      this.once(\"upgradeError\", cleanupAndClose);\n    };\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", () => {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n\n\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      // clear timers\n      this.clearTimeoutFn(this.pingIntervalTimer);\n      this.clearTimeoutFn(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      if (typeof removeEventListener === \"function\") {\n        removeEventListener(\"offline\", this.offlineEventListener, false);\n      }\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      this.writeBuffer = [];\n      this.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick, installTimerFunctions } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\n\n\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", err => {\n      this.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n\n\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", err => {\n      this.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\",\n      \"autoUnref\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n\n    try {\n\n\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = () => {\n          this.onLoad();\n        };\n        xhr.onerror = () => {\n          this.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = () => {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            this.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            this.setTimeoutFn(() => {\n              this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n\n\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    callbacks.push(this.onData.bind(this));\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = e => {\n      this.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      this.setTimeoutFn(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    const initIframe = () => {\n      if (this.iframe) {\n        try {\n          this.form.removeChild(this.iframe);\n        } catch (e) {\n          this.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + this.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = this.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = this.iframeId;\n\n      this.form.appendChild(iframe);\n      this.iframe = iframe;\n    };\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = () => {\n        if (this.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType,\n  nextTick\n} = require(\"./websocket-constructor\");\n\n\n\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n      this.onOpen();\n    };\n    this.ws.onclose = this.onClose.bind(this);\n    this.ws.onmessage = ev => this.onData(ev.data);\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n\n      parser.encodePacket(packet, this.supportsBinary, data => {\n        // always create a new object (GH-437)\n        const opts = {};\n        if (!usingBrowserWebSocket) {\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (this.opts.perMessageDeflate) {\n            const len =\n              \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            this.ws.send(data);\n          } else {\n            this.ws.send(data, opts);\n          }\n        } catch (e) {\n\n\n        }\n\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emit(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "const globalThis = require(\"../globalThis\");\nconst nextTick = (() => {\n  const isPromiseAvailable =\n    typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n  if (isPromiseAvailable) {\n    return cb => Promise.resolve().then(cb);\n  } else {\n    return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n  }\n})();\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\",\n  nextTick\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reconstructPacket = exports.deconstructPacket = void 0;\nconst is_binary_1 = require(\"./is-binary\");\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nfunction deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nexports.deconstructPacket = deconstructPacket;\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (is_binary_1.isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nfunction reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nexports.reconstructPacket = reconstructPacket;\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n"], "sourceRoot": ""}