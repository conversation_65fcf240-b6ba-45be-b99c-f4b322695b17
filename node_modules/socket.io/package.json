{"_from": "socket.io@^4.1.2", "_id": "socket.io@4.2.0", "_inBundle": false, "_integrity": "sha512-sjlGfMmnaWvTRVxGRGWyhd9ctpg4APxWAxu85O/SxekkxHhfxmePWZbaYCkeX5QQX0z1YEnKOlNt6w82E4Nzug==", "_location": "/socket.io", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "socket.io@^4.1.2", "name": "socket.io", "escapedName": "socket.io", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/socket.io/-/socket.io-4.2.0.tgz", "_shasum": "9e1c09d3ea647e24963a2e7ba8ea5c847778e2ed", "_spec": "socket.io@^4.1.2", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket", "bugs": {"url": "https://github.com/socketio/socket.io/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"@types/cookie": "^0.4.1", "@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "~2.0.0", "debug": "~4.3.2", "engine.io": "~5.2.0", "socket.io-adapter": "~2.3.2", "socket.io-parser": "~4.0.4"}, "deprecated": false, "description": "node.js realtime framework server", "devDependencies": {"@types/mocha": "^9.0.0", "expect.js": "0.3.1", "mocha": "^3.5.3", "nyc": "^15.1.0", "prettier": "^2.3.2", "rimraf": "^3.0.2", "socket.io-client": "4.2.0", "socket.io-client-v2": "npm:socket.io-client@^2.4.0", "superagent": "^6.1.0", "supertest": "^6.1.6", "ts-node": "^10.2.1", "tsd": "^0.17.0", "typescript": "^4.4.2"}, "directories": {"doc": "docs/", "example": "example/", "lib": "lib/", "test": "test/"}, "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./dist/index.js"}, "files": ["dist/", "client-dist/", "wrapper.mjs", "!**/*.tsbuildinfo"], "homepage": "https://github.com/socketio/socket.io#readme", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "socket", "io"], "license": "MIT", "main": "./dist/index.js", "name": "socket.io", "repository": {"type": "git", "url": "git://github.com/socketio/socket.io.git"}, "scripts": {"compile": "rimraf ./dist && tsc", "format:check": "prettier --check \"lib/**/*.ts\" \"test/**/*.ts\"", "format:fix": "prettier --write \"lib/**/*.ts\" \"test/**/*.ts\"", "prepack": "npm run compile", "test": "npm run format:check && npm run compile && npm run test:types && npm run test:unit", "test:types": "tsd", "test:unit": "nyc mocha --require ts-node/register --reporter spec --slow 200 --bail --timeout 10000 test/socket.io.ts"}, "tsd": {"directory": "test"}, "type": "commonjs", "types": "./dist/index.d.ts", "version": "4.2.0"}