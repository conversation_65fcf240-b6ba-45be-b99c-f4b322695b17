{"_from": "component-emitter@~1.3.0", "_id": "component-emitter@1.3.0", "_inBundle": false, "_integrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==", "_location": "/component-emitter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "component-emitter@~1.3.0", "name": "component-emitter", "escapedName": "component-emitter", "rawSpec": "~1.3.0", "saveSpec": null, "fetchSpec": "~1.3.0"}, "_requiredBy": ["/engine.io-client", "/socket.io-client", "/socket.io-parser"], "_resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz", "_shasum": "16e4070fba8ae29b679f2215853ee181ab2eabc0", "_spec": "component-emitter@~1.3.0", "_where": "E:\\OpenServer\\domains\\digitalmarketing.com\\Code\\Wefinex\\wefinex-socket\\node_modules\\socket.io-parser", "bugs": {"url": "https://github.com/component/emitter/issues"}, "bundleDependencies": false, "component": {"scripts": {"emitter/index.js": "index.js"}}, "deprecated": false, "description": "Event emitter", "devDependencies": {"mocha": "*", "should": "*"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/component/emitter#readme", "license": "MIT", "main": "index.js", "name": "component-emitter", "repository": {"type": "git", "url": "git+https://github.com/component/emitter.git"}, "scripts": {"test": "make test"}, "version": "1.3.0"}